// 动态加载JS的函数
function loadScript(src, callback) {
  var script = document.createElement("script");
  var head = document.getElementsByTagName("head")[0];
  script.type = "text/javascript";
  script.charset = "UTF-8";
  script.src = src;
  if (script.addEventListener) {
    script.addEventListener(
      "load",
      function () {
        callback();
      },
      false
    );
  } else if (script.attachEvent) {
    script.attachEvent("onreadystatechange", function () {
      var target = window.event.srcElement;
      if (target.readyState == "loaded") {
        callback();
      }
    });
  }
  head.appendChild(script);
}

// loadScript('http://************:7105/Grid/SDKService/jssdk/js/mapbox-gl.js?key=********************************', function () {
// 	loadScript('http://**************:8080/Grid/SDKService/jssdk/js/mapbox-gl.js?key=********************************', function () {

// });

loadScript("./mapjs/mapbox-gl.js", function () {});


var tilesURL = [
	// "http://**************:8080/grid/VectorMapAbility/HMapServer/v2.0/wmts?LAYER=ChinaMap2020Q1&SERVICE=WMTS&REQUEST=GetTile&TILEMATRIX={z}&TILECOL={x}&TILEROW={y}&Version=1.0.0&FORMAT=image/png&style=&tilematrixset=EPSG:3857_base&key=********************************"
  "http://**************:8080/grid/VectorMapAbility/HMapServer/v2.0/wmts?LAYER=ChinaMap2020Q1&SERVICE=WMTS&REQUEST=GetTile&TILEMATRIX={z}&TILECOL={x}&TILEROW={y}&Version=1.0.0&FORMAT=image/png&style=&tilematrixset=EPSG:3857_base&key=********************************"
];
