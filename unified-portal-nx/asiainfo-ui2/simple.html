<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue3 简化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        #loader-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .load_title {
            margin-top: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="loader-wrapper">
        <div>
            <div class="loader"></div>
            <div class="load_title">Vue3应用加载中，请稍后...</div>
        </div>
    </div>
    
    <div id="app">
        <!-- Vue应用将在这里渲染 -->
    </div>
    
    <script type="module" src="/src/main-simple.js"></script>
    
    <script>
        // 监听Vue应用加载
        let loadTimeout = setTimeout(() => {
            document.getElementById('loader-wrapper').innerHTML = `
                <div style="text-align: center; color: red;">
                    <h2>❌ Vue3应用加载超时</h2>
                    <p>请检查浏览器控制台的错误信息</p>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }, 10000);
        
        // 检测Vue应用是否成功挂载
        function checkVueApp() {
            const app = document.getElementById('app');
            if (app && app.innerHTML.trim() !== '') {
                // Vue应用已挂载，隐藏加载器
                document.getElementById('loader-wrapper').style.display = 'none';
                clearTimeout(loadTimeout);
                console.log('✅ Vue3应用加载成功！');
            }
        }
        
        // 每100ms检查一次
        const checkInterval = setInterval(() => {
            checkVueApp();
            if (document.getElementById('loader-wrapper').style.display === 'none') {
                clearInterval(checkInterval);
            }
        }, 100);
        
        // 监听错误
        window.addEventListener('error', (e) => {
            console.error('页面错误:', e.error);
            document.getElementById('loader-wrapper').innerHTML = `
                <div style="text-align: center; color: red;">
                    <h2>❌ 页面加载错误</h2>
                    <p>${e.message}</p>
                    <p>文件: ${e.filename}:${e.lineno}</p>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            console.error('未处理的Promise拒绝:', e.reason);
        });
    </script>
</body>
</html>
