# Vue2 到 Vue3 升级指南

## 升级概述

本项目已从 Vue 2.6.12 + Element UI 2.15.6 升级到 Vue 3.3.4 + Element Plus 2.4.0。

## 主要变更

### 1. 核心依赖升级
- **Vue**: 2.6.12 → 3.3.4
- **Vue Router**: 3.0.7 → 4.2.4
- **Vuex**: 3.6.0 → 4.1.0
- **Element UI**: 2.15.6 → Element Plus 2.4.0

### 2. 构建工具变更
- **构建工具**: Vue CLI → Vite 4.4.9
- **开发服务器**: webpack-dev-server → Vite dev server
- **配置文件**: vue.config.js → vite.config.js

### 3. 主要语法变更

#### 应用创建方式
```javascript
// Vue 2
import Vue from 'vue'
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

// Vue 3
import { createApp } from 'vue'
const app = createApp(App)
app.use(store)
app.use(router)
app.mount('#app')
```

#### 全局属性挂载
```javascript
// Vue 2
Vue.prototype.$http = axios

// Vue 3
app.config.globalProperties.$http = axios
```

#### 路由配置
```javascript
// Vue 2
import Router from 'vue-router'
Vue.use(Router)
const router = new Router({
  mode: 'hash',
  routes: constantRoutes
})

// Vue 3
import { createRouter, createWebHashHistory } from 'vue-router'
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes
})
```

#### Store配置
```javascript
// Vue 2
import Vuex from 'vuex'
Vue.use(Vuex)
const store = new Vuex.Store({
  state: {},
  mutations: {}
})

// Vue 3
import { createStore } from 'vuex'
const store = createStore({
  state() {
    return {}
  },
  mutations: {}
})
```

#### 事件总线
```javascript
// Vue 2
export const eventBus = new Vue()

// Vue 3 (使用mitt)
import mitt from 'mitt'
export const eventBus = mitt()
```

### 4. Element UI → Element Plus

#### 导入方式
```javascript
// Vue 2 + Element UI
import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(Element)

// Vue 3 + Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
app.use(ElementPlus)
```

#### 组件导入
```javascript
// Vue 2
import { Message } from 'element-ui'

// Vue 3
import { ElMessage as Message } from 'element-plus'
```

### 5. 第三方插件升级
- **vue-awesome-swiper** → **swiper** (原生)
- **vuedraggable**: 2.24.3 → 4.1.0
- **vue-echarts**: 6.0.0 → 6.6.0

## 需要手动处理的问题

### 1. 组件中的Element UI导入
项目中还有很多组件文件使用了Element UI的导入，需要逐个替换：

```javascript
// 需要替换
import { Button, Input } from 'element-ui'

// 替换为
import { ElButton as Button, ElInput as Input } from 'element-plus'
```

### 2. 过滤器移除
Vue 3 移除了过滤器功能，需要改为计算属性或方法。

### 3. $on/$off/$once 移除
Vue 3 移除了事件总线相关API，已使用mitt替代。

### 4. router-view 语法更新
```vue
<!-- Vue 2 -->
<keep-alive>
  <router-view v-if="$route.meta.keepAlive" />
</keep-alive>
<router-view v-if="!$route.meta.keepAlive" />

<!-- Vue 3 -->
<router-view v-slot="{ Component, route }">
  <keep-alive>
    <component :is="Component" v-if="route.meta.keepAlive" />
  </keep-alive>
  <component :is="Component" v-if="!route.meta.keepAlive" />
</router-view>
```

## 启动项目

### 安装依赖
```bash
npm install --legacy-peer-deps
```

### 启动开发服务器
```bash
npm run dev
```

### 构建项目
```bash
npm run build
```

## 注意事项

1. **依赖冲突**: 使用 `--legacy-peer-deps` 标志安装依赖
2. **样式兼容**: Element Plus的样式可能与Element UI有差异
3. **API变更**: 部分Element Plus组件API与Element UI不完全兼容
4. **性能优化**: Vite构建速度更快，但可能需要调整一些配置

## 后续工作

1. 逐个检查和修复组件中的Element UI导入
2. 测试所有功能模块
3. 修复样式兼容性问题
4. 优化Vite配置
5. 更新文档和注释

## 升级收益

1. **性能提升**: Vue 3的响应式系统更高效
2. **构建速度**: Vite比webpack快很多
3. **类型支持**: 更好的TypeScript支持
4. **生态系统**: 更现代的生态系统支持
5. **长期维护**: Vue 3是长期支持版本
