import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 自定义插件来处理public文件导入问题
const handlePublicImports = () => {
  return {
    name: 'handle-public-imports',
    resolveId(id) {
      // 处理mapjs相关的文件
      if (id.includes('/mapjs/') || id.includes('mapbox-gl')) {
        return id
      }
    },
    load(id) {
      // 为mapjs和mapbox相关文件返回空的导出
      if (id.includes('/mapjs/') || id.includes('mapbox-gl')) {
        console.log(`Mocking module: ${id}`)
        return `
          // Mock module for ${id}
          export default {};
          export const Map = class MockMap {
            constructor() { console.log('Mock Map created'); }
            on() {}
            off() {}
            remove() {}
          };
          export const Marker = class MockMarker {
            constructor() { console.log('Mock Marker created'); }
            addTo() { return this; }
            remove() {}
          };
        `
      }
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), handlePublicImports()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'bj_src': resolve(__dirname, './src/views/bj_proatal_web')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 移除全局导入，避免循环导入问题
        // additionalData: `@import "@/assets/styles/element-variables.scss";`
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    proxy: {
      // 满意度
      '/workflowBoard': {
        target: 'http://localhost:8085',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/workflowBoard/, '/workflowBoard')
      },
      // 满意度
      '/proxy-satisfy': {
        target: 'http://***********:9085', // 联调网址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/proxy-satisfy/, '')
      },
      // 权限
      '/dev-api': {
        target: 'http://127.0.0.1:8085/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dev-api/, '/nxportal')
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'static',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'vuex'],
          'utils': ['axios', 'echarts', 'moment']
        }
      }
    }
  },
  define: {
    'process.env': {
      ...process.env,
      VUE_APP_BASE_API: JSON.stringify(process.env.VUE_APP_BASE_API || '/dev-api')
    }
  }
})
