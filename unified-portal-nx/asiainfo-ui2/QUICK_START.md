# 防复制功能快速开始指南

## 立即使用

防复制功能已经集成到项目中，并且**默认为所有页面启用**了防复制保护。

### 1. 最简单的使用方式

在任何Vue组件的模板中添加 `v-prevent-copy` 指令：

```vue
<template>
  <div v-prevent-copy>
    这里的内容将被保护，无法复制
  </div>
</template>
```

### 2. 全局防复制控制

**注意：全局防复制已默认启用**，如需临时禁用可以调用：

```javascript
// 临时禁用全局防复制
this.$preventCopy.disable()

// 重新启用全局防复制
this.$preventCopy.enable()

// 检查当前状态
console.log('防复制状态:', this.$preventCopy.isEnabled())
```

### 3. 快速测试

1. 启动项目：`npm run dev`
2. **防复制功能已自动启用**，直接测试：
   - 尝试选择页面文字（应该无法选择）
   - 尝试右键菜单（应该被禁用）
   - 尝试Ctrl+C复制（应该无效）
3. 如需在控制台中控制：
   ```javascript
   // 检查状态
   window.preventCopyHelper.getStatus()

   // 临时禁用
   window.preventCopyHelper.disableGlobal()

   // 重新启用
   window.preventCopyHelper.enableGlobal()
   ```

## 常用场景

### 场景1：保护整个页面
```vue
<template>
  <div class="page-container" v-prevent-copy>
    <!-- 整个页面内容 -->
  </div>
</template>
```

### 场景2：保护特定区域
```vue
<template>
  <div>
    <div class="normal-content">
      这里可以正常复制
    </div>
    
    <div class="protected-content" v-prevent-copy>
      这里无法复制
    </div>
  </div>
</template>
```

### 场景3：条件性保护
```vue
<template>
  <div v-prevent-copy="isProtected">
    根据条件决定是否保护
  </div>
</template>

<script>
export default {
  data() {
    return {
      isProtected: true // 可以动态改变
    }
  }
}
</script>
```

### 场景4：组件级别保护
```vue
<script>
export default {
  name: 'MyComponent',
  // 整个组件启用防复制
  preventCopy: true
}
</script>
```

## 验证功能

创建一个测试页面验证功能是否正常：

```vue
<template>
  <div>
    <h2>防复制测试</h2>
    
    <!-- 测试按钮 -->
    <button @click="$preventCopy.enable()">启用全局防复制</button>
    <button @click="$preventCopy.disable()">禁用全局防复制</button>
    
    <!-- 普通区域 -->
    <div style="border: 1px solid blue; padding: 10px; margin: 10px;">
      <h3>普通区域（可复制）</h3>
      <p>这里的文字可以选择和复制</p>
    </div>
    
    <!-- 保护区域 -->
    <div v-prevent-copy style="border: 1px solid red; padding: 10px; margin: 10px;">
      <h3>保护区域（禁止复制）</h3>
      <p>这里的文字无法选择和复制</p>
      
      <!-- 输入框仍然可用 -->
      <input v-model="testValue" placeholder="输入框仍然可用">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      testValue: '测试输入'
    }
  }
}
</script>
```

## 配置选项

在 `main.js` 中的当前配置（**已默认启用全局防复制**）：

```javascript
Vue.use(PreventCopyPlugin, {
  globalEnable: true,               // ✅ 已设置为true，全局启用
  autoEnableInProduction: true,     // 生产环境自动启用
  warningMessage: '大音平台内容受保护，禁止复制！',    // 提示消息
  showWarning: true                 // 是否显示提示
})
```

如需临时禁用，可以修改 `globalEnable: false`

## 注意事项

1. **输入框保护**：输入框（input、textarea）会自动排除，保持正常功能
2. **Element UI兼容**：已适配Element UI组件
3. **性能影响**：建议按需使用，避免全页面启用
4. **浏览器兼容**：支持现代浏览器

## 故障排除

### 问题1：输入框无法使用
**解决**：检查是否正确识别为输入元素，或者手动添加例外

### 问题2：某些内容仍可复制
**解决**：检查CSS优先级，确保防复制样式生效

### 问题3：影响正常交互
**解决**：使用局部保护而非全局保护

## 更多示例

项目中包含完整的示例文件：
- `src/components/PreventCopyDemo.vue` - 完整功能演示
- `src/views/PreventCopyTest.vue` - 测试页面

## 技术支持

如有问题，请查看：
1. `PREVENT_COPY_README.md` - 详细文档
2. 控制台错误信息
3. 联系开发团队
