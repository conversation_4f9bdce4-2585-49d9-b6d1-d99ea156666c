# 防复制功能使用说明

## 概述

本项目集成了完整的防复制功能，可以有效防止用户复制页面内容，包括文本选择、右键菜单、键盘快捷键、拖拽等操作。

## 功能特性

- ✅ 禁用文本选择
- ✅ 禁用右键菜单
- ✅ 禁用复制相关键盘快捷键（Ctrl+C、Ctrl+A、Ctrl+V等）
- ✅ 禁用开发者工具快捷键（F12、Ctrl+Shift+I等）
- ✅ 禁用图片和链接拖拽
- ✅ 保护输入框正常功能
- ✅ 支持全局和局部控制
- ✅ 支持动态启用/禁用

## 使用方法

### 1. 指令方式（推荐）

#### 基本用法
```vue
<template>
  <!-- 启用防复制 -->
  <div v-prevent-copy>
    这里的内容无法被复制
  </div>
  
  <!-- 条件防复制 -->
  <div v-prevent-copy="isProtected">
    根据条件决定是否保护
  </div>
  
  <!-- 明确禁用防复制 -->
  <div v-prevent-copy="false">
    这里的内容可以正常复制
  </div>
</template>

<script>
export default {
  data() {
    return {
      isProtected: true
    }
  }
}
</script>
```

### 2. 编程方式

```javascript
// 在Vue组件中使用
export default {
  mounted() {
    // 启用全局防复制
    this.$preventCopy.enable()
  },
  
  methods: {
    toggleProtection() {
      // 切换防复制状态
      this.$preventCopy.toggle()
    },
    
    checkStatus() {
      // 检查是否启用
      const isEnabled = this.$preventCopy.isEnabled()
      console.log('防复制状态:', isEnabled)
    },
    
    disableProtection() {
      // 禁用全局防复制
      this.$preventCopy.disable()
    }
  }
}
```

### 3. 组件级别配置

```javascript
export default {
  name: 'MyComponent',
  // 为整个组件启用防复制
  preventCopy: true,
  
  data() {
    return {
      // 组件数据
    }
  }
}
```

## 配置选项

在 `main.js` 中可以配置防复制插件的行为：

```javascript
Vue.use(PreventCopyPlugin, {
  // 是否全局启用防复制
  globalEnable: false,
  
  // 是否在生产环境自动启用
  autoEnableInProduction: true,
  
  // 自定义提示消息
  warningMessage: '内容受保护，禁止复制！',
  
  // 是否显示警告消息
  showWarning: true
})
```

## 文件结构

```
src/
├── utils/
│   └── preventCopy.js          # 防复制核心工具类
├── directive/
│   └── preventCopy.js          # Vue指令实现
├── plugins/
│   └── preventCopy.js          # Vue插件封装
└── components/
    └── PreventCopyDemo.vue     # 使用示例组件
```

## API 参考

### Vue 实例方法

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `$preventCopy.enable()` | 启用全局防复制 | 无 | 无 |
| `$preventCopy.disable()` | 禁用全局防复制 | 无 | 无 |
| `$preventCopy.toggle()` | 切换防复制状态 | 无 | 无 |
| `$preventCopy.isEnabled()` | 检查是否启用 | 无 | Boolean |
| `$preventCopy.getConfig()` | 获取配置信息 | 无 | Object |

### 指令参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `v-prevent-copy` | Boolean | true | 是否启用防复制 |
| `v-prevent-copy="true"` | Boolean | true | 明确启用防复制 |
| `v-prevent-copy="false"` | Boolean | false | 明确禁用防复制 |

## 注意事项

1. **输入框保护**：防复制功能会自动识别输入框（input、textarea、contenteditable），确保这些元素的正常功能不受影响。

2. **性能考虑**：建议根据实际需要选择性启用防复制，避免对所有页面都启用全局防复制。

3. **用户体验**：过度的防复制可能影响用户体验，建议在敏感内容区域使用。

4. **浏览器兼容性**：支持现代浏览器，包括Chrome、Firefox、Safari、Edge等。

5. **开发调试**：在开发环境中，可以通过配置 `globalEnable: false` 来禁用自动防复制，便于调试。

## 示例页面

项目中包含了一个完整的示例组件 `PreventCopyDemo.vue`，展示了各种使用场景：

- 全局防复制控制
- 局部区域保护
- 条件性保护
- 输入框例外处理
- 动态启用/禁用

## 常见问题

### Q: 如何在特定页面禁用防复制？
A: 可以在页面组件中调用 `this.$preventCopy.disable()` 或使用 `v-prevent-copy="false"` 指令。

### Q: 输入框无法正常使用怎么办？
A: 防复制功能会自动识别输入元素，如果遇到问题，请检查元素是否被正确识别为输入元素。

### Q: 如何自定义防复制的样式？
A: 可以通过CSS覆盖 `.prevent-copy` 类的样式，或者修改插件中的样式定义。

### Q: 防复制功能会影响SEO吗？
A: 不会，防复制功能主要通过JavaScript和CSS实现，不会影响搜索引擎的内容抓取。

## 更新日志

- v1.0.0: 初始版本，支持基本的防复制功能
- 包含Vue指令、插件封装和示例组件

## 技术支持

如有问题或建议，请联系开发团队。
