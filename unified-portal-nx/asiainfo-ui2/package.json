{"name": "srd-bd2-boot", "version": "1.0.0", "description": "大音平台", "author": "srd-bd2", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "build:stage": "vite build --mode staging", "build:local": "vite build --mode local", "preview": "vite preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "http://***********:8889/appproject/base/asiainfo-srd-bd-boot.git"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@riophae/vue-treeselect": "^0.4.0", "@vuikit/icons": "^0.8.1", "@vuikit/theme": "^0.8.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.0", "clipboard": "2.0.6", "core-js": "^3.32.0", "crypto-js": "^4.1.1", "echarts": "^5.4.3", "element-plus": "^2.4.0", "file-saver": "2.0.4", "fuse.js": "6.4.3", "html2canvas": "^1.4.1", "jquery": "^3.5.1", "jsencrypt": "3.0.0-rc.1", "jspdf": "^2.5.1", "mitt": "^3.0.1", "moment": "^2.29.4", "path-to-regexp": "6.2.0", "resize-detector": "^0.3.0", "screenfull": "5.0.2", "sortablejs": "1.10.2", "swiper": "^10.0.0", "vue": "^3.3.4", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-echarts": "^6.6.0", "vue-router": "^4.2.4", "vue-upload-component": "^3.1.0", "vuedraggable": "^4.1.0", "vuex": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vitejs/plugin-vue": "^4.3.4", "babel-eslint": "10.1.0", "chalk": "4.1.0", "compression-webpack-plugin": "^10.0.0", "connect": "3.6.6", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "less": "^4.1.2", "less-loader": "^11.1.3", "lint-staged": "10.5.3", "runjs": "4.4.2", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "sass": "^1.64.1", "sass-loader": "^13.3.2", "js-beautify": "1.13.0", "js-cookie": "^3.0.1", "vite": "^4.4.9"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}