import '@babel/polyfill'
import { createApp } from 'vue'
// Swiper样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

import Cookies from 'js-cookie'
// 配置echarts
import * as echarts from 'echarts'

// 引入外部字体
import '@/assets/Family/font.css'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/styles/element-variables.scss'
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/global.scss' //
import App from './App.vue'
import store from './store'
import router from './router'
import permissionDirective from './views/bj_proatal_web/nx-components/directive/permission.js'
import getUserPermission from './user' // 获取用户权限信息
import Blank2 from '@/views/bj_proatal_web/components/common/Blank2.vue'
import moment from 'moment'

// import './assets/icons' // icon
// import './permission' // permission control
// import { getDicts } from '@/api/system/dict/data'
// import { getConfigKey } from '@/api/system/config'
import {
  addDateRange,
  download,
  handleTree,
  parseTime,
  resetForm,
  selectDictLabel,
  selectDictLabels
} from '@/utils/tools'
// import Pagination from '@/components/Pagination'
// 自定义表格工具扩展
// import RightToolbar from '@/components/RightToolbar'
// 代码高亮插件
// import hljs from 'highlight.js'
// import 'highlight.js/styles/github-gist.css'
// 引入jquery
// import $ from 'jquery'

// 全局方法挂载 - 将在createApp后配置
// Vue.prototype.getDicts = getDicts
// Vue.prototype.getConfigKey = getConfigKey
const globalProperties = {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  download,
  handleTree,
  $echarts: echarts,
  $moment: moment,
  msgSuccess: function(msg) {
    this.$message({ showClose: true, message: msg, type: 'success' })
  },
  msgError: function(msg) {
    this.$message({ showClose: true, message: msg, type: 'error' })
  },
  msgInfo: function(msg) {
    this.$message.info(msg)
  }
}
import dragscroll from './utils/directives.js'

// 引入防复制插件
import PreventCopyPlugin from './plugins/preventCopy'
import preventCopyHelper from './utils/preventCopyHelper'

// ***********************************保留2位小数针对本项目做的补丁********************

function isInt(n) {
  if (n && typeof n === 'string') {
    n = Number(n)
    return Number(n) === n && n % 1 === 0
  }
  return Number(n) === n && n % 1 === 0
}
const _toFixed = Number.prototype.toFixed
Number.prototype.toFixed2 = function() {
  // 保留2
  // console.log('this:', this)
  // console.log('arguments[0]:', arguments[0])

  if (this === 0 || this === 0.00 || this == 0.0) {
    return 0
  }

  if (isNaN(this)) {
    return ''
  }

  if (arguments[0] == 2 && !isNaN(this)) {
    if (!isInt(this)) { // 不是整数
      const y = String(this).indexOf('.') + 1// 获取小数点的位置
      var count = String(this).length - y// 获取小数点后的个数

      if (count >= 2) { // 2位小数
        if (this < 0 && this > -0.005) {
          return -0.01
        }
        if (this > 0 && this < 0.005) {
          return 0.01
        }
        return Number.prototype.toFixed.call(this, 2)
      }
      if (count == 1) { // 1 位小数
        return this
      }
    } else {
      return this
    }
  }
}
// ***********************************保留2位小数针对本项目做的补丁********************

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

// Vue3配置将在createApp后设置

/* ----------------迁移函数开始----------------*/
// import Es6Promise from 'es6-promise';
// Es6Promise.polyfill();
// import evBreadcrumb from './views/evaluate/components/Breadcrumb';
// Vue.component('ev-breadcrumb',evBreadcrumb);
import axios from 'axios'
import { ElMessage } from 'element-plus'
let baseURL = ''
if (process.env.NODE_ENV === 'development') {
  // 本地mock地址
  // baseURL = `http://${window.location.host}/mock/complaint-service`;
  // 反向代理地址
  baseURL = `http://${window.location.host}/proxy-satisfy`
} else {
  baseURL = '/bjcem'
}
// 请求默认配置
axios.defaults.baseURL = baseURL

console.log(axios.defaults.baseURL)
// axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8';
// 拦截器
axios.interceptors.request.use((req) => {
  console.log('请求参数=>', req.url, req.data)
  // req.url += `?${Utils.serialize(req.data)}`;
  // 过滤掉文件上传，不做data转换
  // if (req.url.indexOf('createUserGroupByCSV') === -1) {
  //   req.data = Utils.serialize(req.data);
  // }
  return req
})
axios.interceptors.response.use((res) => {
  // console.log('返回值==>', res);
  if (res.status === 200) {
    // if (res.data.success) {
    return res.data
    // }
    // ElMessage.error(res.data.msg);
    // return Promise.reject(res.data);
  }
  return Promise.reject(res)
}, (error) => {
  // ElMessage.error(`服务器错误:${error}`);
  return Promise.reject()
})

/* ----------------迁移函数结束----------------*/

// Vue3中使用mitt替代事件总线
import mitt from 'mitt'
export const eventBus = mitt()

// Vue3应用创建
;(async function() {
  // 登录不进去或者未分配权限
  let error = false
  try {
    await getUserPermission()
  } catch (e) {
    error = true
  }

  const app = createApp(App)

  // 使用插件
  app.use(store)
  app.use(router)
  app.use(ElementPlus, {
    size: Cookies.get('size') || 'medium'
  })
  // 注册权限指令
  app.directive('permission', permissionDirective)

  // 使用防复制插件
  app.use(PreventCopyPlugin, {
    globalEnable: true,
    autoEnableInProduction: true,
    warningMessage: '大音平台内容受保护，禁止复制！',
    showWarning: true
  })

  // 注册全局组件
  app.component('Blank2', Blank2)

  // 注册全局指令
  app.directive('dragscroll', dragscroll)

  // 挂载全局属性
  Object.keys(globalProperties).forEach(key => {
    app.config.globalProperties[key] = globalProperties[key]
  })

  // 挂载防复制辅助工具
  app.config.globalProperties.$preventCopyHelper = preventCopyHelper
  app.config.globalProperties.$message = ElMessage
  app.config.globalProperties.$http = axios

  // 全局配置
  app.config.productionTip = false

  // 挂载应用
  const vm = app.mount('#app')

  if (error) {
    router.replace({ path: '/401' })
  }
})()
