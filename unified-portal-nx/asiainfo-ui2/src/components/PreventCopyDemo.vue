<template>
  <div class="prevent-copy-demo">
    <h2>防复制功能演示</h2>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>控制面板</h3>
      <el-button @click="toggleGlobalPreventCopy" :type="isGlobalEnabled ? 'danger' : 'primary'">
        {{ isGlobalEnabled ? '禁用' : '启用' }}全局防复制
      </el-button>
      <el-button @click="showStatus" type="info">查看状态</el-button>
    </div>

    <!-- 普通内容区域 -->
    <div class="content-section">
      <h3>普通内容区域（可复制）</h3>
      <p>这是普通的文本内容，可以正常选择和复制。</p>
      <p>您可以右键查看菜单，也可以使用Ctrl+C复制文本。</p>
      <img src="/favicon.ico" alt="示例图片" style="width: 50px; height: 50px;">
    </div>

    <!-- 受保护的内容区域 -->
    <div class="content-section" v-prevent-copy>
      <h3>受保护的内容区域（禁止复制）</h3>
      <p>这是受保护的文本内容，无法选择和复制。</p>
      <p>右键菜单被禁用，复制快捷键也被禁用。</p>
      <img src="/favicon.ico" alt="受保护的图片" style="width: 50px; height: 50px;">
    </div>

    <!-- 条件保护的内容区域 -->
    <div class="content-section" v-prevent-copy="conditionalProtection">
      <h3>条件保护的内容区域</h3>
      <p>这个区域的保护状态可以动态控制。</p>
      <el-button @click="toggleConditionalProtection" :type="conditionalProtection ? 'danger' : 'primary'">
        {{ conditionalProtection ? '禁用' : '启用' }}区域保护
      </el-button>
    </div>

    <!-- 输入区域（例外处理） -->
    <div class="content-section" v-prevent-copy>
      <h3>输入区域（例外处理）</h3>
      <p>即使在受保护区域内，输入框仍然可以正常使用：</p>
      <el-input v-model="inputValue" placeholder="这里可以正常输入和复制"></el-input>
      <br><br>
      <el-input type="textarea" v-model="textareaValue" placeholder="文本域也可以正常使用"></el-input>
    </div>

    <!-- 使用说明 -->
    <div class="usage-section">
      <h3>使用说明</h3>
      <h4>1. 指令使用方法：</h4>
      <pre><code>
<!-- 启用防复制 -->
&lt;div v-prevent-copy&gt;受保护的内容&lt;/div&gt;

<!-- 条件防复制 -->
&lt;div v-prevent-copy="isProtected"&gt;条件保护的内容&lt;/div&gt;

<!-- 禁用防复制 -->
&lt;div v-prevent-copy="false"&gt;不受保护的内容&lt;/div&gt;
      </code></pre>

      <h4>2. 编程方式控制：</h4>
      <pre><code>
// 启用全局防复制
this.$preventCopy.enable()

// 禁用全局防复制
this.$preventCopy.disable()

// 切换防复制状态
this.$preventCopy.toggle()

// 检查是否启用
const isEnabled = this.$preventCopy.isEnabled()
      </code></pre>

      <h4>3. 组件级别配置：</h4>
      <pre><code>
export default {
  name: 'MyComponent',
  // 为整个组件启用防复制
  preventCopy: true,
  // ... 其他选项
}
      </code></pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreventCopyDemo',
  data() {
    return {
      isGlobalEnabled: false,
      conditionalProtection: false,
      inputValue: '这是输入框的内容',
      textareaValue: '这是文本域的内容\n可以多行编辑'
    }
  },
  mounted() {
    this.checkGlobalStatus()
  },
  methods: {
    // 切换全局防复制状态
    toggleGlobalPreventCopy() {
      this.$preventCopy.toggle()
      this.checkGlobalStatus()
    },
    
    // 检查全局状态
    checkGlobalStatus() {
      this.isGlobalEnabled = this.$preventCopy.isEnabled()
    },
    
    // 切换条件保护
    toggleConditionalProtection() {
      this.conditionalProtection = !this.conditionalProtection
    },
    
    // 显示状态信息
    showStatus() {
      const config = this.$preventCopy.getConfig()
      const status = {
        全局防复制状态: this.isGlobalEnabled ? '已启用' : '已禁用',
        条件保护状态: this.conditionalProtection ? '已启用' : '已禁用',
        配置信息: config
      }
      
      this.$alert(JSON.stringify(status, null, 2), '防复制状态', {
        confirmButtonText: '确定',
        type: 'info'
      })
    }
  }
}
</script>

<style scoped>
.prevent-copy-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.control-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.content-section {
  border: 1px solid #ddd;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.usage-section {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
}

pre {
  background: #f4f4f4;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

code {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

h3 {
  color: #333;
  margin-top: 0;
}

h4 {
  color: #666;
  margin-bottom: 10px;
}
</style>
