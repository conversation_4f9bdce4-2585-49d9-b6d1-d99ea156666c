<template>
  <div class="field-data-display">
    <!-- 标签模式 -->
    <div v-if="mode === 'tags'" class="tags-mode">
      <el-tag
        v-for="(value, key) in fieldData"
        :key="key"
        :type="getTagType(value)"
        size="mini"
        class="field-tag"
      >
        <span class="field-name">{{ key }}:</span>
        <span class="field-value">{{ formatValue(value) }}</span>
      </el-tag>
    </div>

    <!-- 表格模式 -->
    <div v-else-if="mode === 'table'" class="table-mode">
      <el-table :data="tableData" border size="mini" :show-header="showHeader">
        <el-table-column v-if="showHeader" label="字段名称" prop="name" width="150" />
        <el-table-column v-if="showHeader" label="字段值" prop="value" />
        <el-table-column v-if="showHeader" label="数据类型" prop="type" width="100" />
        
        <template v-if="!showHeader">
          <el-table-column prop="name" width="150" />
          <el-table-column prop="value" />
          <el-table-column prop="type" width="100" />
        </template>
      </el-table>
    </div>

    <!-- 列表模式 -->
    <div v-else-if="mode === 'list'" class="list-mode">
      <div
        v-for="(value, key) in fieldData"
        :key="key"
        class="list-item"
      >
        <span class="item-name">{{ key }}:</span>
        <span class="item-value">{{ formatValue(value) }}</span>
      </div>
    </div>

    <!-- 描述列表模式 -->
    <div v-else-if="mode === 'descriptions'" class="descriptions-mode">
      <el-descriptions :column="column" :border="border" size="mini">
        <el-descriptions-item
          v-for="(value, key) in fieldData"
          :key="key"
          :label="key"
        >
          {{ formatValue(value) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- JSON模式 -->
    <div v-else-if="mode === 'json'" class="json-mode">
      <pre class="json-content">{{ jsonContent }}</pre>
    </div>

    <!-- 默认模式（摘要） -->
    <div v-else class="summary-mode">
      <span class="summary-text">{{ summaryText }}</span>
    </div>
  </div>
</template>

<script>
import { formatFieldValue, generateFieldSummary } from '@/utils/verticalDataUtils'

export default {
  name: 'FieldDataDisplay',
  props: {
    // 字段数据
    fieldData: {
      type: Object,
      default: () => ({})
    },
    // 显示模式: tags, table, list, descriptions, json, summary
    mode: {
      type: String,
      default: 'tags'
    },
    // 最大显示长度（摘要模式）
    maxLength: {
      type: Number,
      default: 100
    },
    // 描述列表列数
    column: {
      type: Number,
      default: 2
    },
    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },
    // 是否显示表头
    showHeader: {
      type: Boolean,
      default: true
    },
    // 最大显示字段数
    maxFields: {
      type: Number,
      default: 10
    }
  },
  computed: {
    /**
     * 表格数据
     */
    tableData() {
      if (!this.fieldData || typeof this.fieldData !== 'object') {
        return []
      }
      
      return Object.entries(this.fieldData)
        .slice(0, this.maxFields)
        .map(([key, value]) => ({
          name: key,
          value: this.formatValue(value),
          type: this.getValueType(value)
        }))
    },
    
    /**
     * JSON内容
     */
    jsonContent() {
      try {
        return JSON.stringify(this.fieldData, null, 2)
      } catch (error) {
        return '无效的JSON数据'
      }
    },
    
    /**
     * 摘要文本
     */
    summaryText() {
      return generateFieldSummary(this.fieldData, this.maxLength)
    }
  },
  methods: {
    /**
     * 格式化值
     */
    formatValue(value) {
      return formatFieldValue(value)
    },
    
    /**
     * 获取值类型
     */
    getValueType(value) {
      if (typeof value === 'number') {
        return '数字'
      } else if (typeof value === 'boolean') {
        return '布尔'
      } else if (value instanceof Date) {
        return '日期'
      } else {
        return '文本'
      }
    },
    
    /**
     * 获取标签类型
     */
    getTagType(value) {
      if (typeof value === 'number') {
        return 'warning'
      } else if (typeof value === 'boolean') {
        return value ? 'success' : 'danger'
      } else if (value instanceof Date) {
        return 'info'
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped>
.field-data-display {
  width: 100%;
}

/* 标签模式 */
.tags-mode {
  max-height: 120px;
  overflow-y: auto;
}

.field-tag {
  margin: 2px;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.field-name {
  font-weight: bold;
  margin-right: 4px;
}

.field-value {
  color: #606266;
}

/* 列表模式 */
.list-mode {
  max-height: 200px;
  overflow-y: auto;
}

.list-item {
  display: flex;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.item-name {
  flex: 0 0 120px;
  font-weight: bold;
  color: #303133;
}

.item-value {
  flex: 1;
  color: #606266;
  word-break: break-all;
}

/* 描述列表模式 */
.descriptions-mode {
  max-height: 300px;
  overflow-y: auto;
}

/* JSON模式 */
.json-mode {
  max-height: 300px;
  overflow-y: auto;
}

.json-content {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 摘要模式 */
.summary-mode {
  padding: 8px 0;
}

.summary-text {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-all;
}

/* 表格模式 */
.table-mode {
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .field-tag {
    max-width: 200px;
  }
  
  .item-name {
    flex: 0 0 80px;
    font-size: 12px;
  }
  
  .item-value {
    font-size: 12px;
  }
  
  .json-content {
    font-size: 10px;
  }
}
</style>