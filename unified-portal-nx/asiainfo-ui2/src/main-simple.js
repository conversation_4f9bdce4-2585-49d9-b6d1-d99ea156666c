import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 简单的App组件
const SimpleApp = {
  template: `
    <div id="app" style="padding: 20px;">
      <h1>🎉 Vue3 升级成功！</h1>
      <p>如果您能看到这个页面，说明Vue3应用已经正常运行。</p>
      <el-button type="primary" @click="showMessage">测试Element Plus</el-button>
      <div v-if="message" style="margin-top: 10px; padding: 10px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px;">
        {{ message }}
      </div>
    </div>
  `,
  data() {
    return {
      message: ''
    }
  },
  methods: {
    showMessage() {
      this.message = '✅ Element Plus 组件工作正常！Vue3 升级成功！'
      console.log('Vue3 应用运行正常！')
    }
  },
  mounted() {
    console.log('Vue3 应用已挂载成功！')
  }
}

// 创建Vue3应用
const app = createApp(SimpleApp)

// 使用Element Plus
app.use(ElementPlus)

// 挂载应用
app.mount('#app')

console.log('Vue3 应用初始化完成！')
