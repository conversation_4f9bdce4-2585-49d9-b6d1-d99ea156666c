{"version": 3, "sources": ["wangEditor.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "wang<PERSON><PERSON><PERSON>", "this", "createElemByHTML", "html", "div", "document", "createElement", "innerHTML", "children", "isDOMList", "selector", "HTMLCollection", "NodeList", "querySelectorAll", "result", "Dom<PERSON><PERSON>", "selector<PERSON><PERSON><PERSON>", "nodeType", "replace", "trim", "indexOf", "length", "i", "$", "obj<PERSON>or<PERSON>ach", "obj", "fn", "key", "hasOwnProperty", "call", "arrFor<PERSON>ach", "fakeArr", "item", "getRandom", "prefix", "Math", "random", "toString", "slice", "replaceHtmlSymbol", "Bold", "editor", "$elem", "type", "_active", "DropList", "menu", "opt", "_this", "$container", "$title", "titleHtml", "replaceLang", "addClass", "append", "list", "onClick", "_emptyFn", "$list", "for<PERSON>ach", "elemHtml", "value", "$li", "on", "e", "hideTimeoutId", "setTimeout", "hide", "_rendered", "_show", "Head", "droplist", "width", "_command", "Panel", "Link", "Italic", "Redo", "StrikeThrough", "Underline", "Undo", "List", "Justify", "ForeColor", "BackColor", "Quote", "Code", "Emoticon", "Table", "Video", "Image", "Menus", "menus", "getPasteText", "clipboardData", "originalEvent", "pasteText", "window", "getData", "getPasteHtml", "filterStyle", "pasteHtml", "docSplitHtml", "split", "getPasteImgs", "items", "test", "push", "getAsFile", "Text", "Command", "API", "_current<PERSON><PERSON>e", "Progress", "_time", "_isShow", "_isRender", "_timeoutId", "$textContainer", "$textContainerElem", "$bar", "UploadImg", "Editor", "toolbarSelector", "textSelector", "Error", "id", "editorId", "customConfig", "prototype", "constructor", "elem", "get", "index", "first", "last", "types", "addEventListener", "target", "matches", "off", "removeEventListener", "attr", "val", "getAttribute", "setAttribute", "className", "arr", "filter", "join", "removeClass", "css", "currentStyle", "style", "styleArr", "resultArr", "map", "show", "$children", "child", "append<PERSON><PERSON><PERSON>", "remove", "parent", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "isContain", "$child", "contains", "getSizeData", "getBoundingClientRect", "getNodeName", "nodeName", "find", "text", "focus", "parentUntil", "_currentElem", "results", "equal", "insertBefore", "$referenceNode", "referenceNode", "parentNode", "insertAfter", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "config", "zIndex", "debug", "pasteFilterStyle", "showLinkImg", "uploadImgMaxSize", "uploadImgShowBase64", "uploadFileName", "uploadImgParams", "token", "uploadImgHeaders", "withCredentials", "uploadImgTimeout", "uploadImgHooks", "before", "xhr", "files", "success", "fail", "error", "timeout", "UA", "_ua", "navigator", "userAgent", "isWebkit", "isIE", "isSeleEmpty", "selection", "isSelectionEmpty", "createEmptyRange", "cmd", "do", "collapseRange", "restoreSelection", "tryChangeActive", "queryCommandState", "str", "lang<PERSON>rgs", "reg", "clearTimeout", "$menuELem", "menuHeight", "height", "showTimeoutId", "$selectionElem", "getSelectionContainerElem", "$textElem", "cmdValue", "queryCommandValue", "emptyFn", "_isCreatedPanelMenus", "$body", "$closeBtn", "$tabTitleContainer", "$tabContentContainer", "tabs", "tabTitleArr", "tabContentArr", "tab", "tabIndex", "title", "tpl", "$content", "_index", "stopPropagation", "events", "event", "$inputs", "_hideOther<PERSON><PERSON><PERSON>", "panel", "$linkelem", "createRangeByElem", "_createPanel", "getSelectionText", "link", "inputLinkId", "inputTextId", "btnOkId", "btnDelId", "delBtnDisplay", "$link", "$text", "_insertLink", "_delLink", "selectionText", "$selectionELem", "$parent", "content", "$targetELem", "$startElem", "getSelectionStartElem", "$endElem", "getSelectionEndElem", "$code", "textId", "btnId", "_insertCode", "_updateCode", "$parentElem", "faceHtml", "handHtml", "_insert", "emoji", "_createEditPanel", "_createInsertPanel", "btnInsertId", "textRowNum", "textColNum", "row<PERSON>um", "parseInt", "colNum", "r", "c", "_this2", "addRowBtnId", "addColBtnId", "delRowBtnId", "delColBtnId", "delTableBtnId", "_addRow", "_addCol", "_delRow", "_delCol", "_delTable", "_getLocationData", "$tr", "$tds", "tdLength", "td", "$tbody", "$trs", "tr<PERSON><PERSON><PERSON>", "tr", "locationData", "trData", "$currentTr", "tdData", "newTr", "tdIndex", "$currentTd", "name", "toLowerCase", "$table", "textValId", "width30", "width50", "width100", "delBtn", "tabsConfig", "$img", "_selectedImg", "uploadImg", "upTriggerId", "upFileId", "linkUrlId", "linkBtnId", "$file", "fileElem", "click", "fileList", "$linkUrl", "url", "insertLinkImg", "tabsConfigResult", "uploadImgServer", "customUploadImg", "FileReader", "MenuConstructors", "bold", "head", "italic", "redo", "strikeThrough", "underline", "undo", "justify", "foreColor", "backColor", "quote", "code", "emoticon", "table", "video", "image", "init", "<PERSON><PERSON>ey", "MenuConstructor", "_addToToolbar", "_bindEvent", "$toolbarElem", "getRange", "changeActive", "clear", "initSelection", "_saveRangeRealTime", "_enterKeyHandle", "_clear<PERSON><PERSON>le", "_pasteHandle", "_tabHandle", "_imgHandle", "saveRange", "pHandle", "$p", "codeHandle", "selectionNodeName", "parentNodeName", "queryCommandSupported", "_willBreakCode", "preventDefault", "_startOffset", "startOffset", "codeLength", "keyCode", "txtHtml", "ex", "pasteFiles", "img", "_name", "_execCommand", "change", "_insertHTML", "range", "insertNode", "deleteContents", "pasteHTML", "_insertElem", "execCommand", "_range", "getSelection", "rangeCount", "getRangeAt", "$containerElem", "toStart", "collapse", "commonAncestorContainer", "startContainer", "endContainer", "endOffset", "removeAllRanges", "addRange", "setEnd", "<PERSON><PERSON><PERSON><PERSON>", "createRange", "selectNodeContents", "selectNode", "progress", "Date", "now", "timeoutId", "_hide", "_typeof", "Symbol", "iterator", "_alert", "alertInfo", "debugInfo", "customAlert", "alert", "onload", "onerror", "<PERSON>ab<PERSON>", "src", "_this3", "maxSize", "maxSizeM", "max<PERSON><PERSON><PERSON>", "uploadImgMaxLength", "hooks", "resultFiles", "errInfo", "file", "size", "bind", "formdata", "FormData", "uploadImgServerArr", "uploadImgServerHash", "encodeURIComponent", "XMLHttpRequest", "open", "ontimeout", "upload", "onprogress", "percent", "progressBar", "lengthComputable", "loaded", "total", "onreadystatechange", "readyState", "status", "responseText", "JSON", "parse", "customInsert", "errno", "data", "beforeResult", "prevent", "msg", "setRequestHeader", "send", "reader", "readAsDataURL", "_initConfig", "Object", "assign", "langConfig", "lang", "RegExp", "_initDom", "$toolbarSelector", "config$$1", "_initCommand", "_initSelectionAPI", "_initUploadImg", "_initMenus", "_initText", "txt", "newLine", "$last", "onChangeTimeoutId", "beforeChangeHtml", "onchange", "currentHtml", "create", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "to", "arguments", "nextSource", "<PERSON><PERSON><PERSON>", "Element", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "s", "ownerDocument", "getElementsByTagName"], "mappings": "CAAC,SAAUA,EAAQC,GACC,gBAAZC,UAA0C,mBAAXC,QAAyBA,OAAOD,QAAUD,IAC9D,kBAAXG,SAAyBA,OAAOC,IAAMD,OAAOH,GACnDD,EAAOM,WAAaL,KACpBM,KAAM,WAAe,YAoDvB,SAASC,GAAiBC,GACtB,GAAIC,OAAM,EAGV,OAFAA,GAAMC,SAASC,cAAc,OAC7BF,EAAIG,UAAYJ,EACTC,EAAII,SAIf,QAASC,GAAUC,GACf,QAAKA,IAGDA,YAAoBC,iBAAkBD,YAAoBE,WAOlE,QAASC,GAAiBH,GACtB,GAAII,GAAST,SAASQ,iBAAiBH,EACvC,OAAID,GAAUK,GACHA,GAECA,GAKhB,QAASC,GAAWL,GAChB,GAAKA,EAAL,CAKA,GAAIA,YAAoBK,GACpB,MAAOL,EAGXT,MAAKS,SAAWA,CAGhB,IAAIM,KACsB,KAAtBN,EAASO,SAETD,GAAkBN,GACXD,EAAUC,GAEjBM,EAAiBN,EACU,gBAAbA,KAEdA,EAAWA,EAASQ,QAAQ,SAAU,IAAIC,OAGtCH,EAF0B,IAA1BN,EAASU,QAAQ,KAEAlB,EAAiBQ,GAGjBG,EAAiBH,GAI1C,IAAIW,GAASL,EAAeK,MAC5B,KAAKA,EAED,MAAOpB,KAIX,IAAIqB,OAAI,EACR,KAAKA,EAAI,EAAGA,EAAID,EAAQC,IACpBrB,KAAKqB,GAAKN,EAAeM,EAE7BrB,MAAKoB,OAASA,GA6WlB,QAASE,GAAEb,GACP,MAAO,IAAIK,GAAWL,GA+H1B,QAASc,GAAWC,EAAKC,GACrB,GAAIC,OAAM,EAEV,KAAKA,IAAOF,GACR,GAAIA,EAAIG,eAAeD,KAEJ,IADND,EAAGG,KAAKJ,EAAKE,EAAKF,EAAIE,IAE3B,MAOhB,QAASG,GAAWC,EAASL,GACzB,GAAIJ,OAAI,GACJU,MAAO,GAEPX,EAASU,EAAQV,QAAU,CAC/B,KAAKC,EAAI,EAAGA,EAAID,IACZW,EAAOD,EAAQT,IAEA,IADNI,EAAGG,KAAKE,EAASC,EAAMV,IAFZA,MAU5B,QAASW,GAAUC,GACf,MAAOA,GAASC,KAAKC,SAASC,WAAWC,MAAM,GAInD,QAASC,GAAkBpC,GACvB,MAAY,OAARA,EACO,GAEJA,EAAKe,QAAQ,MAAO,QAAQA,QAAQ,MAAO,QAAQA,QAAQ,MAAO,UAS7E,QAASsB,GAAKC,GACVxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,qFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAuEnB,QAASC,GAASC,EAAMC,GACpB,GAAIC,GAAQ/C,KAGRwC,EAASK,EAAKL,MAClBxC,MAAK6C,KAAOA,EACZ7C,KAAK8C,IAAMA,CAEX,IAAIE,GAAa1B,EAAE,oCAGf2B,EAASH,EAAIG,OACbC,MAAY,EACZD,KAEAC,EAAYD,EAAO/C,OACnBgD,EAAYC,EAAYX,EAAQU,GAChCD,EAAO/C,KAAKgD,GAEZD,EAAOG,SAAS,gBAChBJ,EAAWK,OAAOJ,GAGtB,IAAIK,GAAOR,EAAIQ,SACXZ,EAAOI,EAAIJ,MAAQ,OACnBa,EAAUT,EAAIS,SAAWC,EAGzBC,EAAQnC,EAAE,eAA0B,SAAToB,EAAkB,WAAa,aAAe,UAC7EM,GAAWK,OAAOI,GAClBH,EAAKI,QAAQ,SAAU3B,GACnB,GAAIU,GAAQV,EAAKU,MAGbkB,EAAWlB,EAAMvC,MACrByD,GAAWR,EAAYX,EAAQmB,GAC/BlB,EAAMvC,KAAKyD,EAEX,IAAIC,GAAQ7B,EAAK6B,MACbC,EAAMvC,EAAE,6BACRmB,KACAoB,EAAIR,OAAOZ,GACXgB,EAAMJ,OAAOQ,GACbpB,EAAMqB,GAAG,QAAS,SAAUC,GACxBR,EAAQK,GAGRb,EAAMiB,cAAgBC,WAAW,WAC7BlB,EAAMmB,QACP,QAMflB,EAAWc,GAAG,aAAc,SAAUC,GAClChB,EAAMiB,cAAgBC,WAAW,WAC7BlB,EAAMmB,QACP,KAIPlE,KAAKgD,WAAaA,EAGlBhD,KAAKmE,WAAY,EACjBnE,KAAKoE,OAAQ,EA2DjB,QAASC,GAAK7B,GACV,GAAIO,GAAQ/C,IAEZA,MAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,+DACftB,KAAK0C,KAAO,WAGZ1C,KAAK2C,SAAU,EAGf3C,KAAKsE,SAAW,GAAI1B,GAAS5C,MACzBuE,MAAO,IACPtB,OAAQ3B,EAAE,eACVoB,KAAM,OACNY,OAASb,MAAOnB,EAAE,eAAgBsC,MAAO,SAAYnB,MAAOnB,EAAE,eAAgBsC,MAAO,SAAYnB,MAAOnB,EAAE,eAAgBsC,MAAO,SAAYnB,MAAOnB,EAAE,eAAgBsC,MAAO,SAAYnB,MAAOnB,EAAE,eAAgBsC,MAAO,SAAYnB,MAAOnB,EAAE,aAAcsC,MAAO,QACnQL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAiD3B,QAASa,GAAM5B,EAAMC,GACjB9C,KAAK6C,KAAOA,EACZ7C,KAAK8C,IAAMA,EAyLf,QAAS4B,GAAKlC,GACVxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,6DACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EA+InB,QAASgC,GAAOnC,GACZxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,uFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EA+CnB,QAASiC,GAAKpC,GACVxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,qFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAsBnB,QAASkC,GAAcrC,GACnBxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,8FACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EA+CnB,QAASmC,GAAUtC,GACfxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,0FACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EA+CnB,QAASoC,GAAKvC,GACVxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,qFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAsBnB,QAASqC,GAAKxC,GACV,GAAIO,GAAQ/C,IAEZA,MAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,8DACftB,KAAK0C,KAAO,WAGZ1C,KAAK2C,SAAU,EAGf3C,KAAKsE,SAAW,GAAI1B,GAAS5C,MACzBuE,MAAO,IACPtB,OAAQ3B,EAAE,eACVoB,KAAM,OACNY,OAASb,MAAOnB,EAAE,4DAA6DsC,MAAO,sBAAyBnB,MAAOnB,EAAE,oDAAqDsC,MAAO,wBACpLL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MA2D3B,QAASqB,GAAQzC,GACb,GAAIO,GAAQ/C,IAEZA,MAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,uEACftB,KAAK0C,KAAO,WAGZ1C,KAAK2C,SAAU,EAGf3C,KAAKsE,SAAW,GAAI1B,GAAS5C,MACzBuE,MAAO,IACPtB,OAAQ3B,EAAE,eACVoB,KAAM,OACNY,OAASb,MAAOnB,EAAE,2DAA4DsC,MAAO,gBAAmBnB,MAAOnB,EAAE,6DAA8DsC,MAAO,kBAAqBnB,MAAOnB,EAAE,4DAA6DsC,MAAO,iBACxRL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAASsB,GAAU1C,GACf,GAAIO,GAAQ/C,IAEZA,MAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,gEACftB,KAAK0C,KAAO,WAGZ1C,KAAK2C,SAAU,EAGf3C,KAAKsE,SAAW,GAAI1B,GAAS5C,MACzBuE,MAAO,IACPtB,OAAQ3B,EAAE,eACVoB,KAAM,eACNY,OAASb,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YAAenB,MAAOnB,EAAE,2DAA4DsC,MAAO,YACx4BL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAASuB,GAAU3C,GACf,GAAIO,GAAQ/C,IAEZA,MAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,oEACftB,KAAK0C,KAAO,WAGZ1C,KAAK2C,SAAU,EAGf3C,KAAKsE,SAAW,GAAI1B,GAAS5C,MACzBuE,MAAO,IACPtB,OAAQ3B,EAAE,cACVoB,KAAM,eACNY,OAASb,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YAAenB,MAAOnB,EAAE,+DAAgEsC,MAAO,YACh7BL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAASwB,GAAM5C,GACXxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,4FACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAuDnB,QAAS0C,GAAK7C,GACVxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,yFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAiInB,QAAS2C,GAAS9C,GACdxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,sFACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EA0FnB,QAAS4C,GAAM/C,GACXxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,+DACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAmVnB,QAAS6C,GAAMhD,GACXxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,6DACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAqEnB,QAAS8C,GAAMjD,GACXxC,KAAKwC,OAASA,EACdxC,KAAKyC,MAAQnB,EAAE,8DACftB,KAAK0C,KAAO,QAGZ1C,KAAK2C,SAAU,EAkPnB,QAAS+C,GAAMlD,GACXxC,KAAKwC,OAASA,EACdxC,KAAK2F,SA0HT,QAASC,GAAa7B,GAClB,GAAI8B,GAAgB9B,EAAE8B,eAAiB9B,EAAE+B,eAAiB/B,EAAE+B,cAAcD,cACtEE,MAAY,EAOhB,OALIA,GADiB,MAAjBF,EACYG,OAAOH,eAAiBG,OAAOH,cAAcI,QAAQ,QAErDJ,EAAcI,QAAQ,cAG/B3D,EAAkByD,GAI7B,QAASG,GAAanC,EAAGoC,GACrB,GAAIN,GAAgB9B,EAAE8B,eAAiB9B,EAAE+B,eAAiB/B,EAAE+B,cAAcD,cACtEE,MAAY,GACZK,MAAY,EAUhB,IATqB,MAAjBP,EACAE,EAAYC,OAAOH,eAAiBG,OAAOH,cAAcI,QAAQ,SAEjEF,EAAYF,EAAcI,QAAQ,cAClCG,EAAYP,EAAcI,QAAQ,eAEjCG,GAAaL,IACdK,EAAY,MAAQ9D,EAAkByD,GAAa,QAElDK,EAAL,CAKA,GAAIC,GAAeD,EAAUE,MAAM,UAgBnC,OAf4B,KAAxBD,EAAajF,SACbgF,EAAYC,EAAa,IAI7BD,EAAYA,EAAUnF,QAAQ,6BAA8B,IAIxDmF,EAFAD,EAEYC,EAAUnF,QAAQ,oCAAqC,IAGvDmF,EAAUnF,QAAQ,4BAA6B,KAOnE,QAASsF,GAAaxC,GAClB,GAAIlD,KAEJ,IADU+E,EAAa7B,GAGnB,MAAOlD,EAGX,IAAIgF,GAAgB9B,EAAE8B,eAAiB9B,EAAE+B,eAAiB/B,EAAE+B,cAAcD,kBACtEW,EAAQX,EAAcW,KAC1B,OAAKA,IAILjF,EAAWiF,EAAO,SAAU9E,EAAKkC,GAC7B,GAAIlB,GAAOkB,EAAMlB,IACb,UAAS+D,KAAK/D,IACd7B,EAAO6F,KAAK9C,EAAM+C,eAInB9F,GAVIA,EAkBf,QAAS+F,GAAKpE,GACVxC,KAAKwC,OAASA,EA4ZlB,QAASqE,GAAQrE,GACbxC,KAAKwC,OAASA,EAsGlB,QAASsE,GAAItE,GACTxC,KAAKwC,OAASA,EACdxC,KAAK+G,cAAgB,KA6KzB,QAASC,GAASxE,GACdxC,KAAKwC,OAASA,EACdxC,KAAKiH,MAAQ,EACbjH,KAAKkH,SAAU,EACflH,KAAKmH,WAAY,EACjBnH,KAAKoH,WAAa,EAClBpH,KAAKqH,eAAiB7E,EAAO8E,mBAC7BtH,KAAKuH,KAAOjG,EAAE,oCAgElB,QAASkG,GAAUhF,GACfxC,KAAKwC,OAASA,EA+RlB,QAASiF,GAAOC,EAAiBC,GAC7B,GAAuB,MAAnBD,EAEA,KAAM,IAAIE,OAAM,2BAGpB5H,MAAK6H,GAAK,cAAgBC,IAE1B9H,KAAK0H,gBAAkBA,EACvB1H,KAAK2H,aAAeA,EAGpB3H,KAAK+H,gBA7rHTjH,EAAWkH,WACPC,YAAanH,EAGb4C,QAAS,SAAiBjC,GACtB,GAAIJ,OAAI,EACR,KAAKA,EAAI,EAAGA,EAAIrB,KAAKoB,OAAQC,IAAK,CAC9B,GAAI6G,GAAOlI,KAAKqB,EAEhB,KAAe,IADFI,EAAGG,KAAKsG,EAAMA,EAAM7G,GAE7B,MAGR,MAAOrB,OAIXmI,IAAK,SAAaC,GACd,GAAIhH,GAASpB,KAAKoB,MAIlB,OAHIgH,IAAShH,IACTgH,GAAgBhH,GAEbE,EAAEtB,KAAKoI,KAIlBC,MAAO,WACH,MAAOrI,MAAKmI,IAAI,IAIpBG,KAAM,WACF,GAAIlH,GAASpB,KAAKoB,MAClB,OAAOpB,MAAKmI,IAAI/G,EAAS,IAI7B0C,GAAI,SAAYpB,EAAMjC,EAAUgB,GAEvBA,IACDA,EAAKhB,EACLA,EAAW,KAIf,IAAI8H,KAGJ,OAFAA,GAAQ7F,EAAK4D,MAAM,OAEZtG,KAAK0D,QAAQ,SAAUwE,GAC1BK,EAAM7E,QAAQ,SAAUhB,GACpB,GAAKA,EAIL,MAAKjC,OAOLyH,GAAKM,iBAAiB9F,EAAM,SAAUqB,GAClC,GAAI0E,GAAS1E,EAAE0E,MACXA,GAAOC,QAAQjI,IACfgB,EAAGG,KAAK6G,EAAQ1E,KAErB,OAVCmE,GAAKM,iBAAiB9F,EAAMjB,GAAI,QAgBhDkH,IAAK,SAAajG,EAAMjB,GACpB,MAAOzB,MAAK0D,QAAQ,SAAUwE,GAC1BA,EAAKU,oBAAoBlG,EAAMjB,GAAI,MAK3CoH,KAAM,SAAcnH,EAAKoH,GACrB,MAAW,OAAPA,EAEO9I,KAAK,GAAG+I,aAAarH,GAGrB1B,KAAK0D,QAAQ,SAAUwE,GAC1BA,EAAKc,aAAatH,EAAKoH,MAMnC1F,SAAU,SAAkB6F,GACxB,MAAKA,GAGEjJ,KAAK0D,QAAQ,SAAUwE,GAC1B,GAAIgB,OAAM,EACNhB,GAAKe,WAELC,EAAMhB,EAAKe,UAAU3C,MAAM,MAC3B4C,EAAMA,EAAIC,OAAO,SAAUpH,GACvB,QAASA,EAAKb,SAGdgI,EAAI/H,QAAQ8H,GAAa,GACzBC,EAAIxC,KAAKuC,GAGbf,EAAKe,UAAYC,EAAIE,KAAK,MAE1BlB,EAAKe,UAAYA,IAjBdjJ,MAuBfqJ,YAAa,SAAqBJ,GAC9B,MAAKA,GAGEjJ,KAAK0D,QAAQ,SAAUwE,GAC1B,GAAIgB,OAAM,EACNhB,GAAKe,YAELC,EAAMhB,EAAKe,UAAU3C,MAAM,MAC3B4C,EAAMA,EAAIC,OAAO,SAAUpH,GAGvB,UAFAA,EAAOA,EAAKb,SAECa,IAASkH,KAM1Bf,EAAKe,UAAYC,EAAIE,KAAK,QAhBvBpJ,MAsBfsJ,IAAK,SAAa5H,EAAKoH,GACnB,GAAIS,GAAe7H,EAAM,IAAMoH,EAAM,GACrC,OAAO9I,MAAK0D,QAAQ,SAAUwE,GAC1B,GAAIsB,IAAStB,EAAKa,aAAa,UAAY,IAAI7H,OAC3CuI,MAAW,GACXC,IACAF,IAEAC,EAAWD,EAAMlD,MAAM,KACvBmD,EAAS/F,QAAQ,SAAU3B,GAEvB,GAAImH,GAAMnH,EAAKuE,MAAM,KAAKqD,IAAI,SAAUtI,GACpC,MAAOA,GAAEH,QAEM,KAAfgI,EAAI9H,QACJsI,EAAUhD,KAAKwC,EAAI,GAAK,IAAMA,EAAI,MAI1CQ,EAAYA,EAAUC,IAAI,SAAU5H,GAChC,MAA0B,KAAtBA,EAAKZ,QAAQO,GACN6H,EAEAxH,IAGX2H,EAAUvI,QAAQoI,GAAgB,GAClCG,EAAUhD,KAAK6C,GAGnBrB,EAAKc,aAAa,QAASU,EAAUN,KAAK,QAG1ClB,EAAKc,aAAa,QAASO,MAMvCK,KAAM,WACF,MAAO5J,MAAKsJ,IAAI,UAAW,UAI/BpF,KAAM,WACF,MAAOlE,MAAKsJ,IAAI,UAAW,SAI/B/I,SAAU,WACN,GAAI2H,GAAOlI,KAAK,EAChB,OAAKkI,GAIE5G,EAAE4G,EAAK3H,UAHH,MAOf8C,OAAQ,SAAgBwG,GACpB,MAAO7J,MAAK0D,QAAQ,SAAUwE,GAC1B2B,EAAUnG,QAAQ,SAAUoG,GACxB5B,EAAK6B,YAAYD,QAM7BE,OAAQ,WACJ,MAAOhK,MAAK0D,QAAQ,SAAUwE,GAC1B,GAAIA,EAAK8B,OACL9B,EAAK8B,aACF,CACH,GAAIC,GAAS/B,EAAKgC,aAClBD,IAAUA,EAAOE,YAAYjC,OAMzCkC,UAAW,SAAmBC,GAC1B,GAAInC,GAAOlI,KAAK,GACZ8J,EAAQO,EAAO,EACnB,OAAOnC,GAAKoC,SAASR,IAIzBS,YAAa,WAET,MADWvK,MAAK,GACJwK,yBAIhBC,YAAa,WAET,MADWzK,MAAK,GACJ0K,UAIhBC,KAAM,SAAclK,GAEhB,MAAOa,GADItB,KAAK,GACFY,iBAAiBH,KAInCmK,KAAM,SAAc9B,GAChB,MAAKA,GAQM9I,KAAK0D,QAAQ,SAAUwE,GAC1BA,EAAK5H,UAAYwI,IAPV9I,KAAK,GACJM,UAAUW,QAAQ,SAAU,WACpC,MAAO,MAWnBf,KAAM,SAAc0D,GAChB,GAAIsE,GAAOlI,KAAK,EAChB,OAAa,OAAT4D,EACOsE,EAAK5H,WAEZ4H,EAAK5H,UAAYsD,EACV5D,OAKf8I,IAAK,WAED,MADW9I,MAAK,GACJ4D,MAAM1C,QAItB2J,MAAO,WACH,MAAO7K,MAAK0D,QAAQ,SAAUwE,GAC1BA,EAAK2C,WAKbZ,OAAQ,WAEJ,MAAO3I,GADItB,KAAK,GACFkK,gBAIlBY,YAAa,SAAqBrK,EAAUsK,GACxC,GAAIC,GAAU5K,SAASQ,iBAAiBH,GACpCW,EAAS4J,EAAQ5J,MACrB,KAAKA,EAED,MAAO,KAGX,IAAI8G,GAAO6C,GAAgB/K,KAAK,EAChC,IAAsB,SAAlBkI,EAAKwC,SACL,MAAO,KAGX,IAAIT,GAAS/B,EAAKgC,cACd7I,MAAI,EACR,KAAKA,EAAI,EAAGA,EAAID,EAAQC,IACpB,GAAI4I,IAAWe,EAAQ3J,GAEnB,MAAOC,GAAE2I,EAKjB,OAAOjK,MAAK8K,YAAYrK,EAAUwJ,IAItCgB,MAAO,SAAexI,GAClB,MAAuB,KAAnBA,EAAMzB,SACChB,KAAK,KAAOyC,EAEZzC,KAAK,KAAOyC,EAAM,IAKjCyI,aAAc,SAAsBzK,GAChC,GAAI0K,GAAiB7J,EAAEb,GACnB2K,EAAgBD,EAAe,EACnC,OAAKC,GAGEpL,KAAK0D,QAAQ,SAAUwE,GACbkD,EAAcC,WACpBH,aAAahD,EAAMkD,KAJnBpL,MASfsL,YAAa,SAAqB7K,GAC9B,GAAI0K,GAAiB7J,EAAEb,GACnB2K,EAAgBD,EAAe,EACnC,OAAKC,GAGEpL,KAAK0D,QAAQ,SAAUwE,GAC1B,GAAI+B,GAASmB,EAAcC,UACvBpB,GAAOsB,YAAcH,EAErBnB,EAAOF,YAAY7B,GAGnB+B,EAAOiB,aAAahD,EAAMkD,EAAcI,eATrCxL,MAwBnB,IAAIyL,IAGA9F,OAAQ,OAAQ,OAAQ,SAAU,YAAa,gBAAiB,YAAa,YAAa,OAAQ,OAAQ,UAAW,QAAS,WAAY,QAAS,QAAS,QAAS,OAAQ,OAAQ,QAarL+F,OAAQ,IAGRC,OAAO,EAGPC,kBAAkB,EASlBC,aAAa,EAGbC,iBAAkB,QAMlBC,qBAAqB,EAMrBC,eAAgB,GAGhBC,iBACIC,MAAO,eAIXC,oBAKAC,iBAAiB,EAGjBC,iBAAkB,IAGlBC,gBASIC,OAAQ,SAAgBC,EAAKhK,EAAQiK,KASrCC,QAAS,SAAiBF,EAAKhK,EAAQ3B,KAGvC8L,KAAM,SAAcH,EAAKhK,EAAQ3B,KAGjC+L,MAAO,SAAeJ,EAAKhK,KAG3BqK,QAAS,SAAiBL,EAAKhK,OAYnCsK,GACAC,IAAKC,UAAUC,UAGfC,SAAU,WAEN,MADU,UACCzG,KAAKzG,KAAK+M,MAIzBI,KAAM,WACF,MAAO,iBAAmBnH,SA8DlCzD,GAAKyF,WACDC,YAAa1F,EAGbgB,QAAS,SAAiBQ,GAGtB,GAAIvB,GAASxC,KAAKwC,OACd4K,EAAc5K,EAAO6K,UAAUC,kBAE/BF,IAEA5K,EAAO6K,UAAUE,mBAIrB/K,EAAOgL,IAAIC,GAAG,QAEVL,IAEA5K,EAAO6K,UAAUK,gBACjBlL,EAAO6K,UAAUM,qBAKzBC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOgL,IAAIK,kBAAkB,SAC7B7N,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,gBAS9B,IAAIlG,GAAc,SAAUX,EAAQsL,GAChC,GAAIC,GAAWvL,EAAOiJ,OAAOsC,aACzBlN,EAASiN,CAab,OAXAC,GAASrK,QAAQ,SAAU3B,GACvB,GAAIiM,GAAMjM,EAAKiM,IACXlF,EAAM/G,EAAK+G,GAEXkF,GAAIvH,KAAK5F,KACTA,EAASA,EAAOI,QAAQ+M,EAAK,WACzB,MAAOlF,QAKZjI,GAMP2C,EAAW,YAyEfZ,GAASoF,WACLC,YAAarF,EAGbgH,KAAM,WACE5J,KAAKgE,eAELiK,aAAajO,KAAKgE,cAGtB,IAAInB,GAAO7C,KAAK6C,KACZqL,EAAYrL,EAAKJ,MACjBO,EAAahD,KAAKgD,UACtB,KAAIhD,KAAKoE,MAAT,CAGA,GAAIpE,KAAKmE,UAELnB,EAAW4G,WACR,CAEH,GAAIuE,GAAaD,EAAU3D,cAAc6D,QAAU,EAC/C7J,EAAQvE,KAAK8C,IAAIyB,OAAS,GAC9BvB,GAAWsG,IAAI,aAAc6E,EAAa,MAAM7E,IAAI,QAAS/E,EAAQ,MAGrE2J,EAAU7K,OAAOL,GACjBhD,KAAKmE,WAAY,EAIrBnE,KAAKoE,OAAQ,IAIjBF,KAAM,WACElE,KAAKqO,eAELJ,aAAajO,KAAKqO,cAGtB,IAAIrL,GAAahD,KAAKgD,UACjBhD,MAAKoE,QAIVpB,EAAWkB,OACXlE,KAAKoE,OAAQ,KAgCrBC,EAAK2D,WACDC,YAAa5D,EAGbG,SAAU,SAAkBZ,GACxB,GAAIpB,GAASxC,KAAKwC,OAEd8L,EAAiB9L,EAAO6K,UAAUkB,2BAClC/L,GAAOgM,UAAUvD,MAAMqD,IAM3B9L,EAAOgL,IAAIC,GAAG,cAAe7J,IAIjCgK,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,MACbuL,EAAM,MACNS,EAAWjM,EAAOgL,IAAIkB,kBAAkB,cACxCV,GAAIvH,KAAKgI,IACTzO,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,gBAS9B,IAAIsF,GAAU,aAGVC,IASJnK,GAAMuD,WACFC,YAAaxD,EAGbmF,KAAM,WACF,GAAI7G,GAAQ/C,KAER6C,EAAO7C,KAAK6C,IAChB,MAAI+L,EAAqBzN,QAAQ0B,IAAS,GAA1C,CAKA,GAAIL,GAASK,EAAKL,OACdqM,EAAQvN,EAAE,QACVgG,EAAqB9E,EAAO8E,mBAC5BxE,EAAM9C,KAAK8C,IAGXE,EAAa1B,EAAE,2CACfiD,EAAQzB,EAAIyB,OAAS,GACzBvB,GAAWsG,IAAI,QAAS/E,EAAQ,MAAM+E,IAAI,eAAgB,EAAI/E,GAAS,EAAI,KAG3E,IAAIuK,GAAYxN,EAAE,iDAClB0B,GAAWK,OAAOyL,GAClBA,EAAUhL,GAAG,QAAS,WAClBf,EAAMmB,QAIV,IAAI6K,GAAqBzN,EAAE,yCACvB0N,EAAuB1N,EAAE,4CAC7B0B,GAAWK,OAAO0L,GAAoB1L,OAAO2L,EAG7C,IAAIZ,GAAStL,EAAIsL,MACbA,IACAY,EAAqB1F,IAAI,SAAU8E,EAAS,MAAM9E,IAAI,aAAc,OAIxE,IAAI2F,GAAOnM,EAAImM,SACXC,KACAC,IACJF,GAAKvL,QAAQ,SAAU0L,EAAKC,GACxB,GAAKD,EAAL,CAGA,GAAIE,GAAQF,EAAIE,OAAS,GACrBC,EAAMH,EAAIG,KAAO,EAGrBD,GAAQnM,EAAYX,EAAQ8M,GAC5BC,EAAMpM,EAAYX,EAAQ+M,EAG1B,IAAItM,GAAS3B,EAAE,wBAA0BgO,EAAQ,QACjDP,GAAmB1L,OAAOJ,EAC1B,IAAIuM,GAAWlO,EAAEiO,EACjBP,GAAqB3L,OAAOmM,GAG5BvM,EAAOwM,OAASJ,EAChBH,EAAYxI,KAAKzD,GACjBkM,EAAczI,KAAK8I,GAGF,IAAbH,GACApM,EAAON,SAAU,EACjBM,EAAOG,SAAS,eAEhBoM,EAAStL,OAIbjB,EAAOa,GAAG,QAAS,SAAUC,GACrBd,EAAON,UAIXuM,EAAYxL,QAAQ,SAAUT,GAC1BA,EAAON,SAAU,EACjBM,EAAOoG,YAAY,gBAEvB8F,EAAczL,QAAQ,SAAU8L,GAC5BA,EAAStL,SAIbjB,EAAON,SAAU,EACjBM,EAAOG,SAAS,cAChBoM,EAAS5F,aAKjB5G,EAAWc,GAAG,QAAS,SAAUC,GAE7BA,EAAE2L,oBAENb,EAAM/K,GAAG,QAAS,SAAUC,GACxBhB,EAAMmB,SAIVoD,EAAmBjE,OAAOL,GAG1BiM,EAAKvL,QAAQ,SAAU0L,EAAKhH,GACxB,GAAKgH,EAAL,EAGaA,EAAIO,YACVjM,QAAQ,SAAUkM,GACrB,GAAInP,GAAWmP,EAAMnP,SACjBiC,EAAOkN,EAAMlN,KACbjB,EAAKmO,EAAMnO,IAAMkN,CACNQ,GAAc/G,GACpBuC,KAAKlK,GAAUqD,GAAGpB,EAAM,SAAUqB,GACvCA,EAAE2L,kBACejO,EAAGsC,IAGhBhB,EAAMmB,aAOtB,IAAI2L,GAAU7M,EAAW2H,KAAK,4BAC1BkF,GAAQzO,QACRyO,EAAQ1H,IAAI,GAAG0C,QAInB7K,KAAKgD,WAAaA,EAGlBhD,KAAK8P,mBAELlB,EAAqBlI,KAAK7D,KAI9BqB,KAAM,WACF,GAAIrB,GAAO7C,KAAK6C,KACZG,EAAahD,KAAKgD,UAClBA,IACAA,EAAWgH,SAIf4E,EAAuBA,EAAqBzF,OAAO,SAAUpH,GACzD,MAAIA,KAASc,KASrBiN,iBAAkB,WACTlB,EAAqBxN,QAG1BwN,EAAqBlL,QAAQ,SAAUb,GACnC,GAAIkN,GAAQlN,EAAKkN,SACbA,GAAM7L,MACN6L,EAAM7L,WAoBtBQ,EAAKsD,WACDC,YAAavD,EAGbnB,QAAS,SAAiBQ,GACtB,GAAIvB,GAASxC,KAAKwC,OACdwN,MAAY,EAEhB,IAAIhQ,KAAK2C,QAAS,CAGd,KADAqN,EAAYxN,EAAO6K,UAAUkB,6BAEzB,MAGJ/L,GAAO6K,UAAU4C,kBAAkBD,GACnCxN,EAAO6K,UAAUM,mBAEjB3N,KAAKkQ,aAAaF,EAAUpF,OAAQoF,EAAUnH,KAAK,aAG/CrG,GAAO6K,UAAUC,mBAEjBtN,KAAKkQ,aAAa,GAAI,IAGtBlQ,KAAKkQ,aAAa1N,EAAO6K,UAAU8C,mBAAoB,KAMnED,aAAc,SAAsBtF,EAAMwF,GACtC,GAAIrN,GAAQ/C,KAGRqQ,EAAcrO,EAAU,cACxBsO,EAActO,EAAU,cACxBuO,EAAUvO,EAAU,UACpBwO,EAAWxO,EAAU,WAGrByO,EAAgBzQ,KAAK2C,QAAU,eAAiB,OAGhDoN,EAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IAEP0K,OAEIK,MAAO,KAEPC,IAAK,iDAAmDe,EAAc,sCAAwC1F,EAAO,uEAA6FyF,EAAc,sCAAwCD,EAAO,kJAAoJG,EAAU,4EAAwFC,EAAW,uCAAyCC,EAAgB,sFAEzkBd,SAGIlP,SAAU,IAAM8P,EAChB7N,KAAM,QACNjB,GAAI,WAEA,GAAIiP,GAAQpP,EAAE,IAAM+O,GAChBM,EAAQrP,EAAE,IAAMgP,GAChBF,EAAOM,EAAM5H,MACb8B,EAAO+F,EAAM7H,KAIjB,OAHA/F,GAAM6N,YAAYhG,EAAMwF,IAGjB,KAKX3P,SAAU,IAAM+P,EAChB9N,KAAM,QACNjB,GAAI,WAKA,MAHAsB,GAAM8N,YAGC,QAQvBd,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjBc,SAAU,WACN,GAAK7Q,KAAK2C,QAAV,CAGA,GAAIH,GAASxC,KAAKwC,MAElB,IADqBA,EAAO6K,UAAUkB,4BACtC,CAGA,GAAIuC,GAAgBtO,EAAO6K,UAAU8C,kBACrC3N,GAAOgL,IAAIC,GAAG,aAAc,SAAWqD,EAAgB,cAI3DF,YAAa,SAAqBhG,EAAMwF,GACpC,GAAKxF,GAASwF,EAAd,CAGapQ,KAAKwC,OACXgL,IAAIC,GAAG,aAAc,YAAc2C,EAAO,qBAAuBxF,EAAO,UAInFgD,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,MACbsO,EAAiBvO,EAAO6K,UAAUkB,2BACjCwC,KAGgC,MAAjCA,EAAetG,eACfzK,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,kBAmB9B1E,EAAOqD,WACHC,YAAatD,EAGbpB,QAAS,SAAiBQ,GAGtB,GAAIvB,GAASxC,KAAKwC,OACd4K,EAAc5K,EAAO6K,UAAUC,kBAE/BF,IAEA5K,EAAO6K,UAAUE,mBAIrB/K,EAAOgL,IAAIC,GAAG,UAEVL,IAEA5K,EAAO6K,UAAUK,gBACjBlL,EAAO6K,UAAUM,qBAKzBC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOgL,IAAIK,kBAAkB,WAC7B7N,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,iBAmB9BzE,EAAKoD,WACDC,YAAarD,EAGbrB,QAAS,SAAiBQ,GAGT/D,KAAKwC,OAGXgL,IAAIC,GAAG,UAkBtB5I,EAAcmD,WACVC,YAAapD,EAGbtB,QAAS,SAAiBQ,GAGtB,GAAIvB,GAASxC,KAAKwC,OACd4K,EAAc5K,EAAO6K,UAAUC,kBAE/BF,IAEA5K,EAAO6K,UAAUE,mBAIrB/K,EAAOgL,IAAIC,GAAG,iBAEVL,IAEA5K,EAAO6K,UAAUK,gBACjBlL,EAAO6K,UAAUM,qBAKzBC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOgL,IAAIK,kBAAkB,kBAC7B7N,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,iBAmB9BvE,EAAUkD,WACNC,YAAanD,EAGbvB,QAAS,SAAiBQ,GAGtB,GAAIvB,GAASxC,KAAKwC,OACd4K,EAAc5K,EAAO6K,UAAUC,kBAE/BF,IAEA5K,EAAO6K,UAAUE,mBAIrB/K,EAAOgL,IAAIC,GAAG,aAEVL,IAEA5K,EAAO6K,UAAUK,gBACjBlL,EAAO6K,UAAUM,qBAKzBC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOgL,IAAIK,kBAAkB,cAC7B7N,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,iBAmB9BtE,EAAKiD,WACDC,YAAalD,EAGbxB,QAAS,SAAiBQ,GAGT/D,KAAKwC,OAGXgL,IAAIC,GAAG,UAgCtBzI,EAAKgD,WACDC,YAAajD,EAGbR,SAAU,SAAkBZ,GACxB,GAAIpB,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SAEvB,IADAhM,EAAO6K,UAAUM,oBACbnL,EAAOgL,IAAIK,kBAAkBjK,GAAjC,CAGApB,EAAOgL,IAAIC,GAAG7J,EAGd,IAAI0K,GAAiB9L,EAAO6K,UAAUkB,2BAItC,IAHqC,OAAjCD,EAAe7D,gBACf6D,EAAiBA,EAAerE,WAEkB,IAAlD,WAAWxD,KAAK6H,EAAe7D,iBAG/B6D,EAAerD,MAAMuD,GAAzB,CAIA,GAAIwC,GAAU1C,EAAerE,QACzB+G,GAAQ/F,MAAMuD,KAKlBF,EAAehD,YAAY0F,GAC3BA,EAAQhH,aAIZ4D,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOgL,IAAIK,kBAAkB,wBAA0BrL,EAAOgL,IAAIK,kBAAkB,sBACpF7N,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,iBAiC9BpE,EAAQ+C,WACJC,YAAahD,EAGbT,SAAU,SAAkBZ,GACX5D,KAAKwC,OACXgL,IAAIC,GAAG7J,KAgCtBsB,EAAU8C,WACNC,YAAa/C,EAGbV,SAAU,SAAkBZ,GACX5D,KAAKwC,OACXgL,IAAIC,GAAG,YAAa7J,KAgCnCuB,EAAU6C,WACNC,YAAa9C,EAGbX,SAAU,SAAkBZ,GACX5D,KAAKwC,OACXgL,IAAIC,GAAG,YAAa7J,KAkBnCwB,EAAM4C,WACFC,YAAa7C,EAEb7B,QAAS,SAAiBQ,GACtB,GAAIvB,GAASxC,KAAKwC,MAClB,KAAKsK,EAAGK,OAEJ,WADA3K,GAAOgL,IAAIC,GAAG,cAAe,eAMjC,IAAIa,GAAiB9L,EAAO6K,UAAUkB,4BAClC0C,MAAU,GACVC,MAAc,EAClB,IAAqC,MAAjC5C,EAAe7D,cAMf,MAJAwG,GAAU3C,EAAe1D,OACzBsG,EAAc5P,EAAE,eAAiB2P,EAAU,iBAC3CC,EAAY5F,YAAYgD,OACxBA,GAAetE,QAGkB,gBAAjCsE,EAAe7D,gBAEfwG,EAAU3C,EAAe1D,OACzBsG,EAAc5P,EAAE,MAAQ2P,EAAU,QAClCC,EAAY5F,YAAYgD,GACxBA,EAAetE,WAIvB4D,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,MACbuL,EAAM,gBACNS,EAAWjM,EAAOgL,IAAIkB,kBAAkB,cACxCV,GAAIvH,KAAKgI,IACTzO,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,iBAmB9BhE,EAAK2C,WACDC,YAAa5C,EAEb9B,QAAS,SAAiBQ,GACtB,GAAIvB,GAASxC,KAAKwC,OACd2O,EAAa3O,EAAO6K,UAAU+D,wBAC9BC,EAAW7O,EAAO6K,UAAUiE,sBAC5BlE,EAAc5K,EAAO6K,UAAUC,mBAC/BwD,EAAgBtO,EAAO6K,UAAU8C,mBACjCoB,MAAQ,EAEZ,OAAKJ,GAAWlG,MAAMoG,GAKjBjE,OAUDpN,KAAK2C,QAEL3C,KAAKkQ,aAAaiB,EAAWjR,QAG7BF,KAAKkQ,iBAbLqB,EAAQjQ,EAAE,SAAWwP,EAAgB,WACrCtO,EAAOgL,IAAIC,GAAG,aAAc8D,GAC5B/O,EAAO6K,UAAU4C,kBAAkBsB,GAAO,OAC1C/O,GAAO6K,UAAUM,wBARjBnL,GAAO6K,UAAUM,oBAsBzBuC,aAAc,SAAsBtM,GAChC,GAAIb,GAAQ/C,IAGZ4D,GAAQA,GAAS,EACjB,IAAIlB,GAAQkB,EAAgB,OAAR,MAChB4N,EAASxP,EAAU,SACnByP,EAAQzP,EAAU,OAElB+N,EAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IAEP0K,OAEIK,MAAO,OAEPC,IAAK,gDAAkDiC,EAAS,4BAA8B5N,EAAQ,oHAAsH6N,EAAQ,yFAEpO9B,SAGIlP,SAAU,IAAMgR,EAChB/O,KAAM,QACNjB,GAAI,WACA,GAAIkP,GAAQrP,EAAE,IAAMkQ,GAChB5G,EAAO+F,EAAM7H,OAAS6H,EAAMzQ,MAWhC,OAVA0K,GAAOtI,EAAkBsI,GACZ,QAATlI,EAEAK,EAAM2O,YAAY9G,GAGlB7H,EAAM4O,YAAY/G,IAIf,QAQvBmF,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjB2B,YAAa,SAAqB9N,GACjB5D,KAAKwC,OACXgL,IAAIC,GAAG,aAAc,cAAgB7J,EAAQ,6BAIxD+N,YAAa,SAAqB/N,GAC9B,GAAIpB,GAASxC,KAAKwC,OACduO,EAAiBvO,EAAO6K,UAAUkB,2BACjCwC,KAGLA,EAAe7Q,KAAK0D,GACpBpB,EAAO6K,UAAUM,qBAIrBC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,MACbsO,EAAiBvO,EAAO6K,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAIa,GAAcb,EAAe9G,QACI,UAAjC8G,EAAetG,eAA0D,QAA9BmH,EAAYnH,eACvDzK,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,kBAmB9B/D,EAAS0C,WACLC,YAAa3C,EAEb/B,QAAS,WACLvD,KAAKkQ,gBAGTA,aAAc,WACV,GAAInN,GAAQ/C,KAGR6R,EAAW,EACD,kMACNvL,MAAM,MAAM5C,QAAQ,SAAU3B,GAC9BA,IACA8P,GAAY,0BAA4B9P,EAAO,YAIvD,IAAI+P,GAAW,EACD,+DACNxL,MAAM,MAAM5C,QAAQ,SAAU3B,GAC9BA,IACA+P,GAAY,0BAA4B/P,EAAO,YAIvD,IAAIgO,GAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IACP6J,OAAQ,IAERa,OAEIK,MAAO,KAEPC,IAAK,uCAAyCsC,EAAW,SAEzDlC,SACIlP,SAAU,gBACViC,KAAM,QACNjB,GAAI,SAAYsC,GACZ,GAAI0E,GAAS1E,EAAE0E,MAGf,OAFA1F,GAAMgP,QAAQtJ,EAAOnI,YAEd,OAMfgP,MAAO,KAEPC,IAAK,uCAAyCuC,EAAW,SAEzDnC,SACIlP,SAAU,gBACViC,KAAM,QACNjB,GAAI,SAAYsC,GACZ,GAAI0E,GAAS1E,EAAE0E,MAGf,OAFA1F,GAAMgP,QAAQtJ,EAAOnI,YAEd,QAQvByP,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjBgC,QAAS,SAAiBC,GACThS,KAAKwC,OACXgL,IAAIC,GAAG,aAAc,SAAWuE,EAAQ,aAkBvDzM,EAAMyC,WACFC,YAAa1C,EAEbhC,QAAS,WACDvD,KAAK2C,QAEL3C,KAAKiS,mBAGLjS,KAAKkS,sBAKbA,mBAAoB,WAChB,GAAInP,GAAQ/C,KAGRmS,EAAcnQ,EAAU,OACxBoQ,EAAapQ,EAAU,OACvBqQ,EAAarQ,EAAU,OAEvB+N,EAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IAEP0K,OAEIK,MAAO,OAEPC,IAAK,sJAAkK6C,EAAa,0IAAiJC,EAAa,wOAA8PF,EAAc,0FAE9lBxC,SAEIlP,SAAU,IAAM0R,EAChBzP,KAAM,QACNjB,GAAI,WACA,GAAI6Q,GAASC,SAASjR,EAAE,IAAM8Q,GAAYtJ,OACtC0J,EAASD,SAASjR,EAAE,IAAM+Q,GAAYvJ,MAQ1C,OANIwJ,IAAUE,GAAUF,EAAS,GAAKE,EAAS,GAE3CzP,EAAMgP,QAAQO,EAAQE,IAInB,QAQvBzC,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjBgC,QAAS,SAAiBO,EAAQE,GAE9B,GAAIC,OAAI,GACJC,MAAI,GACJxS,EAAO,iEACX,KAAKuS,EAAI,EAAGA,EAAIH,EAAQG,IAAK,CAEzB,GADAvS,GAAQ,OACE,IAANuS,EACA,IAAKC,EAAI,EAAGA,EAAIF,EAAQE,IACpBxS,GAAQ,sBAGZ,KAAKwS,EAAI,EAAGA,EAAIF,EAAQE,IACpBxS,GAAQ,iBAGhBA,IAAQ,QAEZA,GAAQ,qBAGR,IAAIsC,GAASxC,KAAKwC,MAClBA,GAAOgL,IAAIC,GAAG,aAAcvN,GAG5BsC,EAAOgL,IAAIC,GAAG,wBAAwB,GACtCjL,EAAOgL,IAAIC,GAAG,4BAA4B,IAI9CwE,iBAAkB,WACd,GAAIU,GAAS3S,KAGT4S,EAAc5Q,EAAU,WACxB6Q,EAAc7Q,EAAU,WACxB8Q,EAAc9Q,EAAU,WACxB+Q,EAAc/Q,EAAU,WACxBgR,EAAgBhR,EAAU,YAGlB,IAAIyC,GAAMzE,MAClBuE,MAAO,IAEP0K,OAEIK,MAAO,OAEPC,IAAK,4LAA8LqD,EAAc,wEAAyFE,EAAc,4EAA6FD,EAAc,wEAAyFE,EAAc,wKAAyLC,EAAgB,+FAEntBrD,SAEIlP,SAAU,IAAMmS,EAChBlQ,KAAM,QACNjB,GAAI,WAGA,MAFAkR,GAAOM,WAEA,KAIXxS,SAAU,IAAMoS,EAChBnQ,KAAM,QACNjB,GAAI,WAGA,MAFAkR,GAAOO,WAEA,KAIXzS,SAAU,IAAMqS,EAChBpQ,KAAM,QACNjB,GAAI,WAGA,MAFAkR,GAAOQ,WAEA,KAIX1S,SAAU,IAAMsS,EAChBrQ,KAAM,QACNjB,GAAI,WAGA,MAFAkR,GAAOS,WAEA,KAIX3S,SAAU,IAAMuS,EAChBtQ,KAAM,QACNjB,GAAI,WAGA,MAFAkR,GAAOU,aAEA,SAMjBzJ,QAIV0J,iBAAkB,WACd,GAAIzS,MACA2B,EAASxC,KAAKwC,OACduO,EAAiBvO,EAAO6K,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAIrG,GAAWqG,EAAetG,aAC9B,IAAiB,OAAbC,GAAkC,OAAbA,EAAzB,CAKA,GAAI6I,GAAMxC,EAAe9G,SACrBuJ,EAAOD,EAAIhT,WACXkT,EAAWD,EAAKpS,MACpBoS,GAAK9P,QAAQ,SAAUgQ,EAAItL,GACvB,GAAIsL,IAAO3C,EAAe,GAOtB,MALAlQ,GAAO6S,IACHtL,MAAOA,EACPF,KAAMwL,EACNtS,OAAQqS,IAEL,GAKf,IAAIE,GAASJ,EAAItJ,SACb2J,EAAOD,EAAOpT,WACdsT,EAAWD,EAAKxS,MAcpB,OAbAwS,GAAKlQ,QAAQ,SAAUoQ,EAAI1L,GACvB,GAAI0L,IAAOP,EAAI,GAOX,MALA1S,GAAOiT,IACH1L,MAAOA,EACPF,KAAM4L,EACN1S,OAAQyS,IAEL,IAKRhT,KAIXoS,QAAS,WAEL,GAAIc,GAAe/T,KAAKsT,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBG,EAAa3S,EAAE0S,EAAO9L,MACtBgM,EAASH,EAAaL,GACtBD,EAAWS,EAAO9S,OAGlB+S,EAAQ/T,SAASC,cAAc,MAC/BkP,EAAM,GACNlO,MAAI,EACR,KAAKA,EAAI,EAAGA,EAAIoS,EAAUpS,IACtBkO,GAAO,iBAEX4E,GAAM7T,UAAYiP,EAElBjO,EAAE6S,GAAO7I,YAAY2I,KAIzBf,QAAS,WAEL,GAAIa,GAAe/T,KAAKsT,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBI,EAASH,EAAaL,GACtBU,EAAUF,EAAO9L,KACJ9G,GAAE0S,EAAO9L,MACC+B,SACN1J,WAGhBmD,QAAQ,SAAUoQ,GACnB,GAAIP,GAAMjS,EAAEwS,GACRN,EAAOD,EAAIhT,WACX8T,EAAab,EAAKrL,IAAIiM,GACtBE,EAAOD,EAAW5J,cAAc8J,aAIpCjT,GADYlB,SAASC,cAAciU,IAC1BhJ,YAAY+I,OAK7BlB,QAAS,WAEL,GAAIY,GAAe/T,KAAKsT,kBACxB,IAAKS,EAAL,CAIiBzS,EADJyS,EAAaD,GACA5L,MACf8B,WAIfoJ,QAAS,WAEL,GAAIW,GAAe/T,KAAKsT,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBI,EAASH,EAAaL,GACtBU,EAAUF,EAAO9L,KACJ9G,GAAE0S,EAAO9L,MACC+B,SACN1J,WAGhBmD,QAAQ,SAAUoQ,GACTxS,EAAEwS,GACGvT,WACO4H,IAAIiM,GAEfpK,aAKnBqJ,UAAW,WACP,GAAI7Q,GAASxC,KAAKwC,OACduO,EAAiBvO,EAAO6K,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAIyD,GAASzD,EAAejG,YAAY,QACnC0J,IAGLA,EAAOxK,WAIX4D,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,MACbsO,EAAiBvO,EAAO6K,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAIrG,GAAWqG,EAAetG,aACb,QAAbC,GAAkC,OAAbA,GACrB1K,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,kBAmB9B7D,EAAMwC,WACFC,YAAazC,EAEbjC,QAAS,WACLvD,KAAKkQ,gBAGTA,aAAc,WACV,GAAInN,GAAQ/C,KAGRyU,EAAYzS,EAAU,YACtByP,EAAQzP,EAAU,OAGlB+N,EAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IAEP0K,OAEIK,MAAO,OAEPC,IAAK,6CAA+CkF,EAAY,mLAAyMhD,EAAQ,0FAEjR9B,SACIlP,SAAU,IAAMgR,EAChB/O,KAAM,QACNjB,GAAI,WACA,GAAIkP,GAAQrP,EAAE,IAAMmT,GAChB3L,EAAM6H,EAAM7H,MAAM5H,MAWtB,OANI4H,IAEA/F,EAAMgP,QAAQjJ,IAIX,QAQvBiH,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjBgC,QAAS,SAAiBjJ,GACT9I,KAAKwC,OACXgL,IAAIC,GAAG,aAAc3E,EAAM,iBAkB1CrD,EAAMuC,WACFC,YAAaxC,EAEblC,QAAS,WACDvD,KAAK2C,QACL3C,KAAKiS,mBAELjS,KAAKkS,sBAIbD,iBAAkB,WACd,GAAIzP,GAASxC,KAAKwC,OAGdkS,EAAU1S,EAAU,YACpB2S,EAAU3S,EAAU,YACpB4S,EAAW5S,EAAU,aACrB6S,EAAS7S,EAAU,WAGnB8S,IACAxF,MAAO,OACPC,IAAK,mSAA8TmF,EAAU,oEAAsEC,EAAU,oEAAsEC,EAAW,yJAA2JC,EAAS,uFAClpBlF,SACIlP,SAAU,IAAMiU,EAChBhS,KAAM,QACNjB,GAAI,WACA,GAAIsT,GAAOvS,EAAOwS,YAKlB,OAJID,IACAA,EAAKzL,IAAI,YAAa,QAGnB,KAGX7I,SAAU,IAAMkU,EAChBjS,KAAM,QACNjB,GAAI,WACA,GAAIsT,GAAOvS,EAAOwS,YAKlB,OAJID,IACAA,EAAKzL,IAAI,YAAa,QAGnB,KAGX7I,SAAU,IAAMmU,EAChBlS,KAAM,QACNjB,GAAI,WACA,GAAIsT,GAAOvS,EAAOwS,YAKlB,OAJID,IACAA,EAAKzL,IAAI,YAAa,SAGnB,KAGX7I,SAAU,IAAMoU,EAChBnS,KAAM,QACNjB,GAAI,WACA,GAAIsT,GAAOvS,EAAOwS,YAKlB,OAJID,IACAA,EAAK/K,UAGF,OAMf+F,EAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IACP0K,KAAM6F,GAEV/E,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAGjBmC,mBAAoB,WAChB,GAAI1P,GAASxC,KAAKwC,OACdyS,EAAYzS,EAAOyS,UACnBxJ,EAASjJ,EAAOiJ,OAGhByJ,EAAclT,EAAU,cACxBmT,EAAWnT,EAAU,WACrBoT,EAAYpT,EAAU,YACtBqT,EAAYrT,EAAU,YAGtB8S,IACAxF,MAAO,OACPC,IAAK,oEAAsE2F,EAAc,oMAAsMC,EAAW,sJAC1SxF,SAEIlP,SAAU,IAAMyU,EAChBxS,KAAM,QACNjB,GAAI,WACA,GAAI6T,GAAQhU,EAAE,IAAM6T,GAChBI,EAAWD,EAAM,EACrB,KAAIC,EAIA,OAAO,CAHPA,GAASC,WAQjB/U,SAAU,IAAM0U,EAChBzS,KAAM,SACNjB,GAAI,WACA,GAAI6T,GAAQhU,EAAE,IAAM6T,GAChBI,EAAWD,EAAM,EACrB,KAAKC,EAED,OAAO,CAIX,IAAIE,GAAWF,EAAS9I,KAMxB,OALIgJ,GAASrU,QACT6T,EAAUA,UAAUQ,IAIjB,OAKfnG,MAAO,OACPC,IAAK,yCAA2C6F,EAAY,sJAA4KC,EAAY,kFACpP1F,SACIlP,SAAU,IAAM4U,EAChB3S,KAAM,QACNjB,GAAI,WACA,GAAIiU,GAAWpU,EAAE,IAAM8T,GACnBO,EAAMD,EAAS5M,MAAM5H,MAOzB,OALIyU,IACAV,EAAUW,cAAcD,IAIrB,OAOfE,MACCpK,EAAOM,qBAAuBN,EAAOqK,iBAAmBrK,EAAOsK,kBAAoB/P,OAAOgQ,YAE3FH,EAAiBnP,KAAKoO,EAAW,IAEjCrJ,EAAOI,aAEPgK,EAAiBnP,KAAKoO,EAAW,GAIrC,IAAI/E,GAAQ,GAAItL,GAAMzE,MAClBuE,MAAO,IACP0K,KAAM4G,GAEV9F,GAAMnG,OAGN5J,KAAK+P,MAAQA,GAIjBnC,gBAAiB,SAAyB7J,GACtC,GAAIvB,GAASxC,KAAKwC,OACdC,EAAQzC,KAAKyC,KACbD,GAAOwS,cACPhV,KAAK2C,SAAU,EACfF,EAAMW,SAAS,gBAEfpD,KAAK2C,SAAU,EACfF,EAAM4G,YAAY,gBAU9B,IAAI4M,KAEJA,GAAiBC,KAAO3T,EAExB0T,EAAiBE,KAAO9R,EAExB4R,EAAiB7F,KAAO1L,EAExBuR,EAAiBG,OAASzR,EAE1BsR,EAAiBI,KAAOzR,EAExBqR,EAAiBK,cAAgBzR,EAEjCoR,EAAiBM,UAAYzR,EAE7BmR,EAAiBO,KAAOzR,EAExBkR,EAAiB3S,KAAO0B,EAExBiR,EAAiBQ,QAAUxR,EAE3BgR,EAAiBS,UAAYxR,EAE7B+Q,EAAiBU,UAAYxR,EAE7B8Q,EAAiBW,MAAQxR,EAEzB6Q,EAAiBY,KAAOxR,EAExB4Q,EAAiBa,SAAWxR,EAE5B2Q,EAAiBc,MAAQxR,EAEzB0Q,EAAiBe,MAAQxR,EAEzByQ,EAAiBgB,MAAQxR,EAYzBC,EAAMsC,WACFC,YAAavC,EAGbwR,KAAM,WACF,GAAInU,GAAQ/C,KAERwC,EAASxC,KAAKwC,SACLA,EAAOiJ,YACK9F,WAGbjC,QAAQ,SAAUyT,GAC1B,GAAIC,GAAkBnB,EAAiBkB,EACnCC,IAA8C,kBAApBA,KAE1BrU,EAAM4C,MAAMwR,GAAW,GAAIC,GAAgB5U,MAKnDxC,KAAKqX,gBAGLrX,KAAKsX,cAITD,cAAe,WACX,GAAI7U,GAASxC,KAAKwC,OACd+U,EAAe/U,EAAO+U,aACtB5R,EAAQ3F,KAAK2F,MACb8F,EAASjJ,EAAOiJ,OAEhBC,EAASD,EAAOC,OAAS,CAC7BnK,GAAWoE,EAAO,SAAUjE,EAAKmB,GAC7B,GAAIJ,GAAQI,EAAKJ,KACbA,KAEAA,EAAM6G,IAAI,UAAWoC,GACrB6L,EAAalU,OAAOZ,OAMhC6U,WAAY,WACR,GAAI3R,GAAQ3F,KAAK2F,MACbnD,EAASxC,KAAKwC,MAClBjB,GAAWoE,EAAO,SAAUjE,EAAKmB,GAC7B,GAAIH,GAAOG,EAAKH,IAChB,IAAKA,EAAL,CAGA,GAAID,GAAQI,EAAKJ,MACb6B,EAAWzB,EAAKyB,QACRzB,GAAKkN,KAGJ,WAATrN,GAAoBG,EAAKU,SACzBd,EAAMqB,GAAG,QAAS,SAAUC,GACW,MAA/BvB,EAAO6K,UAAUmK,YAGrB3U,EAAKU,QAAQQ,KAKR,aAATrB,GAAuB4B,GACvB7B,EAAMqB,GAAG,aAAc,SAAUC,GACM,MAA/BvB,EAAO6K,UAAUmK,aAIrBlT,EAAS+J,cAAgBpK,WAAW,WAChCK,EAASsF,QACV,QACJ9F,GAAG,aAAc,SAAUC,GAE1BO,EAASN,cAAgBC,WAAW,WAChCK,EAASJ,QACV,KAKE,UAATxB,GAAoBG,EAAKU,SACzBd,EAAMqB,GAAG,QAAS,SAAUC,GACxBA,EAAE2L,kBACiC,MAA/BlN,EAAO6K,UAAUmK,YAIrB3U,EAAKU,QAAQQ;iDAO7B0T,aAAc,WAEVlW,EADYvB,KAAK2F,MACC,SAAUjE,EAAKmB,GACzBA,EAAK+K,iBACL3J,WAAW,WACPpB,EAAK+K,mBACN,SAgGnBhH,EAAKoB,WACDC,YAAarB,EAGbsQ,KAAM,WAEFlX,KAAKsX,cAITI,MAAO,WACH1X,KAAKE,KAAK,gBAIdA,KAAM,SAAc4I,GAChB,GAAItG,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SACvB,IAAW,MAAP1F,EACA,MAAO0F,GAAUtO,MAEjBsO,GAAUtO,KAAK4I,GAGftG,EAAOmV,iBAKf/M,KAAM,SAAc9B,GAChB,GAAItG,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SACvB,IAAW,MAAP1F,EACA,MAAO0F,GAAU5D,MAEjB4D,GAAU5D,KAAK,MAAQ9B,EAAM,QAG7BtG,EAAOmV,iBAKftU,OAAQ,SAAgBnD,GACpB,GAAIsC,GAASxC,KAAKwC,MACFA,GAAOgM,UACbnL,OAAO/B,EAAEpB,IAGnBsC,EAAOmV,iBAIXL,WAAY,WAERtX,KAAK4X,qBAGL5X,KAAK6X,kBAGL7X,KAAK8X,eAGL9X,KAAK+X,eAGL/X,KAAKgY,aAGLhY,KAAKiY,cAITL,mBAAoB,WAKhB,QAASM,GAAUnU,GAEfvB,EAAO6K,UAAU6K,YAEjB1V,EAAOmD,MAAM8R,eARjB,GAAIjV,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SAUvBA,GAAU1K,GAAG,QAASoU,GACtB1J,EAAU1K,GAAG,YAAa,SAAUC,GAEhCyK,EAAU1K,GAAG,aAAcoU,KAE/B1J,EAAU1K,GAAG,UAAW,SAAUC,GAC9BmU,IAEA1J,EAAU7F,IAAI,aAAcuP,MAKpCL,gBAAiB,WAKb,QAASM,GAAQpU,GACb,GAAIuK,GAAiB9L,EAAO6K,UAAUkB,2BAEtC,IADkBD,EAAerE,SAChBgB,MAAMuD,KAKN,MADFF,EAAe7D,gBAM1B6D,EAAe1D,QAAnB,CAMA,GAAIwN,GAAK9W,EAAE,cACX8W,GAAGlN,aAAaoD,GAChB9L,EAAO6K,UAAU4C,kBAAkBmI,GAAI,GACvC5V,EAAO6K,UAAUM,mBACjBW,EAAetE,UAanB,QAASqO,GAAWtU,GAChB,GAAIuK,GAAiB9L,EAAO6K,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIsD,GAActD,EAAerE,SAC7BqO,EAAoBhK,EAAe7D,cACnC8N,EAAiB3G,EAAYnH,aAEjC,IAA0B,SAAtB6N,GAAmD,QAAnBC,GAK/B/V,EAAOgL,IAAIgL,sBAAsB,cAAtC,CAMA,IAA8B,IAA1BhW,EAAOiW,eAAyB,CAGhC,GAAIL,GAAK9W,EAAE,cASX,OARA8W,GAAG9M,YAAYsG,GACfpP,EAAO6K,UAAU4C,kBAAkBmI,GAAI,GACvC5V,EAAO6K,UAAUM,mBAGjBnL,EAAOiW,gBAAiB,MAExB1U,GAAE2U,iBAIN,GAAIC,GAAenW,EAAO6K,UAAUmK,WAAWoB,WAG/CpW,GAAOgL,IAAIC,GAAG,aAAc,MAC5BjL,EAAO6K,UAAU6K,YACb1V,EAAO6K,UAAUmK,WAAWoB,cAAgBD,GAE5CnW,EAAOgL,IAAIC,GAAG,aAAc,KAGhC,IAAIoL,GAAavK,EAAepO,OAAOkB,MACnCoB,GAAO6K,UAAUmK,WAAWoB,YAAc,IAAMC,IAGhDrW,EAAOiW,gBAAiB,GAI5B1U,EAAE2U,mBA7FN,GAAIlW,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SA6BvBA,GAAU1K,GAAG,QAAS,SAAUC,GACV,KAAdA,EAAE+U,SAKNX,EAAQpU,KA4DZyK,EAAU1K,GAAG,UAAW,SAAUC,GAC9B,GAAkB,KAAdA,EAAE+U,QAIF,YADAtW,EAAOiW,gBAAiB,EAI5BJ,GAAWtU,MAKnB+T,aAAc,WACV,GAAItV,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SAEvBA,GAAU1K,GAAG,UAAW,SAAUC,GAC9B,GAAkB,IAAdA,EAAE+U,QAAN,CAIA,MAAgB,gBADFtK,EAAUtO,OAAOqU,cAAcrT,WAGzC6C,GAAE2U,qBAFN,MAOJlK,EAAU1K,GAAG,QAAS,SAAUC,GAC5B,GAAkB,IAAdA,EAAE+U,QAAN,CAGA,GAAIV,OAAK,GACLW,EAAUvK,EAAUtO,OAAOqU,cAAcrT,MAGxC6X,IAAuB,SAAZA,IAEZX,EAAK9W,EAAE,gBACPkN,EAAUtO,KAAK,IACfsO,EAAUnL,OAAO+U,GACjB5V,EAAO6K,UAAU4C,kBAAkBmI,GAAI,GAAO,GAC9C5V,EAAO6K,UAAUM,wBAM7BoK,aAAc,WACV,GAAIvV,GAASxC,KAAKwC,OACdoJ,EAAmBpJ,EAAOiJ,OAAOG,iBACjC4C,EAAYhM,EAAOgM,SAGvBA,GAAU1K,GAAG,QAAS,SAAUC,GAC5B,IAAI+I,EAAGK,OAAP,CAIIpJ,EAAE2U,gBAIN,IAAItS,GAAYF,EAAanC,EAAG6H,GAC5B7F,EAAYH,EAAa7B,EAC7BgC,GAAYA,EAAU9E,QAAQ,OAAQ,OAEtC,IAAIqN,GAAiB9L,EAAO6K,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAI5D,GAAW4D,EAAe7D,aAG9B,IAAiB,SAAbC,GAAoC,QAAbA,EAU3B,GAAiB,QAAbA,GAA2C,gBAArB8D,EAAUtO,QAA6B0L,EAa1D,CAEH,IAAK7F,EACD,MAEJvD,GAAOgL,IAAIC,GAAG,aAAc,MAAQ1H,EAAY,YAlB+B,CAE/E,IAAKK,EACD,MAEJ,KAGI5D,EAAOgL,IAAIC,GAAG,aAAcrH,GAC9B,MAAO4S,GAELxW,EAAOgL,IAAIC,GAAG,aAAc,MAAQ1H,EAAY,cAY5DyI,EAAU1K,GAAG,QAAS,SAAUC,GAC5B,IAAI+I,EAAGK,OAAP,CAGIpJ,EAAE2U,gBAIN,IAAIO,GAAa1S,EAAaxC,EAC9B,IAAKkV,GAAeA,EAAW7X,OAA/B,CAKA,GAAIkN,GAAiB9L,EAAO6K,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAI5D,GAAW4D,EAAe7D,aAG9B,IAAiB,SAAbC,GAAoC,QAAbA,EAA3B,CAKgBlI,EAAOyS,UACbA,UAAUgE,UAK5BjB,WAAY,WACR,GAAIxV,GAASxC,KAAKwC,MACFA,GAAOgM,UAEb1K,GAAG,UAAW,SAAUC,GAC9B,GAAkB,IAAdA,EAAE+U,SAGDtW,EAAOgL,IAAIgL,sBAAsB,cAAtC,CAIA,GAAIlK,GAAiB9L,EAAO6K,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIsD,GAActD,EAAerE,SAC7BqO,EAAoBhK,EAAe7D,cACnC8N,EAAiB3G,EAAYnH,aAEP,UAAtB6N,GAAmD,QAAnBC,EAEhC/V,EAAOgL,IAAIC,GAAG,aAAc,QAG5BjL,EAAOgL,IAAIC,GAAG,aAAc,4BAGhC1J,EAAE2U,sBAKVT,WAAY,WACR,GAAIzV,GAASxC,KAAKwC,OACdgM,EAAYhM,EAAOgM,SAIvBA,GAAU1K,GAAG,QAAS,MAAO,SAAUC,GACnC,GAAImV,GAAMlZ,KACN+U,EAAOzT,EAAE4X,EAGb1K,GAAU7D,KAAK,OAAOtB,YARN,gBAWhB0L,EAAK3R,SAXW,gBAYhBZ,EAAOwS,aAAeD,EAGtBvS,EAAO6K,UAAU4C,kBAAkB8E,KAIvCvG,EAAU1K,GAAG,eAAgB,SAAUC,GAC/BA,EAAE0E,OAAOC,QAAQ,SAKrB8F,EAAU7D,KAAK,OAAOtB,YAzBN,gBA0BhB7G,EAAOwS,aAAe,UAelCnO,EAAQmB,WACJC,YAAapB,EAGb4G,GAAI,SAAa6G,EAAM1Q,GACnB,GAAIpB,GAASxC,KAAKwC,MAGlB,IAAKA,EAAO6K,UAAUmK,WAAtB,CAKAhV,EAAO6K,UAAUM,kBAGjB,IAAIwL,GAAQ,IAAM7E,CACdtU,MAAKmZ,GAELnZ,KAAKmZ,GAAOvV,GAGZ5D,KAAKoZ,aAAa9E,EAAM1Q,GAI5BpB,EAAOmD,MAAM8R,eAGbjV,EAAO6K,UAAU6K,YACjB1V,EAAO6K,UAAUM,mBAGjBnL,EAAO6W,QAAU7W,EAAO6W,WAI5BC,YAAa,SAAqBpZ,GAC9B,GAAIsC,GAASxC,KAAKwC,OACd+W,EAAQ/W,EAAO6K,UAAUmK,UAI7B,KADW,SAAS/Q,KAAKvG,KACX4M,EAAGI,WAEb,KAAM,IAAItF,OAAM,oCAGhB5H,MAAKwY,sBAAsB,cAE3BxY,KAAKoZ,aAAa,aAAclZ,GACzBqZ,EAAMC,YAEbD,EAAME,iBACNF,EAAMC,WAAWlY,EAAEpB,GAAM,KAClBqZ,EAAMG,WAEbH,EAAMG,UAAUxZ,IAKxByZ,YAAa,SAAqBlX,GAC9B,GAAID,GAASxC,KAAKwC,OACd+W,EAAQ/W,EAAO6K,UAAUmK,UAEzB+B,GAAMC,aACND,EAAME,iBACNF,EAAMC,WAAW/W,EAAM,MAK/B2W,aAAc,SAAsB9E,EAAM1Q,GACtCxD,SAASwZ,YAAYtF,GAAM,EAAO1Q,IAItC8K,kBAAmB,SAA2B4F,GAC1C,MAAOlU,UAASsO,kBAAkB4F,IAItCzG,kBAAmB,SAA2ByG,GAC1C,MAAOlU,UAASyN,kBAAkByG,IAItCkE,sBAAuB,SAA+BlE,GAClD,MAAOlU,UAASoY,sBAAsBlE,KAe9CxN,EAAIkB,WACAC,YAAanB,EAGb0Q,SAAU,WACN,MAAOxX,MAAK+G,eAIhBmR,UAAW,SAAmB2B,GAC1B,GAAIA,EAGA,YADA7Z,KAAK+G,cAAgB8S,EAKzB,IAAIxM,GAAYrH,OAAO8T,cACvB,IAA6B,IAAzBzM,EAAU0M,WAAd,CAGA,GAAIR,GAAQlM,EAAU2M,WAAW,GAG7BC,EAAiBja,KAAKuO,0BAA0BgL,EACpD,IAAKU,EAAL,CAGaja,KAAKwC,OACKgM,UACTpE,UAAU6P,KAEpBja,KAAK+G,cAAgBwS,MAK7B7L,cAAe,SAAuBwM,GACnB,MAAXA,IAEAA,GAAU,EAEd,IAAIX,GAAQvZ,KAAK+G,aACbwS,IACAA,EAAMY,SAASD,IAKvB/J,iBAAkB,WAEd,MADYnQ,MAAK+G,cAEN/G,KAAK+G,cAAc3E,WAEnB,IAKfmM,0BAA2B,SAAmCgL,GAC1DA,EAAQA,GAASvZ,KAAK+G,aACtB,IAAImB,OAAO,EACX,IAAIqR,EAEA,MADArR,GAAOqR,EAAMa,wBACN9Y,EAAoB,IAAlB4G,EAAKlH,SAAiBkH,EAAOA,EAAKmD,aAGnD+F,sBAAuB,SAA+BmI,GAClDA,EAAQA,GAASvZ,KAAK+G,aACtB,IAAImB,OAAO,EACX,IAAIqR,EAEA,MADArR,GAAOqR,EAAMc,eACN/Y,EAAoB,IAAlB4G,EAAKlH,SAAiBkH,EAAOA,EAAKmD,aAGnDiG,oBAAqB,SAA6BiI,GAC9CA,EAAQA,GAASvZ,KAAK+G,aACtB,IAAImB,OAAO,EACX,IAAIqR,EAEA,MADArR,GAAOqR,EAAMe,aACNhZ,EAAoB,IAAlB4G,EAAKlH,SAAiBkH,EAAOA,EAAKmD,aAKnDiC,iBAAkB,WACd,GAAIiM,GAAQvZ,KAAK+G,aACjB,UAAIwS,IAASA,EAAMc,gBACXd,EAAMc,iBAAmBd,EAAMe,cAC3Bf,EAAMX,cAAgBW,EAAMgB,YAS5C5M,iBAAkB,WACd,GAAIN,GAAYrH,OAAO8T,cACvBzM,GAAUmN,kBACVnN,EAAUoN,SAASza,KAAK+G,gBAI5BwG,iBAAkB,WACd,GAAI/K,GAASxC,KAAKwC,OACd+W,EAAQvZ,KAAKwX,WACb/U,MAAQ,EAEZ,IAAK8W,GAIAvZ,KAAKsN,mBAKV,IAEQR,EAAGI,YAEH1K,EAAOgL,IAAIC,GAAG,aAAc,WAE5B8L,EAAMmB,OAAOnB,EAAMe,aAAcf,EAAMgB,UAAY,GAEnDva,KAAKkY,UAAUqB,KAEf9W,EAAQnB,EAAE,4BACVkB,EAAOgL,IAAIC,GAAG,aAAchL,GAC5BzC,KAAKiQ,kBAAkBxN,GAAO,IAEpC,MAAOuW,MAMb/I,kBAAmB,SAA2BxN,EAAOyX,EAASS,GAI1D,GAAKlY,EAAMrB,OAAX,CAIA,GAAI8G,GAAOzF,EAAM,GACb8W,EAAQnZ,SAASwa,aAEjBD,GACApB,EAAMsB,mBAAmB3S,GAEzBqR,EAAMuB,WAAW5S,GAGE,iBAAZgS,IACPX,EAAMY,SAASD,GAInBla,KAAKkY,UAAUqB,MAkBvBvS,EAASgB,WACLC,YAAajB,EAEb4C,KAAM,SAAcmR,GAChB,GAAIhY,GAAQ/C,IAGZ,KAAIA,KAAKkH,QAAT,CAGAlH,KAAKkH,SAAU,CAGf,IAAIK,GAAOvH,KAAKuH,IAChB,IAAKvH,KAAKmH,UAINnH,KAAKmH,WAAY,MAJA,CACInH,KAAKqH,eACXhE,OAAOkE,GAMtByT,KAAKC,MAAQjb,KAAKiH,MAAQ,KACtB8T,GAAY,IACZxT,EAAK+B,IAAI,QAAoB,IAAXyR,EAAiB,KACnC/a,KAAKiH,MAAQ+T,KAAKC,MAK1B,IAAIC,GAAYlb,KAAKoH,UACjB8T,IACAjN,aAAaiN,GAEjBA,EAAYjX,WAAW,WACnBlB,EAAMoY,SACP,OAGPA,MAAO,WACQnb,KAAKuH,KACXyC,SAGLhK,KAAKiH,MAAQ,EACbjH,KAAKkH,SAAU,EACflH,KAAKmH,WAAY,GAIzB,IAAIiU,GAA4B,kBAAXC,SAAoD,gBAApBA,QAAOC,SAAwB,SAAU9Z,GAC5F,aAAcA,IACZ,SAAUA,GACZ,MAAOA,IAAyB,kBAAX6Z,SAAyB7Z,EAAIyG,cAAgBoT,QAAU7Z,IAAQ6Z,OAAOrT,UAAY,eAAkBxG,GAa3HgG,GAAUQ,WACNC,YAAaT,EAGb+T,OAAQ,SAAgBC,EAAWC,GAC/B,GAAIjZ,GAASxC,KAAKwC,OACdmJ,EAAQnJ,EAAOiJ,OAAOE,MACtB+P,EAAclZ,EAAOiJ,OAAOiQ,WAEhC,IAAI/P,EACA,KAAM,IAAI/D,OAAM,gBAAkB6T,GAAaD,GAE3CE,IAAsC,kBAAhBA,GACtBA,EAAYF,GAEZG,MAAMH,IAMlB5F,cAAe,SAAuBxF,GAClC,GAAIuC,GAAS3S,IAEb,IAAKoQ,EAAL,CAGapQ,KAAKwC,OACXgL,IAAIC,GAAG,aAAc,aAAe2C,EAAO,8BAGlD,IAAI8I,GAAM9Y,SAASC,cAAc,MACjC6Y,GAAI0C,OAAS,WACT1C,EAAM,MAEVA,EAAI2C,QAAU,WACV3C,EAAM,KAENvG,EAAO4I,OAAO,SAAU,6BAA2FnL,EAAO,cAG9H8I,EAAI4C,QAAU,WACV5C,EAAM,MAEVA,EAAI6C,IAAM3L,IAId6E,UAAW,SAAmBxI,GAC1B,GAAIuP,GAAShc,IAEb,IAAKyM,GAAUA,EAAMrL,OAArB,CAKA,GAAIoB,GAASxC,KAAKwC,OACdiJ,EAASjJ,EAAOiJ,OAChBwQ,EAAUxQ,EAAOK,iBACjBoQ,EAAWD,EAAU,IAAO,IAC5BE,EAAY1Q,EAAO2Q,oBAAsB,IACzCtG,EAAkBrK,EAAOqK,gBACzB/J,EAAsBN,EAAOM,oBAC7BC,EAAiBP,EAAOO,gBAAkB,GAC1CC,EAAkBR,EAAOQ,oBACzBE,EAAmBV,EAAOU,qBAC1BkQ,EAAQ5Q,EAAOa,mBACfO,EAAUpB,EAAOY,kBAAoB,IACrCD,EAAkBX,EAAOW,eACN,OAAnBA,IACAA,GAAkB,EAEtB,IAAI2J,GAAkBtK,EAAOsK,gBAGzBuG,KACAC,IAyBJ,IAxBA1a,EAAW4K,EAAO,SAAU+P,GACxB,GAAIlI,GAAOkI,EAAKlI,KACZmI,EAAOD,EAAKC,IAGhB,IAAKnI,GAASmI,EAId,OAAgD,IAA5C,6BAA6BhW,KAAK6N,OAElCiI,GAAQ7V,KAAK,IAAW4N,EAAO,SAG/B2H,EAAUQ,MAEVF,GAAQ7V,KAAK,IAAW4N,EAAO,OAAwB4H,EAAW,SAKtEI,GAAY5V,KAAK8V,KAGjBD,EAAQnb,OAER,WADApB,MAAKub,OAAO,cAAgBgB,EAAQnT,KAAK,MAG7C,IAAIkT,EAAYlb,OAAS+a,EAErB,WADAnc,MAAKub,OAAO,SAAWY,EAAY,MAKvC,IAAIpG,GAA8C,kBAApBA,GAI1B,WAHAA,GAAgBuG,EAAatc,KAAK4V,cAAc8G,KAAK1c,MAOzD,IAAI2c,GAAW,GAAIC,SAOnB,IANA/a,EAAWya,EAAa,SAAUE,GAC9B,GAAIlI,GAAOtI,GAAkBwQ,EAAKlI,IAClCqI,GAAStZ,OAAOiR,EAAMkI,KAItB1G,GAA8C,gBAApBA,GAA8B,CAExD,GAAI+G,GAAqB/G,EAAgBxP,MAAM,IAC/CwP,GAAkB+G,EAAmB,EACrC,IAAIC,GAAsBD,EAAmB,IAAM,EACnDtb,GAAW0K,EAAiB,SAAUvK,EAAKoH,GACvCA,EAAMiU,mBAAmBjU,GAGrBgN,EAAgB3U,QAAQ,KAAO,EAC/B2U,GAAmB,IAEnBA,GAAmB,IAEvBA,EAAkBA,EAAkBpU,EAAM,IAAMoH,EAGhD6T,EAAStZ,OAAO3B,EAAKoH,KAErBgU,IACAhH,GAAmB,IAAMgH,EAI7B,IAAItQ,GAAM,GAAIwQ,eAqFd,IApFAxQ,EAAIyQ,KAAK,OAAQnH,GAGjBtJ,EAAIK,QAAUA,EACdL,EAAI0Q,UAAY,WAERb,EAAMxP,SAAoC,kBAAlBwP,GAAMxP,SAC9BwP,EAAMxP,QAAQL,EAAKhK,GAGvBwZ,EAAOT,OAAO,WAId/O,EAAI2Q,SACJ3Q,EAAI2Q,OAAOC,WAAa,SAAUrZ,GAC9B,GAAIsZ,OAAU,GAEVC,EAAc,GAAItW,GAASxE,EAC3BuB,GAAEwZ,mBACFF,EAAUtZ,EAAEyZ,OAASzZ,EAAE0Z,MACvBH,EAAY1T,KAAKyT,MAM7B7Q,EAAIkR,mBAAqB,WACrB,GAAI7c,OAAS,EACb,IAAuB,IAAnB2L,EAAImR,WAAkB,CACtB,GAAInR,EAAIoR,OAAS,KAAOpR,EAAIoR,QAAU,IAQlC,MANIvB,GAAMzP,OAAgC,kBAAhByP,GAAMzP,OAC5ByP,EAAMzP,MAAMJ,EAAKhK,OAIrBwZ,GAAOT,OAAO,WAAY,qBAA4G/O,EAAIoR,OAK9I,IADA/c,EAAS2L,EAAIqR,aAC2D,gBAAjD,KAAXhd,EAAyB,YAAcua,EAAQva,IACvD,IACIA,EAASid,KAAKC,MAAMld,GACtB,MAAOmY,GAOL,MALIqD,GAAM1P,MAA8B,kBAAf0P,GAAM1P,MAC3B0P,EAAM1P,KAAKH,EAAKhK,EAAQ3B,OAG5Bmb,GAAOT,OAAO,SAAU,qBAAuB1a,GAIvD,GAAKwb,EAAM2B,cAAgC,KAAhBnd,EAAOod,MAQ3B,CACH,GAAI5B,EAAM2B,cAA8C,kBAAvB3B,GAAM2B,aAEnC3B,EAAM2B,aAAahC,EAAOpG,cAAc8G,KAAKV,GAASnb,EAAQ2B,OAC3D,EAEQ3B,EAAOqd,UACbxa,QAAQ,SAAU0M,GACnB4L,EAAOpG,cAAcxF,KAKzBiM,EAAM3P,SAAoC,kBAAlB2P,GAAM3P,SAC9B2P,EAAM3P,QAAQF,EAAKhK,EAAQ3B,OApB3Bwb,GAAM1P,MAA8B,kBAAf0P,GAAM1P,MAC3B0P,EAAM1P,KAAKH,EAAKhK,EAAQ3B,GAI5Bmb,EAAOT,OAAO,SAAU,yBAA2B1a,EAAOod,SAsBlE5B,EAAM9P,QAAkC,kBAAjB8P,GAAM9P,OAAuB,CACpD,GAAI4R,GAAe9B,EAAM9P,OAAOC,EAAKhK,EAAQ8Z,EAC7C,IAAI6B,GAAgG,gBAAvD,KAAjBA,EAA+B,YAAc/C,EAAQ+C,KACzEA,EAAaC,QAGb,WADApe,MAAKub,OAAO4C,EAAaE,KAkBrC,MAXA9c,GAAW4K,EAAkB,SAAUzK,EAAKoH,GACxC0D,EAAI8R,iBAAiB5c,EAAKoH,KAI9B0D,EAAIJ,gBAAkBA,MAGtBI,GAAI+R,KAAK5B,GAOT5Q,GACAlK,EAAW4K,EAAO,SAAU+P,GACxB,GAAIzZ,GAAQiZ,EACRwC,EAAS,GAAIxI,WACjBwI,GAAOC,cAAcjC,GACrBgC,EAAO5C,OAAS,WACZ7Y,EAAM6S,cAAc5V,KAAKa,aAY7C,IAAIiH,GAAW,CAmBfL,GAAOO,WACHC,YAAaR,EAGbiX,YAAa,WAET,GAAIjW,KACJzI,MAAKyL,OAASkT,OAAOC,OAAOnW,EAAQgD,EAAQzL,KAAK+H,aAGjD,IAAI8W,GAAa7e,KAAKyL,OAAOqT,SACzB/Q,IACJxM,GAAWsd,EAAY,SAAUnd,EAAKoH,GAGlCiF,EAASrH,MACLsH,IAAK,GAAI+Q,QAAOrd,EAAK,OACrBoH,IAAKA,MAIb9I,KAAKyL,OAAOsC,SAAWA,GAI3BiR,SAAU,WACN,GAAIjc,GAAQ/C,KAER0H,EAAkB1H,KAAK0H,gBACvBuX,EAAmB3d,EAAEoG,GACrBC,EAAe3H,KAAK2H,aAEpBuX,EAAYlf,KAAKyL,OACjBC,EAASwT,EAAUxT,OAGnB6L,MAAe,GACfjQ,MAAqB,GACrBkH,MAAY,GACZ3E,MAAY,EAEI,OAAhBlC,GAEA4P,EAAejW,EAAE,eACjBgG,EAAqBhG,EAAE,eAGvBuI,EAAYoV,EAAiB1e,WAG7B0e,EAAiB5b,OAAOkU,GAAclU,OAAOiE,GAG7CiQ,EAAajO,IAAI,mBAAoB,WAAWA,IAAI,SAAU,kBAC9DhC,EAAmBgC,IAAI,SAAU,kBAAkBA,IAAI,aAAc,QAAQA,IAAI,SAAU,WAG3FiO,EAAe0H,EACf3X,EAAqBhG,EAAEqG,GAEvBkC,EAAYvC,EAAmB/G,YAInCiO,EAAYlN,EAAE,eACdkN,EAAU3F,KAAK,kBAAmB,QAAQS,IAAI,QAAS,QAAQA,IAAI,SAAU,QAGzEO,GAAaA,EAAUzI,OACvBoN,EAAUnL,OAAOwG,GAEjB2E,EAAUnL,OAAO/B,EAAE,gBAIvBgG,EAAmBjE,OAAOmL,GAG1B+I,EAAanU,SAAS,eACtBkE,EAAmBlE,SAAS,sBAC5BkE,EAAmBgC,IAAI,UAAWoC,GAClC8C,EAAUpL,SAAS,YAGnBpD,KAAKuX,aAAeA,EACpBvX,KAAKsH,mBAAqBA,EAC1BtH,KAAKwO,UAAYA,EAGjBlH,EAAmBxD,GAAG,cAAe,WACjCf,EAAMsW,QAAUtW,EAAMsW,WAE1B9B,EAAazT,GAAG,QAAS,WACrB9D,KAAKqZ,QAAUrZ,KAAKqZ,YAK5B8F,aAAc,WACVnf,KAAKwN,IAAM,GAAI3G,GAAQ7G,OAI3Bof,kBAAmB,WACfpf,KAAKqN,UAAY,GAAIvG,GAAI9G,OAI7Bqf,eAAgB,WACZrf,KAAKiV,UAAY,GAAIzN,GAAUxH,OAInCsf,WAAY,WACRtf,KAAK2F,MAAQ,GAAID,GAAM1F,MACvBA,KAAK2F,MAAMuR,QAIfqI,UAAW,WACPvf,KAAKwf,IAAM,GAAI5Y,GAAK5G,MACpBA,KAAKwf,IAAItI,QAIbS,cAAe,SAAuB8H,GAClC,GAAIjR,GAAYxO,KAAKwO,UACjB3E,EAAY2E,EAAUjO,UAC1B,KAAKsJ,EAAUzI,OAIX,MAFAoN,GAAUnL,OAAO/B,EAAE,oBACnBtB,MAAK2X,eAIT,IAAI+H,GAAQ7V,EAAUvB,MAEtB,IAAImX,EAAS,CAET,GAAIvf,GAAOwf,EAAMxf,OAAOqU,cACpB7J,EAAWgV,EAAMjV,aACrB,IAAa,SAATvK,GAA4B,UAATA,GAAkC,MAAbwK,EAIxC,MAFA8D,GAAUnL,OAAO/B,EAAE,oBACnBtB,MAAK2X,gBAKb3X,KAAKqN,UAAU4C,kBAAkByP,GAAO,GAAO,GAC/C1f,KAAKqN,UAAUM,oBAInB2J,WAAY,WAER,GAAIqI,GAAoB,EACpBC,EAAmB5f,KAAKwf,IAAItf,OAC5Bgf,EAAYlf,KAAKyL,OACjBoU,EAAWX,EAAUW,QACrBA,IAAgC,kBAAbA,KAKnB7f,KAAKqZ,OAAS,WAEV,GAAIyG,GAAc9f,KAAKwf,IAAItf,MACvB4f,GAAY1e,SAAWwe,EAAiBxe,SAKxCue,GACA1R,aAAa0R,GAEjBA,EAAoB1b,WAAW,WAE3B4b,EAASC,GACTF,EAAmBE,GACpB,SAMfC,OAAQ,WAEJ/f,KAAK0e,cAGL1e,KAAKgf,WAGLhf,KAAKmf,eAGLnf,KAAKof,oBAGLpf,KAAKuf,YAGLvf,KAAKsf,aAGLtf,KAAKqf,iBAGLrf,KAAK2X,eAAc,GAGnB3X,KAAKsX,cAKb,KACIlX,SACF,MAAO4Y,GACL,KAAM,IAAIpR,OAAM,eAvhIL,WAGiB,kBAAjB+W,QAAOC,SACdD,OAAOC,OAAS,SAAUnW,EAAQuX,GAE9B,GAAc,MAAVvX,EAEA,KAAM,IAAIwX,WAAU,6CAKxB,KAAK,GAFDC,GAAKvB,OAAOlW,GAEPL,EAAQ,EAAGA,EAAQ+X,UAAU/e,OAAQgH,IAAS,CACnD,GAAIgY,GAAaD,UAAU/X,EAE3B,IAAkB,MAAdgY,EAEA,IAAK,GAAIC,KAAWD,GAEZzB,OAAO3W,UAAUrG,eAAeC,KAAKwe,EAAYC,KACjDH,EAAGG,GAAWD,EAAWC,IAKzC,MAAOH,KAKVI,QAAQtY,UAAUU,UACnB4X,QAAQtY,UAAUU,QAAU4X,QAAQtY,UAAUuY,iBAAmBD,QAAQtY,UAAUwY,oBAAsBF,QAAQtY,UAAUyY,mBAAqBH,QAAQtY,UAAU0Y,kBAAoBJ,QAAQtY,UAAU2Y,uBAAyB,SAAUC,GAGvO,IAFA,GAAIlY,IAAW1I,KAAKI,UAAYJ,KAAK6gB,eAAejgB,iBAAiBggB,GACjEvf,EAAIqH,EAAQtH,SACPC,GAAK,GAAKqH,EAAQ3G,KAAKV,KAAOrB,OACvC,MAAOqB,IAAK,MA0/HxB,IAGImI,GAAQpJ,SAASC,cAAc,QAQnC,OAPAmJ,GAAM9G,KAAO,WACb8G,EAAMlJ,UALU,0lfAMhBF,SAAS0gB,qBAAqB,QAAQ/e,KAAK,GAAGgI,YAAYP,GAG9CxD,OAAOjG,YAAc0H", "file": "wangEditor.min.js", "sourcesContent": ["(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.wangEditor = factory());\n}(this, (function () { 'use strict';\n\n/*\n    poly-fill\n*/\n\nvar polyfill = function () {\n\n    // Object.assign\n    if (typeof Object.assign != 'function') {\n        Object.assign = function (target, varArgs) {\n            // .length of function is 2\n            if (target == null) {\n                // TypeError if undefined or null\n                throw new TypeError('Cannot convert undefined or null to object');\n            }\n\n            var to = Object(target);\n\n            for (var index = 1; index < arguments.length; index++) {\n                var nextSource = arguments[index];\n\n                if (nextSource != null) {\n                    // Skip over if undefined or null\n                    for (var nextKey in nextSource) {\n                        // Avoid bugs when hasOwnProperty is shadowed\n                        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                            to[nextKey] = nextSource[nextKey];\n                        }\n                    }\n                }\n            }\n            return to;\n        };\n    }\n\n    // IE 中兼容 Element.prototype.matches\n    if (!Element.prototype.matches) {\n        Element.prototype.matches = Element.prototype.matchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector || Element.prototype.oMatchesSelector || Element.prototype.webkitMatchesSelector || function (s) {\n            var matches = (this.document || this.ownerDocument).querySelectorAll(s),\n                i = matches.length;\n            while (--i >= 0 && matches.item(i) !== this) {}\n            return i > -1;\n        };\n    }\n};\n\n/*\n    DOM 操作 API\n*/\n\n// 根据 html 代码片段创建 dom 对象\nfunction createElemByHTML(html) {\n    var div = void 0;\n    div = document.createElement('div');\n    div.innerHTML = html;\n    return div.children;\n}\n\n// 是否是 DOM List\nfunction isDOMList(selector) {\n    if (!selector) {\n        return false;\n    }\n    if (selector instanceof HTMLCollection || selector instanceof NodeList) {\n        return true;\n    }\n    return false;\n}\n\n// 封装 document.querySelectorAll\nfunction querySelectorAll(selector) {\n    var result = document.querySelectorAll(selector);\n    if (isDOMList(result)) {\n        return result;\n    } else {\n        return [result];\n    }\n}\n\n// 创建构造函数\nfunction DomElement(selector) {\n    if (!selector) {\n        return;\n    }\n\n    // selector 本来就是 DomElement 对象，直接返回\n    if (selector instanceof DomElement) {\n        return selector;\n    }\n\n    this.selector = selector;\n\n    // 根据 selector 得出的结果（如 DOM，DOM List）\n    var selectorResult = [];\n    if (selector.nodeType === 1) {\n        // 单个 DOM 节点\n        selectorResult = [selector];\n    } else if (isDOMList(selector)) {\n        // DOM List\n        selectorResult = selector;\n    } else if (typeof selector === 'string') {\n        // 字符串\n        selector = selector.replace('/\\n/mg', '').trim();\n        if (selector.indexOf('<') === 0) {\n            // 如 <div>\n            selectorResult = createElemByHTML(selector);\n        } else {\n            // 如 #id .class\n            selectorResult = querySelectorAll(selector);\n        }\n    }\n\n    var length = selectorResult.length;\n    if (!length) {\n        // 空数组\n        return this;\n    }\n\n    // 加入 DOM 节点\n    var i = void 0;\n    for (i = 0; i < length; i++) {\n        this[i] = selectorResult[i];\n    }\n    this.length = length;\n}\n\n// 修改原型\nDomElement.prototype = {\n    constructor: DomElement,\n\n    // 类数组，forEach\n    forEach: function forEach(fn) {\n        var i = void 0;\n        for (i = 0; i < this.length; i++) {\n            var elem = this[i];\n            var result = fn.call(elem, elem, i);\n            if (result === false) {\n                break;\n            }\n        }\n        return this;\n    },\n\n    // 获取第几个元素\n    get: function get(index) {\n        var length = this.length;\n        if (index >= length) {\n            index = index % length;\n        }\n        return $(this[index]);\n    },\n\n    // 第一个\n    first: function first() {\n        return this.get(0);\n    },\n\n    // 最后一个\n    last: function last() {\n        var length = this.length;\n        return this.get(length - 1);\n    },\n\n    // 绑定事件\n    on: function on(type, selector, fn) {\n        // selector 不为空，证明绑定事件要加代理\n        if (!fn) {\n            fn = selector;\n            selector = null;\n        }\n\n        // type 是否有多个\n        var types = [];\n        types = type.split(/\\s+/);\n\n        return this.forEach(function (elem) {\n            types.forEach(function (type) {\n                if (!type) {\n                    return;\n                }\n\n                if (!selector) {\n                    // 无代理\n                    elem.addEventListener(type, fn, false);\n                    return;\n                }\n\n                // 有代理\n                elem.addEventListener(type, function (e) {\n                    var target = e.target;\n                    if (target.matches(selector)) {\n                        fn.call(target, e);\n                    }\n                }, false);\n            });\n        });\n    },\n\n    // 取消事件绑定\n    off: function off(type, fn) {\n        return this.forEach(function (elem) {\n            elem.removeEventListener(type, fn, false);\n        });\n    },\n\n    // 获取/设置 属性\n    attr: function attr(key, val) {\n        if (val == null) {\n            // 获取值\n            return this[0].getAttribute(key);\n        } else {\n            // 设置值\n            return this.forEach(function (elem) {\n                elem.setAttribute(key, val);\n            });\n        }\n    },\n\n    // 添加 class\n    addClass: function addClass(className) {\n        if (!className) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var arr = void 0;\n            if (elem.className) {\n                // 解析当前 className 转换为数组\n                arr = elem.className.split(/\\s/);\n                arr = arr.filter(function (item) {\n                    return !!item.trim();\n                });\n                // 添加 class\n                if (arr.indexOf(className) < 0) {\n                    arr.push(className);\n                }\n                // 修改 elem.class\n                elem.className = arr.join(' ');\n            } else {\n                elem.className = className;\n            }\n        });\n    },\n\n    // 删除 class\n    removeClass: function removeClass(className) {\n        if (!className) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var arr = void 0;\n            if (elem.className) {\n                // 解析当前 className 转换为数组\n                arr = elem.className.split(/\\s/);\n                arr = arr.filter(function (item) {\n                    item = item.trim();\n                    // 删除 class\n                    if (!item || item === className) {\n                        return false;\n                    }\n                    return true;\n                });\n                // 修改 elem.class\n                elem.className = arr.join(' ');\n            }\n        });\n    },\n\n    // 修改 css\n    css: function css(key, val) {\n        var currentStyle = key + ':' + val + ';';\n        return this.forEach(function (elem) {\n            var style = (elem.getAttribute('style') || '').trim();\n            var styleArr = void 0,\n                resultArr = [];\n            if (style) {\n                // 将 style 按照 ; 拆分为数组\n                styleArr = style.split(';');\n                styleArr.forEach(function (item) {\n                    // 对每项样式，按照 : 拆分为 key 和 value\n                    var arr = item.split(':').map(function (i) {\n                        return i.trim();\n                    });\n                    if (arr.length === 2) {\n                        resultArr.push(arr[0] + ':' + arr[1]);\n                    }\n                });\n                // 替换或者新增\n                resultArr = resultArr.map(function (item) {\n                    if (item.indexOf(key) === 0) {\n                        return currentStyle;\n                    } else {\n                        return item;\n                    }\n                });\n                if (resultArr.indexOf(currentStyle) < 0) {\n                    resultArr.push(currentStyle);\n                }\n                // 结果\n                elem.setAttribute('style', resultArr.join('; '));\n            } else {\n                // style 无值\n                elem.setAttribute('style', currentStyle);\n            }\n        });\n    },\n\n    // 显示\n    show: function show() {\n        return this.css('display', 'block');\n    },\n\n    // 隐藏\n    hide: function hide() {\n        return this.css('display', 'none');\n    },\n\n    // 获取子节点\n    children: function children() {\n        var elem = this[0];\n        if (!elem) {\n            return null;\n        }\n\n        return $(elem.children);\n    },\n\n    // 增加子节点\n    append: function append($children) {\n        return this.forEach(function (elem) {\n            $children.forEach(function (child) {\n                elem.appendChild(child);\n            });\n        });\n    },\n\n    // 移除当前节点\n    remove: function remove() {\n        return this.forEach(function (elem) {\n            if (elem.remove) {\n                elem.remove();\n            } else {\n                var parent = elem.parentElement;\n                parent && parent.removeChild(elem);\n            }\n        });\n    },\n\n    // 是否包含某个子节点\n    isContain: function isContain($child) {\n        var elem = this[0];\n        var child = $child[0];\n        return elem.contains(child);\n    },\n\n    // 尺寸数据\n    getSizeData: function getSizeData() {\n        var elem = this[0];\n        return elem.getBoundingClientRect(); // 可得到 bottom height left right top width 的数据\n    },\n\n    // 封装 nodeName\n    getNodeName: function getNodeName() {\n        var elem = this[0];\n        return elem.nodeName;\n    },\n\n    // 从当前元素查找\n    find: function find(selector) {\n        var elem = this[0];\n        return $(elem.querySelectorAll(selector));\n    },\n\n    // 获取当前元素的 text\n    text: function text(val) {\n        if (!val) {\n            // 获取 text\n            var elem = this[0];\n            return elem.innerHTML.replace(/<.*?>/g, function () {\n                return '';\n            });\n        } else {\n            // 设置 text\n            return this.forEach(function (elem) {\n                elem.innerHTML = val;\n            });\n        }\n    },\n\n    // 获取 html\n    html: function html(value) {\n        var elem = this[0];\n        if (value == null) {\n            return elem.innerHTML;\n        } else {\n            elem.innerHTML = value;\n            return this;\n        }\n    },\n\n    // 获取 value\n    val: function val() {\n        var elem = this[0];\n        return elem.value.trim();\n    },\n\n    // focus\n    focus: function focus() {\n        return this.forEach(function (elem) {\n            elem.focus();\n        });\n    },\n\n    // parent\n    parent: function parent() {\n        var elem = this[0];\n        return $(elem.parentElement);\n    },\n\n    // parentUntil 找到符合 selector 的父节点\n    parentUntil: function parentUntil(selector, _currentElem) {\n        var results = document.querySelectorAll(selector);\n        var length = results.length;\n        if (!length) {\n            // 传入的 selector 无效\n            return null;\n        }\n\n        var elem = _currentElem || this[0];\n        if (elem.nodeName === 'BODY') {\n            return null;\n        }\n\n        var parent = elem.parentElement;\n        var i = void 0;\n        for (i = 0; i < length; i++) {\n            if (parent === results[i]) {\n                // 找到，并返回\n                return $(parent);\n            }\n        }\n\n        // 继续查找\n        return this.parentUntil(selector, parent);\n    },\n\n    // 判断两个 elem 是否相等\n    equal: function equal($elem) {\n        if ($elem.nodeType === 1) {\n            return this[0] === $elem;\n        } else {\n            return this[0] === $elem[0];\n        }\n    },\n\n    // 将该元素插入到某个元素前面\n    insertBefore: function insertBefore(selector) {\n        var $referenceNode = $(selector);\n        var referenceNode = $referenceNode[0];\n        if (!referenceNode) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var parent = referenceNode.parentNode;\n            parent.insertBefore(elem, referenceNode);\n        });\n    },\n\n    // 将该元素插入到某个元素后面\n    insertAfter: function insertAfter(selector) {\n        var $referenceNode = $(selector);\n        var referenceNode = $referenceNode[0];\n        if (!referenceNode) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var parent = referenceNode.parentNode;\n            if (parent.lastChild === referenceNode) {\n                // 最后一个元素\n                parent.appendChild(elem);\n            } else {\n                // 不是最后一个元素\n                parent.insertBefore(elem, referenceNode.nextSibling);\n            }\n        });\n    }\n};\n\n// new 一个对象\nfunction $(selector) {\n    return new DomElement(selector);\n}\n\n/*\n    配置信息\n*/\n\nvar config = {\n\n    // 默认菜单配置\n    menus: ['head', 'bold', 'italic', 'underline', 'strikeThrough', 'foreColor', 'backColor', 'link', 'list', 'justify', 'quote', 'emoticon', 'image', 'table', 'video', 'code', 'undo', 'redo'],\n\n    // // 语言配置\n    // lang: {\n    //     '设置标题': 'title',\n    //     '正文': 'p',\n    //     '链接文字': 'link text',\n    //     '链接': 'link',\n    //     '插入': 'insert',\n    //     '创建': 'init'\n    // },\n\n    // 编辑区域的 z-index\n    zIndex: 10000,\n\n    // 是否开启 debug 模式（debug 模式下错误会 throw error 形式抛出）\n    debug: false,\n\n    // 粘贴过滤样式，默认开启\n    pasteFilterStyle: true,\n\n    // onchange 事件\n    // onchange: function (html) {\n    //     // html 即变化之后的内容\n    //     console.log(html)\n    // },\n\n    // 是否显示添加网络图片的 tab\n    showLinkImg: true,\n\n    // 默认上传图片 max size: 5M\n    uploadImgMaxSize: 5 * 1024 * 1024,\n\n    // 配置一次最多上传几个图片\n    // uploadImgMaxLength: 5,\n\n    // 上传图片，是否显示 base64 格式\n    uploadImgShowBase64: false,\n\n    // 上传图片，server 地址（如果有值，则 base64 格式的配置则失效）\n    // uploadImgServer: '/upload',\n\n    // 自定义配置 filename\n    uploadFileName: '',\n\n    // 上传图片的自定义参数\n    uploadImgParams: {\n        token: 'abcdef12345'\n    },\n\n    // 上传图片的自定义header\n    uploadImgHeaders: {\n        // 'Accept': 'text/x-json'\n    },\n\n    // 配置 XHR withCredentials\n    withCredentials: false,\n\n    // 自定义上传图片超时时间 ms\n    uploadImgTimeout: 5000,\n\n    // 上传图片 hook \n    uploadImgHooks: {\n        // customInsert: function (insertLinkImg, result, editor) {\n        //     console.log('customInsert')\n        //     // 图片上传并返回结果，自定义插入图片的事件，而不是编辑器自动插入图片\n        //     const data = result.data1 || []\n        //     data.forEach(link => {\n        //         insertLinkImg(link)\n        //     })\n        // },\n        before: function before(xhr, editor, files) {\n            // 图片上传之前触发\n\n            // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传\n            // return {\n            //     prevent: true,\n            //     msg: '放弃上传'\n            // }\n        },\n        success: function success(xhr, editor, result) {\n            // 图片上传并返回结果，图片插入成功之后触发\n        },\n        fail: function fail(xhr, editor, result) {\n            // 图片上传并返回结果，但图片插入错误时触发\n        },\n        error: function error(xhr, editor) {\n            // 图片上传出错时触发\n        },\n        timeout: function timeout(xhr, editor) {\n            // 图片上传超时时触发\n        }\n    }\n\n};\n\n/*\n    工具\n*/\n\n// 和 UA 相关的属性\nvar UA = {\n    _ua: navigator.userAgent,\n\n    // 是否 webkit\n    isWebkit: function isWebkit() {\n        var reg = /webkit/i;\n        return reg.test(this._ua);\n    },\n\n    // 是否 IE\n    isIE: function isIE() {\n        return 'ActiveXObject' in window;\n    }\n};\n\n// 遍历对象\nfunction objForEach(obj, fn) {\n    var key = void 0,\n        result = void 0;\n    for (key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            result = fn.call(obj, key, obj[key]);\n            if (result === false) {\n                break;\n            }\n        }\n    }\n}\n\n// 遍历类数组\nfunction arrForEach(fakeArr, fn) {\n    var i = void 0,\n        item = void 0,\n        result = void 0;\n    var length = fakeArr.length || 0;\n    for (i = 0; i < length; i++) {\n        item = fakeArr[i];\n        result = fn.call(fakeArr, item, i);\n        if (result === false) {\n            break;\n        }\n    }\n}\n\n// 获取随机数\nfunction getRandom(prefix) {\n    return prefix + Math.random().toString().slice(2);\n}\n\n// 替换 html 特殊字符\nfunction replaceHtmlSymbol(html) {\n    if (html == null) {\n        return '';\n    }\n    return html.replace(/</gm, '&lt;').replace(/>/gm, '&gt;').replace(/\"/gm, '&quot;');\n}\n\n// 返回百分比的格式\n\n/*\n    bold-menu\n*/\n// 构造函数\nfunction Bold(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-bold\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nBold.prototype = {\n    constructor: Bold,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 bold 命令\n        editor.cmd.do('bold');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('bold')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    替换多语言\n */\n\nvar replaceLang = function (editor, str) {\n    var langArgs = editor.config.langArgs || [];\n    var result = str;\n\n    langArgs.forEach(function (item) {\n        var reg = item.reg;\n        var val = item.val;\n\n        if (reg.test(result)) {\n            result = result.replace(reg, function () {\n                return val;\n            });\n        }\n    });\n\n    return result;\n};\n\n/*\n    droplist\n*/\nvar _emptyFn = function _emptyFn() {};\n\n// 构造函数\nfunction DropList(menu, opt) {\n    var _this = this;\n\n    // droplist 所依附的菜单\n    var editor = menu.editor;\n    this.menu = menu;\n    this.opt = opt;\n    // 容器\n    var $container = $('<div class=\"w-e-droplist\"></div>');\n\n    // 标题\n    var $title = opt.$title;\n    var titleHtml = void 0;\n    if ($title) {\n        // 替换多语言\n        titleHtml = $title.html();\n        titleHtml = replaceLang(editor, titleHtml);\n        $title.html(titleHtml);\n\n        $title.addClass('w-e-dp-title');\n        $container.append($title);\n    }\n\n    var list = opt.list || [];\n    var type = opt.type || 'list'; // 'list' 列表形式（如“标题”菜单） / 'inline-block' 块状形式（如“颜色”菜单）\n    var onClick = opt.onClick || _emptyFn;\n\n    // 加入 DOM 并绑定事件\n    var $list = $('<ul class=\"' + (type === 'list' ? 'w-e-list' : 'w-e-block') + '\"></ul>');\n    $container.append($list);\n    list.forEach(function (item) {\n        var $elem = item.$elem;\n\n        // 替换多语言\n        var elemHtml = $elem.html();\n        elemHtml = replaceLang(editor, elemHtml);\n        $elem.html(elemHtml);\n\n        var value = item.value;\n        var $li = $('<li class=\"w-e-item\"></li>');\n        if ($elem) {\n            $li.append($elem);\n            $list.append($li);\n            $elem.on('click', function (e) {\n                onClick(value);\n\n                // 隐藏\n                _this.hideTimeoutId = setTimeout(function () {\n                    _this.hide();\n                }, 0);\n            });\n        }\n    });\n\n    // 绑定隐藏事件\n    $container.on('mouseleave', function (e) {\n        _this.hideTimeoutId = setTimeout(function () {\n            _this.hide();\n        }, 0);\n    });\n\n    // 记录属性\n    this.$container = $container;\n\n    // 基本属性\n    this._rendered = false;\n    this._show = false;\n}\n\n// 原型\nDropList.prototype = {\n    constructor: DropList,\n\n    // 显示（插入DOM）\n    show: function show() {\n        if (this.hideTimeoutId) {\n            // 清除之前的定时隐藏\n            clearTimeout(this.hideTimeoutId);\n        }\n\n        var menu = this.menu;\n        var $menuELem = menu.$elem;\n        var $container = this.$container;\n        if (this._show) {\n            return;\n        }\n        if (this._rendered) {\n            // 显示\n            $container.show();\n        } else {\n            // 加入 DOM 之前先定位位置\n            var menuHeight = $menuELem.getSizeData().height || 0;\n            var width = this.opt.width || 100; // 默认为 100\n            $container.css('margin-top', menuHeight + 'px').css('width', width + 'px');\n\n            // 加入到 DOM\n            $menuELem.append($container);\n            this._rendered = true;\n        }\n\n        // 修改属性\n        this._show = true;\n    },\n\n    // 隐藏（移除DOM）\n    hide: function hide() {\n        if (this.showTimeoutId) {\n            // 清除之前的定时显示\n            clearTimeout(this.showTimeoutId);\n        }\n\n        var $container = this.$container;\n        if (!this._show) {\n            return;\n        }\n        // 隐藏并需改属性\n        $container.hide();\n        this._show = false;\n    }\n};\n\n/*\n    menu - header\n*/\n// 构造函数\nfunction Head(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-header\"><i/></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 100,\n        $title: $('<p>设置标题</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<h1>H1</h1>'), value: '<h1>' }, { $elem: $('<h2>H2</h2>'), value: '<h2>' }, { $elem: $('<h3>H3</h3>'), value: '<h3>' }, { $elem: $('<h4>H4</h4>'), value: '<h4>' }, { $elem: $('<h5>H5</h5>'), value: '<h5>' }, { $elem: $('<p>正文</p>'), value: '<p>' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 Head 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nHead.prototype = {\n    constructor: Head,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        if (editor.$textElem.equal($selectionElem)) {\n            // 不能选中多行来设置标题，否则会出现问题\n            // 例如选中的是 <p>xxx</p><p>yyy</p> 来设置标题，设置之后会成为 <h1>xxx<br>yyy</h1> 不符合预期\n            return;\n        }\n\n        editor.cmd.do('formatBlock', value);\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var reg = /^h/i;\n        var cmdValue = editor.cmd.queryCommandValue('formatBlock');\n        if (reg.test(cmdValue)) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    panel\n*/\n\nvar emptyFn = function emptyFn() {};\n\n// 记录已经显示 panel 的菜单\nvar _isCreatedPanelMenus = [];\n\n// 构造函数\nfunction Panel(menu, opt) {\n    this.menu = menu;\n    this.opt = opt;\n}\n\n// 原型\nPanel.prototype = {\n    constructor: Panel,\n\n    // 显示（插入DOM）\n    show: function show() {\n        var _this = this;\n\n        var menu = this.menu;\n        if (_isCreatedPanelMenus.indexOf(menu) >= 0) {\n            // 该菜单已经创建了 panel 不能再创建\n            return;\n        }\n\n        var editor = menu.editor;\n        var $body = $('body');\n        var $textContainerElem = editor.$textContainerElem;\n        var opt = this.opt;\n\n        // panel 的容器\n        var $container = $('<div class=\"w-e-panel-container\"></div>');\n        var width = opt.width || 300; // 默认 300px\n        $container.css('width', width + 'px').css('margin-left', (0 - width) / 2 + 'px');\n\n        // 添加关闭按钮\n        var $closeBtn = $('<i class=\"w-e-icon-close w-e-panel-close\"></i>');\n        $container.append($closeBtn);\n        $closeBtn.on('click', function () {\n            _this.hide();\n        });\n\n        // 准备 tabs 容器\n        var $tabTitleContainer = $('<ul class=\"w-e-panel-tab-title\"></ul>');\n        var $tabContentContainer = $('<div class=\"w-e-panel-tab-content\"></div>');\n        $container.append($tabTitleContainer).append($tabContentContainer);\n\n        // 设置高度\n        var height = opt.height;\n        if (height) {\n            $tabContentContainer.css('height', height + 'px').css('overflow-y', 'auto');\n        }\n\n        // tabs\n        var tabs = opt.tabs || [];\n        var tabTitleArr = [];\n        var tabContentArr = [];\n        tabs.forEach(function (tab, tabIndex) {\n            if (!tab) {\n                return;\n            }\n            var title = tab.title || '';\n            var tpl = tab.tpl || '';\n\n            // 替换多语言\n            title = replaceLang(editor, title);\n            tpl = replaceLang(editor, tpl);\n\n            // 添加到 DOM\n            var $title = $('<li class=\"w-e-item\">' + title + '</li>');\n            $tabTitleContainer.append($title);\n            var $content = $(tpl);\n            $tabContentContainer.append($content);\n\n            // 记录到内存\n            $title._index = tabIndex;\n            tabTitleArr.push($title);\n            tabContentArr.push($content);\n\n            // 设置 active 项\n            if (tabIndex === 0) {\n                $title._active = true;\n                $title.addClass('w-e-active');\n            } else {\n                $content.hide();\n            }\n\n            // 绑定 tab 的事件\n            $title.on('click', function (e) {\n                if ($title._active) {\n                    return;\n                }\n                // 隐藏所有的 tab\n                tabTitleArr.forEach(function ($title) {\n                    $title._active = false;\n                    $title.removeClass('w-e-active');\n                });\n                tabContentArr.forEach(function ($content) {\n                    $content.hide();\n                });\n\n                // 显示当前的 tab\n                $title._active = true;\n                $title.addClass('w-e-active');\n                $content.show();\n            });\n        });\n\n        // 绑定关闭事件\n        $container.on('click', function (e) {\n            // 点击时阻止冒泡\n            e.stopPropagation();\n        });\n        $body.on('click', function (e) {\n            _this.hide();\n        });\n\n        // 添加到 DOM\n        $textContainerElem.append($container);\n\n        // 绑定 opt 的事件，只有添加到 DOM 之后才能绑定成功\n        tabs.forEach(function (tab, index) {\n            if (!tab) {\n                return;\n            }\n            var events = tab.events || [];\n            events.forEach(function (event) {\n                var selector = event.selector;\n                var type = event.type;\n                var fn = event.fn || emptyFn;\n                var $content = tabContentArr[index];\n                $content.find(selector).on(type, function (e) {\n                    e.stopPropagation();\n                    var needToHide = fn(e);\n                    // 执行完事件之后，是否要关闭 panel\n                    if (needToHide) {\n                        _this.hide();\n                    }\n                });\n            });\n        });\n\n        // focus 第一个 elem\n        var $inputs = $container.find('input[type=text],textarea');\n        if ($inputs.length) {\n            $inputs.get(0).focus();\n        }\n\n        // 添加到属性\n        this.$container = $container;\n\n        // 隐藏其他 panel\n        this._hideOtherPanels();\n        // 记录该 menu 已经创建了 panel\n        _isCreatedPanelMenus.push(menu);\n    },\n\n    // 隐藏（移除DOM）\n    hide: function hide() {\n        var menu = this.menu;\n        var $container = this.$container;\n        if ($container) {\n            $container.remove();\n        }\n\n        // 将该 menu 记录中移除\n        _isCreatedPanelMenus = _isCreatedPanelMenus.filter(function (item) {\n            if (item === menu) {\n                return false;\n            } else {\n                return true;\n            }\n        });\n    },\n\n    // 一个 panel 展示时，隐藏其他 panel\n    _hideOtherPanels: function _hideOtherPanels() {\n        if (!_isCreatedPanelMenus.length) {\n            return;\n        }\n        _isCreatedPanelMenus.forEach(function (menu) {\n            var panel = menu.panel || {};\n            if (panel.hide) {\n                panel.hide();\n            }\n        });\n    }\n};\n\n/*\n    menu - link\n*/\n// 构造函数\nfunction Link(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-link\"><i/></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nLink.prototype = {\n    constructor: Link,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        var $linkelem = void 0;\n\n        if (this._active) {\n            // 当前选区在链接里面\n            $linkelem = editor.selection.getSelectionContainerElem();\n            if (!$linkelem) {\n                return;\n            }\n            // 将该元素都包含在选取之内，以便后面整体替换\n            editor.selection.createRangeByElem($linkelem);\n            editor.selection.restoreSelection();\n            // 显示 panel\n            this._createPanel($linkelem.text(), $linkelem.attr('href'));\n        } else {\n            // 当前选区不在链接里面\n            if (editor.selection.isSelectionEmpty()) {\n                // 选区是空的，未选中内容\n                this._createPanel('', '');\n            } else {\n                // 选中内容了\n                this._createPanel(editor.selection.getSelectionText(), '');\n            }\n        }\n    },\n\n    // 创建 panel\n    _createPanel: function _createPanel(text, link) {\n        var _this = this;\n\n        // panel 中需要用到的id\n        var inputLinkId = getRandom('input-link');\n        var inputTextId = getRandom('input-text');\n        var btnOkId = getRandom('btn-ok');\n        var btnDelId = getRandom('btn-del');\n\n        // 是否显示“删除链接”\n        var delBtnDisplay = this._active ? 'inline-block' : 'none';\n\n        // 初始化并显示 panel\n        var panel = new Panel(this, {\n            width: 300,\n            // panel 中可包含多个 tab\n            tabs: [{\n                // tab 的标题\n                title: '链接',\n                // 模板\n                tpl: '<div>\\n                            <input id=\"' + inputTextId + '\" type=\"text\" class=\"block\" value=\"' + text + '\" placeholder=\"\\u94FE\\u63A5\\u6587\\u5B57\"/></td>\\n                            <input id=\"' + inputLinkId + '\" type=\"text\" class=\"block\" value=\"' + link + '\" placeholder=\"http://...\"/></td>\\n                            <div class=\"w-e-button-container\">\\n                                <button id=\"' + btnOkId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                                <button id=\"' + btnDelId + '\" class=\"gray right\" style=\"display:' + delBtnDisplay + '\">\\u5220\\u9664\\u94FE\\u63A5</button>\\n                            </div>\\n                        </div>',\n                // 事件绑定\n                events: [\n                // 插入链接\n                {\n                    selector: '#' + btnOkId,\n                    type: 'click',\n                    fn: function fn() {\n                        // 执行插入链接\n                        var $link = $('#' + inputLinkId);\n                        var $text = $('#' + inputTextId);\n                        var link = $link.val();\n                        var text = $text.val();\n                        _this._insertLink(text, link);\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                },\n                // 删除链接\n                {\n                    selector: '#' + btnDelId,\n                    type: 'click',\n                    fn: function fn() {\n                        // 执行删除链接\n                        _this._delLink();\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // tab end\n            ] // tabs end\n        });\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 删除当前链接\n    _delLink: function _delLink() {\n        if (!this._active) {\n            return;\n        }\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var selectionText = editor.selection.getSelectionText();\n        editor.cmd.do('insertHTML', '<span>' + selectionText + '</span>');\n    },\n\n    // 插入链接\n    _insertLink: function _insertLink(text, link) {\n        if (!text || !link) {\n            return;\n        }\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', '<a href=\"' + link + '\" target=\"_blank\">' + text + '</a>');\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        if ($selectionELem.getNodeName() === 'A') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    italic-menu\n*/\n// 构造函数\nfunction Italic(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-italic\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nItalic.prototype = {\n    constructor: Italic,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 italic 命令\n        editor.cmd.do('italic');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('italic')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    redo-menu\n*/\n// 构造函数\nfunction Redo(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-redo\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nRedo.prototype = {\n    constructor: Redo,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n\n        // 执行 redo 命令\n        editor.cmd.do('redo');\n    }\n};\n\n/*\n    strikeThrough-menu\n*/\n// 构造函数\nfunction StrikeThrough(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-strikethrough\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nStrikeThrough.prototype = {\n    constructor: StrikeThrough,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 strikeThrough 命令\n        editor.cmd.do('strikeThrough');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('strikeThrough')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    underline-menu\n*/\n// 构造函数\nfunction Underline(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-underline\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nUnderline.prototype = {\n    constructor: Underline,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 underline 命令\n        editor.cmd.do('underline');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('underline')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    undo-menu\n*/\n// 构造函数\nfunction Undo(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-undo\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nUndo.prototype = {\n    constructor: Undo,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n\n        // 执行 undo 命令\n        editor.cmd.do('undo');\n    }\n};\n\n/*\n    menu - list\n*/\n// 构造函数\nfunction List(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-list2\"><i/></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>设置列表</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<span><i class=\"w-e-icon-list-numbered\"></i> 有序列表</span>'), value: 'insertOrderedList' }, { $elem: $('<span><i class=\"w-e-icon-list2\"></i> 无序列表</span>'), value: 'insertUnorderedList' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 List 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nList.prototype = {\n    constructor: List,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        editor.selection.restoreSelection();\n        if (editor.cmd.queryCommandState(value)) {\n            return;\n        }\n        editor.cmd.do(value);\n\n        // 验证列表是否被包裹在 <p> 之内\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        if ($selectionElem.getNodeName() === 'LI') {\n            $selectionElem = $selectionElem.parent();\n        }\n        if (/^ol|ul$/i.test($selectionElem.getNodeName()) === false) {\n            return;\n        }\n        if ($selectionElem.equal($textElem)) {\n            // 证明是顶级标签，没有被 <p> 包裹\n            return;\n        }\n        var $parent = $selectionElem.parent();\n        if ($parent.equal($textElem)) {\n            // $parent 是顶级标签，不能删除\n            return;\n        }\n\n        $selectionElem.insertAfter($parent);\n        $parent.remove();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('insertUnOrderedList') || editor.cmd.queryCommandState('insertOrderedList')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - justify\n*/\n// 构造函数\nfunction Justify(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-paragraph-left\"><i/></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 100,\n        $title: $('<p>对齐方式</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<span><i class=\"w-e-icon-paragraph-left\"></i> 靠左</span>'), value: 'justifyLeft' }, { $elem: $('<span><i class=\"w-e-icon-paragraph-center\"></i> 居中</span>'), value: 'justifyCenter' }, { $elem: $('<span><i class=\"w-e-icon-paragraph-right\"></i> 靠右</span>'), value: 'justifyRight' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 List 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nJustify.prototype = {\n    constructor: Justify,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do(value);\n    }\n};\n\n/*\n    menu - Forecolor\n*/\n// 构造函数\nfunction ForeColor(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-pencil2\"><i/></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>文字颜色</p>'),\n        type: 'inline-block', // droplist 内容以 block 形式展示\n        list: [{ $elem: $('<i style=\"color:#000000;\" class=\"w-e-icon-pencil2\"></i>'), value: '#000000' }, { $elem: $('<i style=\"color:#eeece0;\" class=\"w-e-icon-pencil2\"></i>'), value: '#eeece0' }, { $elem: $('<i style=\"color:#1c487f;\" class=\"w-e-icon-pencil2\"></i>'), value: '#1c487f' }, { $elem: $('<i style=\"color:#4d80bf;\" class=\"w-e-icon-pencil2\"></i>'), value: '#4d80bf' }, { $elem: $('<i style=\"color:#c24f4a;\" class=\"w-e-icon-pencil2\"></i>'), value: '#c24f4a' }, { $elem: $('<i style=\"color:#8baa4a;\" class=\"w-e-icon-pencil2\"></i>'), value: '#8baa4a' }, { $elem: $('<i style=\"color:#7b5ba1;\" class=\"w-e-icon-pencil2\"></i>'), value: '#7b5ba1' }, { $elem: $('<i style=\"color:#46acc8;\" class=\"w-e-icon-pencil2\"></i>'), value: '#46acc8' }, { $elem: $('<i style=\"color:#f9963b;\" class=\"w-e-icon-pencil2\"></i>'), value: '#f9963b' }, { $elem: $('<i style=\"color:#ffffff;\" class=\"w-e-icon-pencil2\"></i>'), value: '#ffffff' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 ForeColor 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nForeColor.prototype = {\n    constructor: ForeColor,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('foreColor', value);\n    }\n};\n\n/*\n    menu - BackColor\n*/\n// 构造函数\nfunction BackColor(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-paint-brush\"><i/></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>背景色</p>'),\n        type: 'inline-block', // droplist 内容以 block 形式展示\n        list: [{ $elem: $('<i style=\"color:#000000;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#000000' }, { $elem: $('<i style=\"color:#eeece0;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#eeece0' }, { $elem: $('<i style=\"color:#1c487f;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#1c487f' }, { $elem: $('<i style=\"color:#4d80bf;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#4d80bf' }, { $elem: $('<i style=\"color:#c24f4a;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#c24f4a' }, { $elem: $('<i style=\"color:#8baa4a;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#8baa4a' }, { $elem: $('<i style=\"color:#7b5ba1;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#7b5ba1' }, { $elem: $('<i style=\"color:#46acc8;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#46acc8' }, { $elem: $('<i style=\"color:#f9963b;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#f9963b' }, { $elem: $('<i style=\"color:#ffffff;\" class=\"w-e-icon-paint-brush\"></i>'), value: '#ffffff' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 BackColor 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nBackColor.prototype = {\n    constructor: BackColor,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('backColor', value);\n    }\n};\n\n/*\n    menu - quote\n*/\n// 构造函数\nfunction Quote(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-quotes-left\"><i/>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nQuote.prototype = {\n    constructor: Quote,\n\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        if (!UA.isIE()) {\n            editor.cmd.do('formatBlock', '<BLOCKQUOTE>');\n            return;\n        }\n\n        // IE 中不支持 formatBlock <BLOCKQUOTE> ，要用其他方式兼容\n\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        var content = void 0,\n            $targetELem = void 0;\n        if ($selectionElem.getNodeName() === 'P') {\n            // 将 P 转换为 quote\n            content = $selectionElem.text();\n            $targetELem = $('<blockquote>' + content + '</blockquote>');\n            $targetELem.insertAfter($selectionElem);\n            $selectionElem.remove();\n            return;\n        }\n        if ($selectionElem.getNodeName() === 'BLOCKQUOTE') {\n            // 撤销 quote\n            content = $selectionElem.text();\n            $targetELem = $('<p>' + content + '</p>');\n            $targetELem.insertAfter($selectionElem);\n            $selectionElem.remove();\n        }\n    },\n\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var reg = /^BLOCKQUOTE$/i;\n        var cmdValue = editor.cmd.queryCommandValue('formatBlock');\n        if (reg.test(cmdValue)) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - code\n*/\n// 构造函数\nfunction Code(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-terminal\"><i/>\\n        </div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nCode.prototype = {\n    constructor: Code,\n\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        var $startElem = editor.selection.getSelectionStartElem();\n        var $endElem = editor.selection.getSelectionEndElem();\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n        var selectionText = editor.selection.getSelectionText();\n        var $code = void 0;\n\n        if (!$startElem.equal($endElem)) {\n            // 跨元素选择，不做处理\n            editor.selection.restoreSelection();\n            return;\n        }\n        if (!isSeleEmpty) {\n            // 选取不是空，用 <code> 包裹即可\n            $code = $('<code>' + selectionText + '</code>');\n            editor.cmd.do('insertElem', $code);\n            editor.selection.createRangeByElem($code, false);\n            editor.selection.restoreSelection();\n            return;\n        }\n\n        // 选取是空，且没有夸元素选择，则插入 <pre><code></code></prev>\n        if (this._active) {\n            // 选中状态，将编辑内容\n            this._createPanel($startElem.html());\n        } else {\n            // 未选中状态，将创建内容\n            this._createPanel();\n        }\n    },\n\n    _createPanel: function _createPanel(value) {\n        var _this = this;\n\n        // value - 要编辑的内容\n        value = value || '';\n        var type = !value ? 'new' : 'edit';\n        var textId = getRandom('texxt');\n        var btnId = getRandom('btn');\n\n        var panel = new Panel(this, {\n            width: 500,\n            // 一个 Panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '插入代码',\n                // 模板\n                tpl: '<div>\\n                        <textarea id=\"' + textId + '\" style=\"height:145px;;\">' + value + '</textarea>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    <div>',\n                // 事件绑定\n                events: [\n                // 插入代码\n                {\n                    selector: '#' + btnId,\n                    type: 'click',\n                    fn: function fn() {\n                        var $text = $('#' + textId);\n                        var text = $text.val() || $text.html();\n                        text = replaceHtmlSymbol(text);\n                        if (type === 'new') {\n                            // 新插入\n                            _this._insertCode(text);\n                        } else {\n                            // 编辑更新\n                            _this._updateCode(text);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // new Panel end\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入代码\n    _insertCode: function _insertCode(value) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', '<pre><code>' + value + '</code></pre><p><br></p>');\n    },\n\n    // 更新代码\n    _updateCode: function _updateCode(value) {\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        $selectionELem.html(value);\n        editor.selection.restoreSelection();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var $parentElem = $selectionELem.parent();\n        if ($selectionELem.getNodeName() === 'CODE' && $parentElem.getNodeName() === 'PRE') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - emoticon\n*/\n// 构造函数\nfunction Emoticon(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-happy\"><i/>\\n        </div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nEmoticon.prototype = {\n    constructor: Emoticon,\n\n    onClick: function onClick() {\n        this._createPanel();\n    },\n\n    _createPanel: function _createPanel() {\n        var _this = this;\n\n        // 拼接表情字符串\n        var faceHtml = '';\n        var faceStr = '😀 😃 😄 😁 😆 😅 😂  😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😜 😝 😛 🤑 🤗 🤓 😎 😏 😒 😞 😔 😟 😕 🙁  😣 😖 😫 😩 😤 😠 😡 😶 😐 😑 😯 😦 😧 😮 😲 😵 😳 😱 😨 😰 😢 😥 😭 😓 😪 😴 🙄 🤔 😬 🤐';\n        faceStr.split(/\\s/).forEach(function (item) {\n            if (item) {\n                faceHtml += '<span class=\"w-e-item\">' + item + '</span>';\n            }\n        });\n\n        var handHtml = '';\n        var handStr = '🙌 👏 👋 👍 👎 👊 ✊ ️👌 ✋ 👐 💪 🙏 ️👆 👇 👈 👉 🖕 🖐 🤘 🖖';\n        handStr.split(/\\s/).forEach(function (item) {\n            if (item) {\n                handHtml += '<span class=\"w-e-item\">' + item + '</span>';\n            }\n        });\n\n        var panel = new Panel(this, {\n            width: 300,\n            height: 200,\n            // 一个 Panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '表情',\n                // 模板\n                tpl: '<div class=\"w-e-emoticon-container\">' + faceHtml + '</div>',\n                // 事件绑定\n                events: [{\n                    selector: 'span.w-e-item',\n                    type: 'click',\n                    fn: function fn(e) {\n                        var target = e.target;\n                        _this._insert(target.innerHTML);\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            }, // first tab end\n            {\n                // 标题\n                title: '手势',\n                // 模板\n                tpl: '<div class=\"w-e-emoticon-container\">' + handHtml + '</div>',\n                // 事件绑定\n                events: [{\n                    selector: 'span.w-e-item',\n                    type: 'click',\n                    fn: function fn(e) {\n                        var target = e.target;\n                        _this._insert(target.innerHTML);\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // second tab end\n            ] // tabs end\n        });\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入表情\n    _insert: function _insert(emoji) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', '<span>' + emoji + '</span>');\n    }\n};\n\n/*\n    menu - table\n*/\n// 构造函数\nfunction Table(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-table2\"><i/></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nTable.prototype = {\n    constructor: Table,\n\n    onClick: function onClick() {\n        if (this._active) {\n            // 编辑现有表格\n            this._createEditPanel();\n        } else {\n            // 插入新表格\n            this._createInsertPanel();\n        }\n    },\n\n    // 创建插入新表格的 panel\n    _createInsertPanel: function _createInsertPanel() {\n        var _this = this;\n\n        // 用到的 id\n        var btnInsertId = getRandom('btn');\n        var textRowNum = getRandom('row');\n        var textColNum = getRandom('col');\n\n        var panel = new Panel(this, {\n            width: 250,\n            // panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '插入表格',\n                // 模板\n                tpl: '<div>\\n                        <p style=\"text-align:left; padding:5px 0;\">\\n                            \\u521B\\u5EFA\\n                            <input id=\"' + textRowNum + '\" type=\"text\" value=\"5\" style=\"width:40px;text-align:center;\"/>\\n                            \\u884C\\n                            <input id=\"' + textColNum + '\" type=\"text\" value=\"5\" style=\"width:40px;text-align:center;\"/>\\n                            \\u5217\\u7684\\u8868\\u683C\\n                        </p>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnInsertId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    // 点击按钮，插入表格\n                    selector: '#' + btnInsertId,\n                    type: 'click',\n                    fn: function fn() {\n                        var rowNum = parseInt($('#' + textRowNum).val());\n                        var colNum = parseInt($('#' + textColNum).val());\n\n                        if (rowNum && colNum && rowNum > 0 && colNum > 0) {\n                            // form 数据有效\n                            _this._insert(rowNum, colNum);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // panel end\n\n        // 展示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入表格\n    _insert: function _insert(rowNum, colNum) {\n        // 拼接 table 模板\n        var r = void 0,\n            c = void 0;\n        var html = '<table border=\"0\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\">';\n        for (r = 0; r < rowNum; r++) {\n            html += '<tr>';\n            if (r === 0) {\n                for (c = 0; c < colNum; c++) {\n                    html += '<th>&nbsp;</th>';\n                }\n            } else {\n                for (c = 0; c < colNum; c++) {\n                    html += '<td>&nbsp;</td>';\n                }\n            }\n            html += '</tr>';\n        }\n        html += '</table><p><br></p>';\n\n        // 执行命令\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', html);\n\n        // 防止 firefox 下出现 resize 的控制点\n        editor.cmd.do('enableObjectResizing', false);\n        editor.cmd.do('enableInlineTableEditing', false);\n    },\n\n    // 创建编辑表格的 panel\n    _createEditPanel: function _createEditPanel() {\n        var _this2 = this;\n\n        // 可用的 id\n        var addRowBtnId = getRandom('add-row');\n        var addColBtnId = getRandom('add-col');\n        var delRowBtnId = getRandom('del-row');\n        var delColBtnId = getRandom('del-col');\n        var delTableBtnId = getRandom('del-table');\n\n        // 创建 panel 对象\n        var panel = new Panel(this, {\n            width: 320,\n            // panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '编辑表格',\n                // 模板\n                tpl: '<div>\\n                        <div class=\"w-e-button-container\" style=\"border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;\">\\n                            <button id=\"' + addRowBtnId + '\" class=\"left\">\\u589E\\u52A0\\u884C</button>\\n                            <button id=\"' + delRowBtnId + '\" class=\"red left\">\\u5220\\u9664\\u884C</button>\\n                            <button id=\"' + addColBtnId + '\" class=\"left\">\\u589E\\u52A0\\u5217</button>\\n                            <button id=\"' + delColBtnId + '\" class=\"red left\">\\u5220\\u9664\\u5217</button>\\n                        </div>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + delTableBtnId + '\" class=\"gray left\">\\u5220\\u9664\\u8868\\u683C</button>\\n                        </dv>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    // 增加行\n                    selector: '#' + addRowBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._addRow();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 增加列\n                    selector: '#' + addColBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._addCol();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除行\n                    selector: '#' + delRowBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delRow();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除列\n                    selector: '#' + delColBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delCol();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除表格\n                    selector: '#' + delTableBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delTable();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            }]\n        });\n        // 显示 panel\n        panel.show();\n    },\n\n    // 获取选中的单元格的位置信息\n    _getLocationData: function _getLocationData() {\n        var result = {};\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var nodeName = $selectionELem.getNodeName();\n        if (nodeName !== 'TD' && nodeName !== 'TH') {\n            return;\n        }\n\n        // 获取 td index\n        var $tr = $selectionELem.parent();\n        var $tds = $tr.children();\n        var tdLength = $tds.length;\n        $tds.forEach(function (td, index) {\n            if (td === $selectionELem[0]) {\n                // 记录并跳出循环\n                result.td = {\n                    index: index,\n                    elem: td,\n                    length: tdLength\n                };\n                return false;\n            }\n        });\n\n        // 获取 tr index\n        var $tbody = $tr.parent();\n        var $trs = $tbody.children();\n        var trLength = $trs.length;\n        $trs.forEach(function (tr, index) {\n            if (tr === $tr[0]) {\n                // 记录并跳出循环\n                result.tr = {\n                    index: index,\n                    elem: tr,\n                    length: trLength\n                };\n                return false;\n            }\n        });\n\n        // 返回结果\n        return result;\n    },\n\n    // 增加行\n    _addRow: function _addRow() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var $currentTr = $(trData.elem);\n        var tdData = locationData.td;\n        var tdLength = tdData.length;\n\n        // 拼接即将插入的字符串\n        var newTr = document.createElement('tr');\n        var tpl = '',\n            i = void 0;\n        for (i = 0; i < tdLength; i++) {\n            tpl += '<td>&nbsp;</td>';\n        }\n        newTr.innerHTML = tpl;\n        // 插入\n        $(newTr).insertAfter($currentTr);\n    },\n\n    // 增加列\n    _addCol: function _addCol() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var tdData = locationData.td;\n        var tdIndex = tdData.index;\n        var $currentTr = $(trData.elem);\n        var $trParent = $currentTr.parent();\n        var $trs = $trParent.children();\n\n        // 遍历所有行\n        $trs.forEach(function (tr) {\n            var $tr = $(tr);\n            var $tds = $tr.children();\n            var $currentTd = $tds.get(tdIndex);\n            var name = $currentTd.getNodeName().toLowerCase();\n\n            // new 一个 td，并插入\n            var newTd = document.createElement(name);\n            $(newTd).insertAfter($currentTd);\n        });\n    },\n\n    // 删除行\n    _delRow: function _delRow() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var $currentTr = $(trData.elem);\n        $currentTr.remove();\n    },\n\n    // 删除列\n    _delCol: function _delCol() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var tdData = locationData.td;\n        var tdIndex = tdData.index;\n        var $currentTr = $(trData.elem);\n        var $trParent = $currentTr.parent();\n        var $trs = $trParent.children();\n\n        // 遍历所有行\n        $trs.forEach(function (tr) {\n            var $tr = $(tr);\n            var $tds = $tr.children();\n            var $currentTd = $tds.get(tdIndex);\n            // 删除\n            $currentTd.remove();\n        });\n    },\n\n    // 删除表格\n    _delTable: function _delTable() {\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var $table = $selectionELem.parentUntil('table');\n        if (!$table) {\n            return;\n        }\n        $table.remove();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var nodeName = $selectionELem.getNodeName();\n        if (nodeName === 'TD' || nodeName === 'TH') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - video\n*/\n// 构造函数\nfunction Video(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-play\"><i/></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nVideo.prototype = {\n    constructor: Video,\n\n    onClick: function onClick() {\n        this._createPanel();\n    },\n\n    _createPanel: function _createPanel() {\n        var _this = this;\n\n        // 创建 id\n        var textValId = getRandom('text-val');\n        var btnId = getRandom('btn');\n\n        // 创建 panel\n        var panel = new Panel(this, {\n            width: 350,\n            // 一个 panel 多个 tab\n            tabs: [{\n                // 标题\n                title: '插入视频',\n                // 模板\n                tpl: '<div>\\n                        <input id=\"' + textValId + '\" type=\"text\" class=\"block\" placeholder=\"\\u683C\\u5F0F\\u5982\\uFF1A<iframe src=... ></iframe>\"/>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    selector: '#' + btnId,\n                    type: 'click',\n                    fn: function fn() {\n                        var $text = $('#' + textValId);\n                        var val = $text.val().trim();\n\n                        // 测试用视频地址\n                        // <iframe height=498 width=510 src='http://player.youku.com/embed/XMjcwMzc3MzM3Mg==' frameborder=0 'allowfullscreen'></iframe>\n\n                        if (val) {\n                            // 插入视频\n                            _this._insert(val);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // panel end\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入视频\n    _insert: function _insert(val) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', val + '<p><br></p>');\n    }\n};\n\n/*\n    menu - img\n*/\n// 构造函数\nfunction Image(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-image\"><i/></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nImage.prototype = {\n    constructor: Image,\n\n    onClick: function onClick() {\n        if (this._active) {\n            this._createEditPanel();\n        } else {\n            this._createInsertPanel();\n        }\n    },\n\n    _createEditPanel: function _createEditPanel() {\n        var editor = this.editor;\n\n        // id\n        var width30 = getRandom('width-30');\n        var width50 = getRandom('width-50');\n        var width100 = getRandom('width-100');\n        var delBtn = getRandom('del-btn');\n\n        // tab 配置\n        var tabsConfig = [{\n            title: '编辑图片',\n            tpl: '<div>\\n                    <div class=\"w-e-button-container\" style=\"border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;\">\\n                        <span style=\"float:left;font-size:14px;margin:4px 5px 0 5px;color:#333;\">\\u6700\\u5927\\u5BBD\\u5EA6\\uFF1A</span>\\n                        <button id=\"' + width30 + '\" class=\"left\">30%</button>\\n                        <button id=\"' + width50 + '\" class=\"left\">50%</button>\\n                        <button id=\"' + width100 + '\" class=\"left\">100%</button>\\n                    </div>\\n                    <div class=\"w-e-button-container\">\\n                        <button id=\"' + delBtn + '\" class=\"gray left\">\\u5220\\u9664\\u56FE\\u7247</button>\\n                    </dv>\\n                </div>',\n            events: [{\n                selector: '#' + width30,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '30%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + width50,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '50%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + width100,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '100%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + delBtn,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.remove();\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }]\n        }];\n\n        // 创建 panel 并显示\n        var panel = new Panel(this, {\n            width: 300,\n            tabs: tabsConfig\n        });\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    _createInsertPanel: function _createInsertPanel() {\n        var editor = this.editor;\n        var uploadImg = editor.uploadImg;\n        var config = editor.config;\n\n        // id\n        var upTriggerId = getRandom('up-trigger');\n        var upFileId = getRandom('up-file');\n        var linkUrlId = getRandom('link-url');\n        var linkBtnId = getRandom('link-btn');\n\n        // tabs 的配置\n        var tabsConfig = [{\n            title: '上传图片',\n            tpl: '<div class=\"w-e-up-img-container\">\\n                    <div id=\"' + upTriggerId + '\" class=\"w-e-up-btn\">\\n                        <i class=\"w-e-icon-upload2\"></i>\\n                    </div>\\n                    <div style=\"display:none;\">\\n                        <input id=\"' + upFileId + '\" type=\"file\" multiple=\"multiple\" accept=\"image/jpg,image/jpeg,image/png,image/gif,image/bmp\"/>\\n                    </div>\\n                </div>',\n            events: [{\n                // 触发选择图片\n                selector: '#' + upTriggerId,\n                type: 'click',\n                fn: function fn() {\n                    var $file = $('#' + upFileId);\n                    var fileElem = $file[0];\n                    if (fileElem) {\n                        fileElem.click();\n                    } else {\n                        // 返回 true 可关闭 panel\n                        return true;\n                    }\n                }\n            }, {\n                // 选择图片完毕\n                selector: '#' + upFileId,\n                type: 'change',\n                fn: function fn() {\n                    var $file = $('#' + upFileId);\n                    var fileElem = $file[0];\n                    if (!fileElem) {\n                        // 返回 true 可关闭 panel\n                        return true;\n                    }\n\n                    // 获取选中的 file 对象列表\n                    var fileList = fileElem.files;\n                    if (fileList.length) {\n                        uploadImg.uploadImg(fileList);\n                    }\n\n                    // 返回 true 可关闭 panel\n                    return true;\n                }\n            }]\n        }, // first tab end\n        {\n            title: '网络图片',\n            tpl: '<div>\\n                    <input id=\"' + linkUrlId + '\" type=\"text\" class=\"block\" placeholder=\"\\u56FE\\u7247\\u94FE\\u63A5\"/></td>\\n                    <div class=\"w-e-button-container\">\\n                        <button id=\"' + linkBtnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                    </div>\\n                </div>',\n            events: [{\n                selector: '#' + linkBtnId,\n                type: 'click',\n                fn: function fn() {\n                    var $linkUrl = $('#' + linkUrlId);\n                    var url = $linkUrl.val().trim();\n\n                    if (url) {\n                        uploadImg.insertLinkImg(url);\n                    }\n\n                    // 返回 true 表示函数执行结束之后关闭 panel\n                    return true;\n                }\n            }]\n        } // second tab end\n        ]; // tabs end\n\n        // 判断 tabs 的显示\n        var tabsConfigResult = [];\n        if ((config.uploadImgShowBase64 || config.uploadImgServer || config.customUploadImg) && window.FileReader) {\n            // 显示“上传图片”\n            tabsConfigResult.push(tabsConfig[0]);\n        }\n        if (config.showLinkImg) {\n            // 显示“网络图片”\n            tabsConfigResult.push(tabsConfig[1]);\n        }\n\n        // 创建 panel 并显示\n        var panel = new Panel(this, {\n            width: 300,\n            tabs: tabsConfigResult\n        });\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor._selectedImg) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    所有菜单的汇总\n*/\n\n// 存储菜单的构造函数\nvar MenuConstructors = {};\n\nMenuConstructors.bold = Bold;\n\nMenuConstructors.head = Head;\n\nMenuConstructors.link = Link;\n\nMenuConstructors.italic = Italic;\n\nMenuConstructors.redo = Redo;\n\nMenuConstructors.strikeThrough = StrikeThrough;\n\nMenuConstructors.underline = Underline;\n\nMenuConstructors.undo = Undo;\n\nMenuConstructors.list = List;\n\nMenuConstructors.justify = Justify;\n\nMenuConstructors.foreColor = ForeColor;\n\nMenuConstructors.backColor = BackColor;\n\nMenuConstructors.quote = Quote;\n\nMenuConstructors.code = Code;\n\nMenuConstructors.emoticon = Emoticon;\n\nMenuConstructors.table = Table;\n\nMenuConstructors.video = Video;\n\nMenuConstructors.image = Image;\n\n/*\n    菜单集合\n*/\n// 构造函数\nfunction Menus(editor) {\n    this.editor = editor;\n    this.menus = {};\n}\n\n// 修改原型\nMenus.prototype = {\n    constructor: Menus,\n\n    // 初始化菜单\n    init: function init() {\n        var _this = this;\n\n        var editor = this.editor;\n        var config = editor.config || {};\n        var configMenus = config.menus || []; // 获取配置中的菜单\n\n        // 根据配置信息，创建菜单\n        configMenus.forEach(function (menuKey) {\n            var MenuConstructor = MenuConstructors[menuKey];\n            if (MenuConstructor && typeof MenuConstructor === 'function') {\n                // 创建单个菜单\n                _this.menus[menuKey] = new MenuConstructor(editor);\n            }\n        });\n\n        // 添加到菜单栏\n        this._addToToolbar();\n\n        // 绑定事件\n        this._bindEvent();\n    },\n\n    // 添加到菜单栏\n    _addToToolbar: function _addToToolbar() {\n        var editor = this.editor;\n        var $toolbarElem = editor.$toolbarElem;\n        var menus = this.menus;\n        var config = editor.config;\n        // config.zIndex 是配置的编辑区域的 z-index，菜单的 z-index 得在其基础上 +1\n        var zIndex = config.zIndex + 1;\n        objForEach(menus, function (key, menu) {\n            var $elem = menu.$elem;\n            if ($elem) {\n                // 设置 z-index\n                $elem.css('z-index', zIndex);\n                $toolbarElem.append($elem);\n            }\n        });\n    },\n\n    // 绑定菜单 click mouseenter 事件\n    _bindEvent: function _bindEvent() {\n        var menus = this.menus;\n        var editor = this.editor;\n        objForEach(menus, function (key, menu) {\n            var type = menu.type;\n            if (!type) {\n                return;\n            }\n            var $elem = menu.$elem;\n            var droplist = menu.droplist;\n            var panel = menu.panel;\n\n            // 点击类型，例如 bold\n            if (type === 'click' && menu.onClick) {\n                $elem.on('click', function (e) {\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    menu.onClick(e);\n                });\n            }\n\n            // 下拉框，例如 head\n            if (type === 'droplist' && droplist) {\n                $elem.on('mouseenter', function (e) {\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    // 显示\n                    droplist.showTimeoutId = setTimeout(function () {\n                        droplist.show();\n                    }, 200);\n                }).on('mouseleave', function (e) {\n                    // 隐藏\n                    droplist.hideTimeoutId = setTimeout(function () {\n                        droplist.hide();\n                    }, 0);\n                });\n            }\n\n            // 弹框类型，例如 link\n            if (type === 'panel' && menu.onClick) {\n                $elem.on('click', function (e) {\n                    e.stopPropagation();\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    // 在自定义事件中显示 panel\n                    menu.onClick(e);\n                });\n            }\n        });\n    },\n\n    // 尝试修改菜单状态\n    changeActive: function changeActive() {\n        var menus = this.menus;\n        objForEach(menus, function (key, menu) {\n            if (menu.tryChangeActive) {\n                setTimeout(function () {\n                    menu.tryChangeActive();\n                }, 100);\n            }\n        });\n    }\n};\n\n/*\n    粘贴信息的处理\n*/\n\n// 获取粘贴的纯文本\nfunction getPasteText(e) {\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData;\n    var pasteText = void 0;\n    if (clipboardData == null) {\n        pasteText = window.clipboardData && window.clipboardData.getData('text');\n    } else {\n        pasteText = clipboardData.getData('text/plain');\n    }\n\n    return replaceHtmlSymbol(pasteText);\n}\n\n// 获取粘贴的html\nfunction getPasteHtml(e, filterStyle) {\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData;\n    var pasteText = void 0,\n        pasteHtml = void 0;\n    if (clipboardData == null) {\n        pasteText = window.clipboardData && window.clipboardData.getData('text');\n    } else {\n        pasteText = clipboardData.getData('text/plain');\n        pasteHtml = clipboardData.getData('text/html');\n    }\n    if (!pasteHtml && pasteText) {\n        pasteHtml = '<p>' + replaceHtmlSymbol(pasteText) + '</p>';\n    }\n    if (!pasteHtml) {\n        return;\n    }\n\n    // 过滤word中状态过来的无用字符\n    var docSplitHtml = pasteHtml.split('</html>');\n    if (docSplitHtml.length === 2) {\n        pasteHtml = docSplitHtml[0];\n    }\n\n    // 过滤无用标签\n    pasteHtml = pasteHtml.replace(/<(meta|script|link).+?>/igm, '');\n\n    if (filterStyle) {\n        // 过滤样式\n        pasteHtml = pasteHtml.replace(/\\s?(class|style)=('|\").+?('|\")/igm, '');\n    } else {\n        // 保留样式\n        pasteHtml = pasteHtml.replace(/\\s?class=('|\").+?('|\")/igm, '');\n    }\n\n    return pasteHtml;\n}\n\n// 获取粘贴的图片文件\nfunction getPasteImgs(e) {\n    var result = [];\n    var txt = getPasteText(e);\n    if (txt) {\n        // 有文字，就忽略图片\n        return result;\n    }\n\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData || {};\n    var items = clipboardData.items;\n    if (!items) {\n        return result;\n    }\n\n    objForEach(items, function (key, value) {\n        var type = value.type;\n        if (/image/i.test(type)) {\n            result.push(value.getAsFile());\n        }\n    });\n\n    return result;\n}\n\n/*\n    编辑区域\n*/\n\n// 构造函数\nfunction Text(editor) {\n    this.editor = editor;\n}\n\n// 修改原型\nText.prototype = {\n    constructor: Text,\n\n    // 初始化\n    init: function init() {\n        // 绑定事件\n        this._bindEvent();\n    },\n\n    // 清空内容\n    clear: function clear() {\n        this.html('<p><br></p>');\n    },\n\n    // 获取 设置 html\n    html: function html(val) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        if (val == null) {\n            return $textElem.html();\n        } else {\n            $textElem.html(val);\n\n            // 初始化选取，将光标定位到内容尾部\n            editor.initSelection();\n        }\n    },\n\n    // 获取 设置 text\n    text: function text(val) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        if (val == null) {\n            return $textElem.text();\n        } else {\n            $textElem.text('<p>' + val + '</p>');\n\n            // 初始化选取，将光标定位到内容尾部\n            editor.initSelection();\n        }\n    },\n\n    // 追加内容\n    append: function append(html) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        $textElem.append($(html));\n\n        // 初始化选取，将光标定位到内容尾部\n        editor.initSelection();\n    },\n\n    // 绑定事件\n    _bindEvent: function _bindEvent() {\n        // 实时保存选取\n        this._saveRangeRealTime();\n\n        // 按回车建时的特殊处理\n        this._enterKeyHandle();\n\n        // 清空时保留 <p><br></p>\n        this._clearHandle();\n\n        // 粘贴事件（粘贴文字，粘贴图片）\n        this._pasteHandle();\n\n        // tab 特殊处理\n        this._tabHandle();\n\n        // img 点击\n        this._imgHandle();\n    },\n\n    // 实时保存选取\n    _saveRangeRealTime: function _saveRangeRealTime() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        // 保存当前的选区\n        function saveRange(e) {\n            // 随时保存选区\n            editor.selection.saveRange();\n            // 更新按钮 ative 状态\n            editor.menus.changeActive();\n        }\n        // 按键后保存\n        $textElem.on('keyup', saveRange);\n        $textElem.on('mousedown', function (e) {\n            // mousedown 状态下，鼠标滑动到编辑区域外面，也需要保存选区\n            $textElem.on('mouseleave', saveRange);\n        });\n        $textElem.on('mouseup', function (e) {\n            saveRange();\n            // 在编辑器区域之内完成点击，取消鼠标滑动到编辑区外面的事件\n            $textElem.off('mouseleave', saveRange);\n        });\n    },\n\n    // 按回车键时的特殊处理\n    _enterKeyHandle: function _enterKeyHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        // 将回车之后生成的非 <p> 的顶级标签，改为 <p>\n        function pHandle(e) {\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            var $parentElem = $selectionElem.parent();\n            if (!$parentElem.equal($textElem)) {\n                // 不是顶级标签\n                return;\n            }\n            var nodeName = $selectionElem.getNodeName();\n            if (nodeName === 'P') {\n                // 当前的标签是 P ，不用做处理\n                return;\n            }\n\n            if ($selectionElem.text()) {\n                // 有内容，不做处理\n                return;\n            }\n\n            // 插入 <p> ，并将选取定位到 <p>，删除当前标签\n            var $p = $('<p><br></p>');\n            $p.insertBefore($selectionElem);\n            editor.selection.createRangeByElem($p, true);\n            editor.selection.restoreSelection();\n            $selectionElem.remove();\n        }\n\n        $textElem.on('keyup', function (e) {\n            if (e.keyCode !== 13) {\n                // 不是回车键\n                return;\n            }\n            // 将回车之后生成的非 <p> 的顶级标签，改为 <p>\n            pHandle(e);\n        });\n\n        // <pre><code></code></pre> 回车时 特殊处理\n        function codeHandle(e) {\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var $parentElem = $selectionElem.parent();\n            var selectionNodeName = $selectionElem.getNodeName();\n            var parentNodeName = $parentElem.getNodeName();\n\n            if (selectionNodeName !== 'CODE' || parentNodeName !== 'PRE') {\n                // 不符合要求 忽略\n                return;\n            }\n\n            if (!editor.cmd.queryCommandSupported('insertHTML')) {\n                // 必须原生支持 insertHTML 命令\n                return;\n            }\n\n            // 处理：光标定位到代码末尾，联系点击两次回车，即跳出代码块\n            if (editor._willBreakCode === true) {\n                // 此时可以跳出代码块\n                // 插入 <p> ，并将选取定位到 <p>\n                var $p = $('<p><br></p>');\n                $p.insertAfter($parentElem);\n                editor.selection.createRangeByElem($p, true);\n                editor.selection.restoreSelection();\n\n                // 修改状态\n                editor._willBreakCode = false;\n\n                e.preventDefault();\n                return;\n            }\n\n            var _startOffset = editor.selection.getRange().startOffset;\n\n            // 处理：回车时，不能插入 <br> 而是插入 \\n ，因为是在 pre 标签里面\n            editor.cmd.do('insertHTML', '\\n');\n            editor.selection.saveRange();\n            if (editor.selection.getRange().startOffset === _startOffset) {\n                // 没起作用，再来一遍\n                editor.cmd.do('insertHTML', '\\n');\n            }\n\n            var codeLength = $selectionElem.html().length;\n            if (editor.selection.getRange().startOffset + 1 === codeLength) {\n                // 说明光标在代码最后的位置，执行了回车操作\n                // 记录下来，以便下次回车时候跳出 code\n                editor._willBreakCode = true;\n            }\n\n            // 阻止默认行为\n            e.preventDefault();\n        }\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 13) {\n                // 不是回车键\n                // 取消即将跳转代码块的记录\n                editor._willBreakCode = false;\n                return;\n            }\n            // <pre><code></code></pre> 回车时 特殊处理\n            codeHandle(e);\n        });\n    },\n\n    // 清空时保留 <p><br></p>\n    _clearHandle: function _clearHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 8) {\n                return;\n            }\n            var txtHtml = $textElem.html().toLowerCase().trim();\n            if (txtHtml === '<p><br></p>') {\n                // 最后剩下一个空行，就不再删除了\n                e.preventDefault();\n                return;\n            }\n        });\n\n        $textElem.on('keyup', function (e) {\n            if (e.keyCode !== 8) {\n                return;\n            }\n            var $p = void 0;\n            var txtHtml = $textElem.html().toLowerCase().trim();\n\n            // firefox 时用 txtHtml === '<br>' 判断，其他用 !txtHtml 判断\n            if (!txtHtml || txtHtml === '<br>') {\n                // 内容空了\n                $p = $('<p><br/></p>');\n                $textElem.html(''); // 一定要先清空，否则在 firefox 下有问题\n                $textElem.append($p);\n                editor.selection.createRangeByElem($p, false, true);\n                editor.selection.restoreSelection();\n            }\n        });\n    },\n\n    // 粘贴事件（粘贴文字 粘贴图片）\n    _pasteHandle: function _pasteHandle() {\n        var editor = this.editor;\n        var pasteFilterStyle = editor.config.pasteFilterStyle;\n        var $textElem = editor.$textElem;\n\n        // 粘贴文字\n        $textElem.on('paste', function (e) {\n            if (UA.isIE()) {\n                return;\n            } else {\n                // 阻止默认行为，使用 execCommand 的粘贴命令\n                e.preventDefault();\n            }\n\n            // 获取粘贴的文字\n            var pasteHtml = getPasteHtml(e, pasteFilterStyle);\n            var pasteText = getPasteText(e);\n            pasteText = pasteText.replace(/\\n/gm, '<br>');\n\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var nodeName = $selectionElem.getNodeName();\n\n            // code 中粘贴忽略\n            if (nodeName === 'CODE' || nodeName === 'PRE') {\n                return;\n            }\n\n            // 先放开注释，有问题再追查 ————\n            // // 表格中忽略，可能会出现异常问题\n            // if (nodeName === 'TD' || nodeName === 'TH') {\n            //     return\n            // }\n\n            if (nodeName === 'DIV' || $textElem.html() === '<p><br></p>' || !pasteFilterStyle) {\n                // 是 div，可粘贴过滤样式的文字和链接。另外，不过滤粘贴的样式，也可直接插入 HTML\n                if (!pasteHtml) {\n                    return;\n                }\n                try {\n                    // firefox 中，获取的 pasteHtml 可能是没有 <ul> 包裹的 <li>\n                    // 因此执行 insertHTML 会报错\n                    editor.cmd.do('insertHTML', pasteHtml);\n                } catch (ex) {\n                    // 此时使用 pasteText 来兼容一下\n                    editor.cmd.do('insertHTML', '<p>' + pasteText + '</p>');\n                }\n            } else {\n                // 不是 div，证明在已有内容的元素中粘贴，只粘贴纯文本\n                if (!pasteText) {\n                    return;\n                }\n                editor.cmd.do('insertHTML', '<p>' + pasteText + '</p>');\n            }\n        });\n\n        // 粘贴图片\n        $textElem.on('paste', function (e) {\n            if (UA.isIE()) {\n                return;\n            } else {\n                e.preventDefault();\n            }\n\n            // 获取粘贴的图片\n            var pasteFiles = getPasteImgs(e);\n            if (!pasteFiles || !pasteFiles.length) {\n                return;\n            }\n\n            // 获取当前的元素\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var nodeName = $selectionElem.getNodeName();\n\n            // code 中粘贴忽略\n            if (nodeName === 'CODE' || nodeName === 'PRE') {\n                return;\n            }\n\n            // 上传图片\n            var uploadImg = editor.uploadImg;\n            uploadImg.uploadImg(pasteFiles);\n        });\n    },\n\n    // tab 特殊处理\n    _tabHandle: function _tabHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 9) {\n                return;\n            }\n            if (!editor.cmd.queryCommandSupported('insertHTML')) {\n                // 必须原生支持 insertHTML 命令\n                return;\n            }\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var $parentElem = $selectionElem.parent();\n            var selectionNodeName = $selectionElem.getNodeName();\n            var parentNodeName = $parentElem.getNodeName();\n\n            if (selectionNodeName === 'CODE' && parentNodeName === 'PRE') {\n                // <pre><code> 里面\n                editor.cmd.do('insertHTML', '    ');\n            } else {\n                // 普通文字\n                editor.cmd.do('insertHTML', '&nbsp;&nbsp;&nbsp;&nbsp;');\n            }\n\n            e.preventDefault();\n        });\n    },\n\n    // img 点击\n    _imgHandle: function _imgHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        var selectedClass = 'w-e-selected';\n\n        // 为图片增加 selected 样式\n        $textElem.on('click', 'img', function (e) {\n            var img = this;\n            var $img = $(img);\n\n            // 去掉所有图片的 selected 样式\n            $textElem.find('img').removeClass(selectedClass);\n\n            // 为点击的图片增加样式，并记录当前图片\n            $img.addClass(selectedClass);\n            editor._selectedImg = $img;\n\n            // 修改选取\n            editor.selection.createRangeByElem($img);\n        });\n\n        // 去掉图片的 selected 样式\n        $textElem.on('click  keyup', function (e) {\n            if (e.target.matches('img')) {\n                // 点击的是图片，忽略\n                return;\n            }\n            // 取消掉 selected 样式，并删除记录\n            $textElem.find('img').removeClass(selectedClass);\n            editor._selectedImg = null;\n        });\n    }\n};\n\n/*\n    命令，封装 document.execCommand\n*/\n\n// 构造函数\nfunction Command(editor) {\n    this.editor = editor;\n}\n\n// 修改原型\nCommand.prototype = {\n    constructor: Command,\n\n    // 执行命令\n    do: function _do(name, value) {\n        var editor = this.editor;\n\n        // 如果无选区，忽略\n        if (!editor.selection.getRange()) {\n            return;\n        }\n\n        // 恢复选取\n        editor.selection.restoreSelection();\n\n        // 执行\n        var _name = '_' + name;\n        if (this[_name]) {\n            // 有自定义事件\n            this[_name](value);\n        } else {\n            // 默认 command\n            this._execCommand(name, value);\n        }\n\n        // 修改菜单状态\n        editor.menus.changeActive();\n\n        // 最后，恢复选取保证光标在原来的位置闪烁\n        editor.selection.saveRange();\n        editor.selection.restoreSelection();\n\n        // 触发 onchange\n        editor.change && editor.change();\n    },\n\n    // 自定义 insertHTML 事件\n    _insertHTML: function _insertHTML(html) {\n        var editor = this.editor;\n        var range = editor.selection.getRange();\n\n        // 保证传入的参数是 html 代码\n        var test = /^<.+>$/.test(html);\n        if (!test && !UA.isWebkit()) {\n            // webkit 可以插入非 html 格式的文字\n            throw new Error('执行 insertHTML 命令时传入的参数必须是 html 格式');\n        }\n\n        if (this.queryCommandSupported('insertHTML')) {\n            // W3C\n            this._execCommand('insertHTML', html);\n        } else if (range.insertNode) {\n            // IE\n            range.deleteContents();\n            range.insertNode($(html)[0]);\n        } else if (range.pasteHTML) {\n            // IE <= 10\n            range.pasteHTML(html);\n        }\n    },\n\n    // 插入 elem\n    _insertElem: function _insertElem($elem) {\n        var editor = this.editor;\n        var range = editor.selection.getRange();\n\n        if (range.insertNode) {\n            range.deleteContents();\n            range.insertNode($elem[0]);\n        }\n    },\n\n    // 封装 execCommand\n    _execCommand: function _execCommand(name, value) {\n        document.execCommand(name, false, value);\n    },\n\n    // 封装 document.queryCommandValue\n    queryCommandValue: function queryCommandValue(name) {\n        return document.queryCommandValue(name);\n    },\n\n    // 封装 document.queryCommandState\n    queryCommandState: function queryCommandState(name) {\n        return document.queryCommandState(name);\n    },\n\n    // 封装 document.queryCommandSupported\n    queryCommandSupported: function queryCommandSupported(name) {\n        return document.queryCommandSupported(name);\n    }\n};\n\n/*\n    selection range API\n*/\n\n// 构造函数\nfunction API(editor) {\n    this.editor = editor;\n    this._currentRange = null;\n}\n\n// 修改原型\nAPI.prototype = {\n    constructor: API,\n\n    // 获取 range 对象\n    getRange: function getRange() {\n        return this._currentRange;\n    },\n\n    // 保存选区\n    saveRange: function saveRange(_range) {\n        if (_range) {\n            // 保存已有选区\n            this._currentRange = _range;\n            return;\n        }\n\n        // 获取当前的选区\n        var selection = window.getSelection();\n        if (selection.rangeCount === 0) {\n            return;\n        }\n        var range = selection.getRangeAt(0);\n\n        // 判断选区内容是否在编辑内容之内\n        var $containerElem = this.getSelectionContainerElem(range);\n        if (!$containerElem) {\n            return;\n        }\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        if ($textElem.isContain($containerElem)) {\n            // 是编辑内容之内的\n            this._currentRange = range;\n        }\n    },\n\n    // 折叠选区\n    collapseRange: function collapseRange(toStart) {\n        if (toStart == null) {\n            // 默认为 false\n            toStart = false;\n        }\n        var range = this._currentRange;\n        if (range) {\n            range.collapse(toStart);\n        }\n    },\n\n    // 选中区域的文字\n    getSelectionText: function getSelectionText() {\n        var range = this._currentRange;\n        if (range) {\n            return this._currentRange.toString();\n        } else {\n            return '';\n        }\n    },\n\n    // 选区的 $Elem\n    getSelectionContainerElem: function getSelectionContainerElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.commonAncestorContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n    getSelectionStartElem: function getSelectionStartElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.startContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n    getSelectionEndElem: function getSelectionEndElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.endContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n\n    // 选区是否为空\n    isSelectionEmpty: function isSelectionEmpty() {\n        var range = this._currentRange;\n        if (range && range.startContainer) {\n            if (range.startContainer === range.endContainer) {\n                if (range.startOffset === range.endOffset) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    },\n\n    // 恢复选区\n    restoreSelection: function restoreSelection() {\n        var selection = window.getSelection();\n        selection.removeAllRanges();\n        selection.addRange(this._currentRange);\n    },\n\n    // 创建一个空白（即 &#8203 字符）选区\n    createEmptyRange: function createEmptyRange() {\n        var editor = this.editor;\n        var range = this.getRange();\n        var $elem = void 0;\n\n        if (!range) {\n            // 当前无 range\n            return;\n        }\n        if (!this.isSelectionEmpty()) {\n            // 当前选区必须没有内容才可以\n            return;\n        }\n\n        try {\n            // 目前只支持 webkit 内核\n            if (UA.isWebkit()) {\n                // 插入 &#8203\n                editor.cmd.do('insertHTML', '&#8203;');\n                // 修改 offset 位置\n                range.setEnd(range.endContainer, range.endOffset + 1);\n                // 存储\n                this.saveRange(range);\n            } else {\n                $elem = $('<strong>&#8203;</strong>');\n                editor.cmd.do('insertElem', $elem);\n                this.createRangeByElem($elem, true);\n            }\n        } catch (ex) {\n            // 部分情况下会报错，兼容一下\n        }\n    },\n\n    // 根据 $Elem 设置选区\n    createRangeByElem: function createRangeByElem($elem, toStart, isContent) {\n        // $elem - 经过封装的 elem\n        // toStart - true 开始位置，false 结束位置\n        // isContent - 是否选中Elem的内容\n        if (!$elem.length) {\n            return;\n        }\n\n        var elem = $elem[0];\n        var range = document.createRange();\n\n        if (isContent) {\n            range.selectNodeContents(elem);\n        } else {\n            range.selectNode(elem);\n        }\n\n        if (typeof toStart === 'boolean') {\n            range.collapse(toStart);\n        }\n\n        // 存储 range\n        this.saveRange(range);\n    }\n};\n\n/*\n    上传进度条\n*/\n\nfunction Progress(editor) {\n    this.editor = editor;\n    this._time = 0;\n    this._isShow = false;\n    this._isRender = false;\n    this._timeoutId = 0;\n    this.$textContainer = editor.$textContainerElem;\n    this.$bar = $('<div class=\"w-e-progress\"></div>');\n}\n\nProgress.prototype = {\n    constructor: Progress,\n\n    show: function show(progress) {\n        var _this = this;\n\n        // 状态处理\n        if (this._isShow) {\n            return;\n        }\n        this._isShow = true;\n\n        // 渲染\n        var $bar = this.$bar;\n        if (!this._isRender) {\n            var $textContainer = this.$textContainer;\n            $textContainer.append($bar);\n        } else {\n            this._isRender = true;\n        }\n\n        // 改变进度（节流，100ms 渲染一次）\n        if (Date.now() - this._time > 100) {\n            if (progress <= 1) {\n                $bar.css('width', progress * 100 + '%');\n                this._time = Date.now();\n            }\n        }\n\n        // 隐藏\n        var timeoutId = this._timeoutId;\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(function () {\n            _this._hide();\n        }, 500);\n    },\n\n    _hide: function _hide() {\n        var $bar = this.$bar;\n        $bar.remove();\n\n        // 修改状态\n        this._time = 0;\n        this._isShow = false;\n        this._isRender = false;\n    }\n};\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\n\n/*\n    上传图片\n*/\n\n// 构造函数\nfunction UploadImg(editor) {\n    this.editor = editor;\n}\n\n// 原型\nUploadImg.prototype = {\n    constructor: UploadImg,\n\n    // 根据 debug 弹出不同的信息\n    _alert: function _alert(alertInfo, debugInfo) {\n        var editor = this.editor;\n        var debug = editor.config.debug;\n        var customAlert = editor.config.customAlert;\n\n        if (debug) {\n            throw new Error('wangEditor: ' + (debugInfo || alertInfo));\n        } else {\n            if (customAlert && typeof customAlert === 'function') {\n                customAlert(alertInfo);\n            } else {\n                alert(alertInfo);\n            }\n        }\n    },\n\n    // 根据链接插入图片\n    insertLinkImg: function insertLinkImg(link) {\n        var _this2 = this;\n\n        if (!link) {\n            return;\n        }\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', '<img src=\"' + link + '\" style=\"max-width:100%;\"/>');\n\n        // 验证图片 url 是否有效，无效的话给出提示\n        var img = document.createElement('img');\n        img.onload = function () {\n            img = null;\n        };\n        img.onerror = function () {\n            img = null;\n            // 无法成功下载图片\n            _this2._alert('插入图片错误', 'wangEditor: \\u63D2\\u5165\\u56FE\\u7247\\u51FA\\u9519\\uFF0C\\u56FE\\u7247\\u94FE\\u63A5\\u662F \"' + link + '\"\\uFF0C\\u4E0B\\u8F7D\\u8BE5\\u94FE\\u63A5\\u5931\\u8D25');\n            return;\n        };\n        img.onabort = function () {\n            img = null;\n        };\n        img.src = link;\n    },\n\n    // 上传图片\n    uploadImg: function uploadImg(files) {\n        var _this3 = this;\n\n        if (!files || !files.length) {\n            return;\n        }\n\n        // ------------------------------ 获取配置信息 ------------------------------\n        var editor = this.editor;\n        var config = editor.config;\n        var maxSize = config.uploadImgMaxSize;\n        var maxSizeM = maxSize / 1000 / 1000;\n        var maxLength = config.uploadImgMaxLength || 10000;\n        var uploadImgServer = config.uploadImgServer;\n        var uploadImgShowBase64 = config.uploadImgShowBase64;\n        var uploadFileName = config.uploadFileName || '';\n        var uploadImgParams = config.uploadImgParams || {};\n        var uploadImgHeaders = config.uploadImgHeaders || {};\n        var hooks = config.uploadImgHooks || {};\n        var timeout = config.uploadImgTimeout || 3000;\n        var withCredentials = config.withCredentials;\n        if (withCredentials == null) {\n            withCredentials = false;\n        }\n        var customUploadImg = config.customUploadImg;\n\n        // ------------------------------ 验证文件信息 ------------------------------\n        var resultFiles = [];\n        var errInfo = [];\n        arrForEach(files, function (file) {\n            var name = file.name;\n            var size = file.size;\n\n            // chrome 低版本 name === undefined\n            if (!name || !size) {\n                return;\n            }\n\n            if (/\\.(jpg|jpeg|png|bmp|gif)$/i.test(name) === false) {\n                // 后缀名不合法，不是图片\n                errInfo.push('\\u3010' + name + '\\u3011\\u4E0D\\u662F\\u56FE\\u7247');\n                return;\n            }\n            if (maxSize < size) {\n                // 上传图片过大\n                errInfo.push('\\u3010' + name + '\\u3011\\u5927\\u4E8E ' + maxSizeM + 'M');\n                return;\n            }\n\n            // 验证通过的加入结果列表\n            resultFiles.push(file);\n        });\n        // 抛出验证信息\n        if (errInfo.length) {\n            this._alert('图片验证未通过: \\n' + errInfo.join('\\n'));\n            return;\n        }\n        if (resultFiles.length > maxLength) {\n            this._alert('一次最多上传' + maxLength + '张图片');\n            return;\n        }\n\n        // ------------------------------ 自定义上传 ------------------------------\n        if (customUploadImg && typeof customUploadImg === 'function') {\n            customUploadImg(resultFiles, this.insertLinkImg.bind(this));\n\n            // 阻止以下代码执行\n            return;\n        }\n\n        // 添加图片数据\n        var formdata = new FormData();\n        arrForEach(resultFiles, function (file) {\n            var name = uploadFileName || file.name;\n            formdata.append(name, file);\n        });\n\n        // ------------------------------ 上传图片 ------------------------------\n        if (uploadImgServer && typeof uploadImgServer === 'string') {\n            // 添加参数\n            var uploadImgServerArr = uploadImgServer.split('#');\n            uploadImgServer = uploadImgServerArr[0];\n            var uploadImgServerHash = uploadImgServerArr[1] || '';\n            objForEach(uploadImgParams, function (key, val) {\n                val = encodeURIComponent(val);\n\n                // 第一，将参数拼接到 url 中\n                if (uploadImgServer.indexOf('?') > 0) {\n                    uploadImgServer += '&';\n                } else {\n                    uploadImgServer += '?';\n                }\n                uploadImgServer = uploadImgServer + key + '=' + val;\n\n                // 第二，将参数添加到 formdata 中\n                formdata.append(key, val);\n            });\n            if (uploadImgServerHash) {\n                uploadImgServer += '#' + uploadImgServerHash;\n            }\n\n            // 定义 xhr\n            var xhr = new XMLHttpRequest();\n            xhr.open('POST', uploadImgServer);\n\n            // 设置超时\n            xhr.timeout = timeout;\n            xhr.ontimeout = function () {\n                // hook - timeout\n                if (hooks.timeout && typeof hooks.timeout === 'function') {\n                    hooks.timeout(xhr, editor);\n                }\n\n                _this3._alert('上传图片超时');\n            };\n\n            // 监控 progress\n            if (xhr.upload) {\n                xhr.upload.onprogress = function (e) {\n                    var percent = void 0;\n                    // 进度条\n                    var progressBar = new Progress(editor);\n                    if (e.lengthComputable) {\n                        percent = e.loaded / e.total;\n                        progressBar.show(percent);\n                    }\n                };\n            }\n\n            // 返回数据\n            xhr.onreadystatechange = function () {\n                var result = void 0;\n                if (xhr.readyState === 4) {\n                    if (xhr.status < 200 || xhr.status >= 300) {\n                        // hook - error\n                        if (hooks.error && typeof hooks.error === 'function') {\n                            hooks.error(xhr, editor);\n                        }\n\n                        // xhr 返回状态错误\n                        _this3._alert('上传图片发生错误', '\\u4E0A\\u4F20\\u56FE\\u7247\\u53D1\\u751F\\u9519\\u8BEF\\uFF0C\\u670D\\u52A1\\u5668\\u8FD4\\u56DE\\u72B6\\u6001\\u662F ' + xhr.status);\n                        return;\n                    }\n\n                    result = xhr.responseText;\n                    if ((typeof result === 'undefined' ? 'undefined' : _typeof(result)) !== 'object') {\n                        try {\n                            result = JSON.parse(result);\n                        } catch (ex) {\n                            // hook - fail\n                            if (hooks.fail && typeof hooks.fail === 'function') {\n                                hooks.fail(xhr, editor, result);\n                            }\n\n                            _this3._alert('上传图片失败', '上传图片返回结果错误，返回结果是: ' + result);\n                            return;\n                        }\n                    }\n                    if (!hooks.customInsert && result.errno != '0') {\n                        // hook - fail\n                        if (hooks.fail && typeof hooks.fail === 'function') {\n                            hooks.fail(xhr, editor, result);\n                        }\n\n                        // 数据错误\n                        _this3._alert('上传图片失败', '上传图片返回结果错误，返回结果 errno=' + result.errno);\n                    } else {\n                        if (hooks.customInsert && typeof hooks.customInsert === 'function') {\n                            // 使用者自定义插入方法\n                            hooks.customInsert(_this3.insertLinkImg.bind(_this3), result, editor);\n                        } else {\n                            // 将图片插入编辑器\n                            var data = result.data || [];\n                            data.forEach(function (link) {\n                                _this3.insertLinkImg(link);\n                            });\n                        }\n\n                        // hook - success\n                        if (hooks.success && typeof hooks.success === 'function') {\n                            hooks.success(xhr, editor, result);\n                        }\n                    }\n                }\n            };\n\n            // hook - before\n            if (hooks.before && typeof hooks.before === 'function') {\n                var beforeResult = hooks.before(xhr, editor, resultFiles);\n                if (beforeResult && (typeof beforeResult === 'undefined' ? 'undefined' : _typeof(beforeResult)) === 'object') {\n                    if (beforeResult.prevent) {\n                        // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传\n                        this._alert(beforeResult.msg);\n                        return;\n                    }\n                }\n            }\n\n            // 自定义 headers\n            objForEach(uploadImgHeaders, function (key, val) {\n                xhr.setRequestHeader(key, val);\n            });\n\n            // 跨域传 cookie\n            xhr.withCredentials = withCredentials;\n\n            // 发送请求\n            xhr.send(formdata);\n\n            // 注意，要 return 。不去操作接下来的 base64 显示方式\n            return;\n        }\n\n        // ------------------------------ 显示 base64 格式 ------------------------------\n        if (uploadImgShowBase64) {\n            arrForEach(files, function (file) {\n                var _this = _this3;\n                var reader = new FileReader();\n                reader.readAsDataURL(file);\n                reader.onload = function () {\n                    _this.insertLinkImg(this.result);\n                };\n            });\n        }\n    }\n};\n\n/*\n    编辑器构造函数\n*/\n\n// id，累加\nvar editorId = 1;\n\n// 构造函数\nfunction Editor(toolbarSelector, textSelector) {\n    if (toolbarSelector == null) {\n        // 没有传入任何参数，报错\n        throw new Error('错误：初始化编辑器时候未传入任何参数，请查阅文档');\n    }\n    // id，用以区分单个页面不同的编辑器对象\n    this.id = 'wangEditor-' + editorId++;\n\n    this.toolbarSelector = toolbarSelector;\n    this.textSelector = textSelector;\n\n    // 自定义配置\n    this.customConfig = {};\n}\n\n// 修改原型\nEditor.prototype = {\n    constructor: Editor,\n\n    // 初始化配置\n    _initConfig: function _initConfig() {\n        // _config 是默认配置，this.customConfig 是用户自定义配置，将它们 merge 之后再赋值\n        var target = {};\n        this.config = Object.assign(target, config, this.customConfig);\n\n        // 将语言配置，生成正则表达式\n        var langConfig = this.config.lang || {};\n        var langArgs = [];\n        objForEach(langConfig, function (key, val) {\n            // key 即需要生成正则表达式的规则，如“插入链接”\n            // val 即需要被替换成的语言，如“insert link”\n            langArgs.push({\n                reg: new RegExp(key, 'img'),\n                val: val\n\n            });\n        });\n        this.config.langArgs = langArgs;\n    },\n\n    // 初始化 DOM\n    _initDom: function _initDom() {\n        var _this = this;\n\n        var toolbarSelector = this.toolbarSelector;\n        var $toolbarSelector = $(toolbarSelector);\n        var textSelector = this.textSelector;\n\n        var config$$1 = this.config;\n        var zIndex = config$$1.zIndex;\n\n        // 定义变量\n        var $toolbarElem = void 0,\n            $textContainerElem = void 0,\n            $textElem = void 0,\n            $children = void 0;\n\n        if (textSelector == null) {\n            // 只传入一个参数，即是容器的选择器或元素，toolbar 和 text 的元素自行创建\n            $toolbarElem = $('<div></div>');\n            $textContainerElem = $('<div></div>');\n\n            // 将编辑器区域原有的内容，暂存起来\n            $children = $toolbarSelector.children();\n\n            // 添加到 DOM 结构中\n            $toolbarSelector.append($toolbarElem).append($textContainerElem);\n\n            // 自行创建的，需要配置默认的样式\n            $toolbarElem.css('background-color', '#f1f1f1').css('border', '1px solid #ccc');\n            $textContainerElem.css('border', '1px solid #ccc').css('border-top', 'none').css('height', '300px');\n        } else {\n            // toolbar 和 text 的选择器都有值，记录属性\n            $toolbarElem = $toolbarSelector;\n            $textContainerElem = $(textSelector);\n            // 将编辑器区域原有的内容，暂存起来\n            $children = $textContainerElem.children();\n        }\n\n        // 编辑区域\n        $textElem = $('<div></div>');\n        $textElem.attr('contenteditable', 'true').css('width', '100%').css('height', '100%');\n\n        // 初始化编辑区域内容\n        if ($children && $children.length) {\n            $textElem.append($children);\n        } else {\n            $textElem.append($('<p><br></p>'));\n        }\n\n        // 编辑区域加入DOM\n        $textContainerElem.append($textElem);\n\n        // 设置通用的 class\n        $toolbarElem.addClass('w-e-toolbar');\n        $textContainerElem.addClass('w-e-text-container');\n        $textContainerElem.css('z-index', zIndex);\n        $textElem.addClass('w-e-text');\n\n        // 记录属性\n        this.$toolbarElem = $toolbarElem;\n        this.$textContainerElem = $textContainerElem;\n        this.$textElem = $textElem;\n\n        // 绑定 onchange\n        $textContainerElem.on('click keyup', function () {\n            _this.change && _this.change();\n        });\n        $toolbarElem.on('click', function () {\n            this.change && this.change();\n        });\n    },\n\n    // 封装 command\n    _initCommand: function _initCommand() {\n        this.cmd = new Command(this);\n    },\n\n    // 封装 selection range API\n    _initSelectionAPI: function _initSelectionAPI() {\n        this.selection = new API(this);\n    },\n\n    // 添加图片上传\n    _initUploadImg: function _initUploadImg() {\n        this.uploadImg = new UploadImg(this);\n    },\n\n    // 初始化菜单\n    _initMenus: function _initMenus() {\n        this.menus = new Menus(this);\n        this.menus.init();\n    },\n\n    // 添加 text 区域\n    _initText: function _initText() {\n        this.txt = new Text(this);\n        this.txt.init();\n    },\n\n    // 初始化选区，将光标定位到内容尾部\n    initSelection: function initSelection(newLine) {\n        var $textElem = this.$textElem;\n        var $children = $textElem.children();\n        if (!$children.length) {\n            // 如果编辑器区域无内容，添加一个空行，重新设置选区\n            $textElem.append($('<p><br></p>'));\n            this.initSelection();\n            return;\n        }\n\n        var $last = $children.last();\n\n        if (newLine) {\n            // 新增一个空行\n            var html = $last.html().toLowerCase();\n            var nodeName = $last.getNodeName();\n            if (html !== '<br>' && html !== '<br\\/>' || nodeName !== 'P') {\n                // 最后一个元素不是 <p><br></p>，添加一个空行，重新设置选区\n                $textElem.append($('<p><br></p>'));\n                this.initSelection();\n                return;\n            }\n        }\n\n        this.selection.createRangeByElem($last, false, true);\n        this.selection.restoreSelection();\n    },\n\n    // 绑定事件\n    _bindEvent: function _bindEvent() {\n        // -------- 绑定 onchange 事件 --------\n        var onChangeTimeoutId = 0;\n        var beforeChangeHtml = this.txt.html();\n        var config$$1 = this.config;\n        var onchange = config$$1.onchange;\n        if (onchange && typeof onchange === 'function') {\n            // 触发 change 的有三个场景：\n            // 1. $textContainerElem.on('click keyup')\n            // 2. $toolbarElem.on('click')\n            // 3. editor.cmd.do()\n            this.change = function () {\n                // 判断是否有变化\n                var currentHtml = this.txt.html();\n                if (currentHtml.length === beforeChangeHtml.length) {\n                    return;\n                }\n\n                // 执行，使用节流\n                if (onChangeTimeoutId) {\n                    clearTimeout(onChangeTimeoutId);\n                }\n                onChangeTimeoutId = setTimeout(function () {\n                    // 触发配置的 onchange 函数\n                    onchange(currentHtml);\n                    beforeChangeHtml = currentHtml;\n                }, 200);\n            };\n        }\n    },\n\n    // 创建编辑器\n    create: function create() {\n        // 初始化配置信息\n        this._initConfig();\n\n        // 初始化 DOM\n        this._initDom();\n\n        // 封装 command API\n        this._initCommand();\n\n        // 封装 selection range API\n        this._initSelectionAPI();\n\n        // 添加 text\n        this._initText();\n\n        // 初始化菜单\n        this._initMenus();\n\n        // 添加 图片上传\n        this._initUploadImg();\n\n        // 初始化选区，将光标定位到内容尾部\n        this.initSelection(true);\n\n        // 绑定事件\n        this._bindEvent();\n    }\n};\n\n// 检验是否浏览器环境\ntry {\n    document;\n} catch (ex) {\n    throw new Error('请在浏览器环境下运行');\n}\n\n// polyfill\npolyfill();\n\n// 这里的 `inlinecss` 将被替换成 css 代码的内容，详情可去 ./gulpfile.js 中搜索 `inlinecss` 关键字\nvar inlinecss = '.w-e-toolbar,.w-e-text-container,.w-e-menu-panel {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-toolbar *,.w-e-text-container *,.w-e-menu-panel * {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-clear-fix:after {  content: \"\";  display: table;  clear: both;}.w-e-toolbar .w-e-droplist {  position: absolute;  left: 0;  top: 0;  background-color: #fff;  border: 1px solid #f1f1f1;  border-right-color: #ccc;  border-bottom-color: #ccc;}.w-e-toolbar .w-e-droplist .w-e-dp-title {  text-align: center;  color: #999;  line-height: 2;  border-bottom: 1px solid #f1f1f1;  font-size: 13px;}.w-e-toolbar .w-e-droplist ul.w-e-list {  list-style: none;  line-height: 1;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {  color: #333;  padding: 5px 0;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {  background-color: #f1f1f1;}.w-e-toolbar .w-e-droplist ul.w-e-block {  list-style: none;  text-align: left;  padding: 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {  display: inline-block;  *display: inline;  *zoom: 1;  padding: 3px 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {  background-color: #f1f1f1;}@font-face {  font-family: \\'w-e-icon\\';  src: url(data:application/x-font-woff;charset=utf-8;base64,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) format(\\'truetype\\');  font-weight: normal;  font-style: normal;}[class^=\"w-e-icon-\"],[class*=\" w-e-icon-\"] {  /* use !important to prevent issues with browser extensions that change fonts */  font-family: \\'w-e-icon\\' !important;  speak: none;  font-style: normal;  font-weight: normal;  font-variant: normal;  text-transform: none;  line-height: 1;  /* Better Font Rendering =========== */  -webkit-font-smoothing: antialiased;  -moz-osx-font-smoothing: grayscale;}.w-e-icon-close:before {  content: \"\\\\f00d\";}.w-e-icon-upload2:before {  content: \"\\\\e9c6\";}.w-e-icon-trash-o:before {  content: \"\\\\f014\";}.w-e-icon-header:before {  content: \"\\\\f1dc\";}.w-e-icon-pencil2:before {  content: \"\\\\e906\";}.w-e-icon-paint-brush:before {  content: \"\\\\f1fc\";}.w-e-icon-image:before {  content: \"\\\\e90d\";}.w-e-icon-play:before {  content: \"\\\\e912\";}.w-e-icon-location:before {  content: \"\\\\e947\";}.w-e-icon-undo:before {  content: \"\\\\e965\";}.w-e-icon-redo:before {  content: \"\\\\e966\";}.w-e-icon-quotes-left:before {  content: \"\\\\e977\";}.w-e-icon-list-numbered:before {  content: \"\\\\e9b9\";}.w-e-icon-list2:before {  content: \"\\\\e9bb\";}.w-e-icon-link:before {  content: \"\\\\e9cb\";}.w-e-icon-happy:before {  content: \"\\\\e9df\";}.w-e-icon-bold:before {  content: \"\\\\ea62\";}.w-e-icon-underline:before {  content: \"\\\\ea63\";}.w-e-icon-italic:before {  content: \"\\\\ea64\";}.w-e-icon-strikethrough:before {  content: \"\\\\ea65\";}.w-e-icon-table2:before {  content: \"\\\\ea71\";}.w-e-icon-paragraph-left:before {  content: \"\\\\ea77\";}.w-e-icon-paragraph-center:before {  content: \"\\\\ea78\";}.w-e-icon-paragraph-right:before {  content: \"\\\\ea79\";}.w-e-icon-terminal:before {  content: \"\\\\f120\";}.w-e-icon-page-break:before {  content: \"\\\\ea68\";}.w-e-icon-cancel-circle:before {  content: \"\\\\ea0d\";}.w-e-toolbar {  display: -webkit-box;  display: -ms-flexbox;  display: flex;  padding: 0 5px;  /* 单个菜单 */}.w-e-toolbar .w-e-menu {  position: relative;  text-align: center;  padding: 5px 10px;  cursor: pointer;}.w-e-toolbar .w-e-menu i {  color: #999;}.w-e-toolbar .w-e-menu:hover i {  color: #333;}.w-e-toolbar .w-e-active i {  color: #1e88e5;}.w-e-toolbar .w-e-active:hover i {  color: #1e88e5;}.w-e-text-container .w-e-panel-container {  position: absolute;  top: 0;  left: 50%;  border: 1px solid #ccc;  border-top: 0;  box-shadow: 1px 1px 2px #ccc;  color: #333;  background-color: #fff;  /* 为 emotion panel 定制的样式 */  /* 上传图片的 panel 定制样式 */}.w-e-text-container .w-e-panel-container .w-e-panel-close {  position: absolute;  right: 0;  top: 0;  padding: 5px;  margin: 2px 5px 0 0;  cursor: pointer;  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-close:hover {  color: #333;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title {  list-style: none;  display: -webkit-box;  display: -ms-flexbox;  display: flex;  font-size: 14px;  margin: 2px 10px 0 10px;  border-bottom: 1px solid #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-item {  padding: 3px 5px;  color: #999;  cursor: pointer;  margin: 0 3px;  position: relative;  top: 1px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-active {  color: #333;  border-bottom: 1px solid #333;  cursor: default;  font-weight: 700;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content {  padding: 10px 15px 10px 15px;  font-size: 16px;  /* 输入框的样式 */  /* 按钮的样式 */}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content button:focus {  outline: none;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea {  width: 100%;  border: 1px solid #ccc;  padding: 5px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus {  border-color: #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text] {  border: none;  border-bottom: 1px solid #ccc;  font-size: 14px;  height: 20px;  color: #333;  text-align: left;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].small {  width: 30px;  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].block {  display: block;  width: 100%;  margin: 10px 0;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {  border-bottom: 2px solid #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {  font-size: 14px;  color: #1e88e5;  border: none;  padding: 5px 10px;  background-color: #fff;  cursor: pointer;  border-radius: 3px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {  float: left;  margin-right: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {  float: right;  margin-left: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {  color: #c24f4a;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {  background-color: #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {  content: \"\";  display: table;  clear: both;}.w-e-text-container .w-e-panel-container .w-e-emoticon-container .w-e-item {  cursor: pointer;  font-size: 18px;  padding: 0 3px;  display: inline-block;  *display: inline;  *zoom: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container {  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn {  display: inline-block;  *display: inline;  *zoom: 1;  color: #999;  cursor: pointer;  font-size: 60px;  line-height: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover {  color: #333;}.w-e-text-container {  position: relative;}.w-e-text-container .w-e-progress {  position: absolute;  background-color: #1e88e5;  bottom: 0;  left: 0;  height: 1px;}.w-e-text {  padding: 0 10px;  overflow-y: scroll;}.w-e-text p,.w-e-text h1,.w-e-text h2,.w-e-text h3,.w-e-text h4,.w-e-text h5,.w-e-text table,.w-e-text pre {  margin: 10px 0;  line-height: 1.5;}.w-e-text ul,.w-e-text ol {  margin: 10px 0 10px 20px;}.w-e-text blockquote {  display: block;  border-left: 8px solid #d0e5f2;  padding: 5px 10px;  margin: 10px 0;  line-height: 1.4;  font-size: 100%;  background-color: #f1f1f1;}.w-e-text code {  display: inline-block;  *display: inline;  *zoom: 1;  background-color: #f1f1f1;  border-radius: 3px;  padding: 3px 5px;  margin: 0 3px;}.w-e-text pre code {  display: block;}.w-e-text table {  border-top: 1px solid #ccc;  border-left: 1px solid #ccc;}.w-e-text table td,.w-e-text table th {  border-bottom: 1px solid #ccc;  border-right: 1px solid #ccc;  padding: 3px 5px;}.w-e-text table th {  border-bottom: 2px solid #ccc;  text-align: center;}.w-e-text:focus {  outline: none;}.w-e-text img {  cursor: pointer;}.w-e-text img:hover {  box-shadow: 0 0 5px #333;}.w-e-text img.w-e-selected {  border: 2px solid #1e88e5;}.w-e-text img.w-e-selected:hover {  box-shadow: none;}';\n\n// 将 css 代码添加到 <style> 中\nvar style = document.createElement('style');\nstyle.type = 'text/css';\nstyle.innerHTML = inlinecss;\ndocument.getElementsByTagName('HEAD').item(0).appendChild(style);\n\n// 返回\nvar index = window.wangEditor || Editor;\n\nreturn index;\n\n})));\n"]}