/**
 * 防复制样式文件
 * 提供全面的防复制CSS样式
 */

/* 全局防复制样式 */
.prevent-copy-global {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
  
  /* 禁用拖拽 */
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
}

/* 局部防复制样式 */
.prevent-copy {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
  
  /* 禁用文本选择的视觉反馈 */
  &::selection {
    background: transparent !important;
  }
  
  &::-moz-selection {
    background: transparent !important;
  }
  
  /* 禁用图片和媒体元素的拖拽 */
  img, video, audio, canvas, svg {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    pointer-events: none !important;
    
    /* 禁用图片的右键保存 */
    -webkit-touch-callout: none !important;
  }
  
  /* 禁用链接的拖拽 */
  a {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
  }
  
  /* 禁用表格的选择 */
  table, th, td {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }
  
  /* 禁用代码块的选择 */
  pre, code {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }
}

/* 输入元素例外 - 保持正常功能 */
.prevent-copy input,
.prevent-copy textarea,
.prevent-copy select,
.prevent-copy [contenteditable="true"],
.prevent-copy [contenteditable=""],
.prevent-copy-global input,
.prevent-copy-global textarea,
.prevent-copy-global select,
.prevent-copy-global [contenteditable="true"],
.prevent-copy-global [contenteditable=""] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  pointer-events: auto !important;
  -webkit-touch-callout: default !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
}

/* Element UI 组件例外 */
.prevent-copy .el-input__inner,
.prevent-copy .el-textarea__inner,
.prevent-copy .el-select,
.prevent-copy .el-date-editor,
.prevent-copy .el-cascader,
.prevent-copy-global .el-input__inner,
.prevent-copy-global .el-textarea__inner,
.prevent-copy-global .el-select,
.prevent-copy-global .el-date-editor,
.prevent-copy-global .el-cascader {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  pointer-events: auto !important;
}

/* 防复制提示框 */
.prevent-copy-warning {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  z-index: 99999;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
  &.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
  
  &::before {
    content: '⚠️';
    margin-right: 8px;
  }
}

/* 防复制状态指示器 */
.prevent-copy-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #ff4757;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  z-index: 9999;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
  animation: pulse 2s infinite;
  
  &::before {
    content: '🔒';
    margin-right: 6px;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
  }
  50% {
    box-shadow: 0 2px 16px rgba(255, 71, 87, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
  }
}

/* 禁用打印样式 */
@media print {
  .prevent-copy,
  .prevent-copy-global {
    display: none !important;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .prevent-copy,
  .prevent-copy-global {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
  }
  
  .prevent-copy-warning {
    font-size: 12px;
    padding: 10px 20px;
  }
  
  .prevent-copy-indicator {
    top: 10px;
    right: 10px;
    font-size: 11px;
    padding: 6px 10px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .prevent-copy-warning {
    background: black;
    border: 2px solid white;
  }
  
  .prevent-copy-indicator {
    background: black;
    border: 1px solid white;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .prevent-copy-warning {
    background: rgba(255, 255, 255, 0.9);
    color: black;
  }
}

/* 特殊元素的防复制处理 */
.prevent-copy {
  /* 禁用文本框架的选择 */
  iframe {
    pointer-events: none !important;
  }
  
  /* 禁用嵌入内容的选择 */
  embed, object {
    pointer-events: none !important;
  }
  
  /* 禁用SVG元素的选择 */
  svg {
    pointer-events: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    user-select: none !important;
  }
}

/* 调试模式样式 */
.prevent-copy-debug {
  .prevent-copy {
    outline: 2px dashed #ff4757 !important;
    position: relative;
    
    &::after {
      content: 'PROTECTED';
      position: absolute;
      top: 0;
      right: 0;
      background: #ff4757;
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 0 0 0 4px;
      pointer-events: none;
    }
  }
}
