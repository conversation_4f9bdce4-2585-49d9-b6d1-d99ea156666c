/**
* Element Plus theme customization
* Modified for Element Plus compatibility
**/

/* theme color */
$--el-color-primary: #FF9900;
$--el-color-success: #13ce66;
$--el-color-warning: #ffba00;
$--el-color-danger: #ff4949;
// $--el-color-info: #1E1E1E;

$--el-button-font-weight: 400;

// $--el-color-text-regular: #1f2d3d;

$--el-border-color-light: #dfe4ed;
$--el-border-color-lighter: #e6ebf5;

$--el-table-border: 1px solid #dfe6ec;

// Element Plus uses CSS variables, so we don't need to import the full theme
// Custom styles can be added here

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--el-color-primary;
}


