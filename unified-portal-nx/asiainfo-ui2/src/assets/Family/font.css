
@font-face {
    font-family: 'SourceHanSansSC-Medium';
    src: url("SourceHanSansSC-Medium.ttf");
}

@font-face {
    font-family: 'SourceHanSansSC-Regular';
    src: url("SourceHanSansSC-Regular.ttf");
}
/* @font-face {
    font-family: 'SourceHanSansSC-Normal';
    src: url("SourceHanSansSC-Normal.otf");
} */

@font-face {
    font-family: 'SourceHanSansSC-Bold';
    src: url("SourceHanSansSC-Bold.ttf");
}


/* @font-face {
    font-family: 'SourceHanSansSC-ExtraLight';
    src: url("SourceHanSansSC-ExtraLight.otf");
}
@font-face {
    font-family: 'SourceHanSansSC-Heavy';
    src: url("SourceHanSansSC-Heavy.otf");
}
@font-face {
    font-family: 'SourceHanSansSC-Light';
    src: url("SourceHanSansSC-Light.otf");
} */

/* @font-face {
    font-family: 'SF-UI-Display-Bold';
    src: url("SF-UI-Display-Bold.otf");
}
@font-face {
    font-family: 'SF-UI-Display-Medium';
    src: url("SF-UI-Display-Medium.otf");
}

@font-face {
    font-family: 'SF-UI-Display-Regular';
    src: url("SF-UI-Display-Regular.otf");
} */