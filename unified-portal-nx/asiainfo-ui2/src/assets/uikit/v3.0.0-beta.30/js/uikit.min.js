/*! UIkit 3.0.0-beta.30 | http://www.getuikit.com | (c) 2014 - 2017 YOOtheme | MIT License */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define("uikit",["jquery"],e):t.UIkit=e(t.jQuery)}(this,function(t){"use strict";function e(){return"complete"===document.readyState||"loading"!==document.readyState&&!Ot.doScroll}function i(t){var i=function(){s(document,"DOMContentLoaded",i),s(window,"load",i),t()};e()?t():(n(document,"DOMContentLoaded",i),n(window,"load",i))}function n(t,e,i,n){void 0===n&&(n=!1),e.split(" ").forEach(function(e){return F(t).addEventListener(e,i,n)})}function s(t,e,i,n){void 0===n&&(n=!1),e.split(" ").forEach(function(e){return F(t).removeEventListener(e,i,n)})}function o(t,e,i,o,r){var a=function(n){var l=!r||r(n);l&&(s(t,e,a,o),i(n,l))};n(t,e,a,o)}function r(t,e){var i=p(e);return F(t).dispatchEvent(i),i}function a(e,i,n,s){void 0===s&&(s=!1);var o=i instanceof t.Event?i:t.Event(i);return St(e)[s?"triggerHandler":"trigger"](o,n),o}function l(t,e,i,n){return void 0===i&&(i=400),void 0===n&&(n="linear"),C(function(s,r){t=St(t);for(var a in e)t.css(a,t.css(a));var l=setTimeout(function(){return t.trigger(ae)},i);o(t,ae+" "+Pt,function(e){var i=e.type;clearTimeout(l),t.removeClass("uk-transition").css("transition",""),i===Pt?r():s()},!1,function(e){var i=e.target;return t.is(i)}),t.addClass("uk-transition").css("transition","all "+i+"ms "+n).css(e)})}function h(t,e,i,n,s){var r=arguments;return void 0===i&&(i=200),C(function(a,l){function c(){t.css("animation-duration",""),f(t,jt+"\\S*")}if((t=St(t)).hasClass(Ft))Gt(function(){return C.resolve().then(function(){return h.apply(null,r).then(a,l)})});else{var u=e+" "+jt+(s?"leave":"enter");0===e.lastIndexOf(jt,0)&&(n&&(u+=" "+jt+n),s&&(u+=" "+jt+"reverse")),c(),o(t,(he||"animationend")+" "+Ht,function(e){var i=!1;e.type===Ht?l():a(),Gt(function(){i||(t.addClass(Ft),Gt(function(){return t.removeClass(Ft)}))}),C.resolve().then(function(){i=!0,c()})},!1,function(e){var i=e.target;return t.is(i)}),t.css("animation-duration",i+"ms").addClass(u),he||Gt(function(){return Lt.cancel(t)})}})}function c(t){return t instanceof St}function u(t,e){return!!(t=St(t)).is(e)||(N(e)?t.parents(e).length:F(e).contains(t[0]))}function d(t,e,i,n){return(t=St(t)).attr(e,function(t,e){return e?e.replace(i,n):e})}function f(t,e){return d(t,"class",new RegExp("(^|\\s)"+e+"(?!\\S)","g"),"")}function p(t,e,i,n){if(void 0===e&&(e=!0),void 0===i&&(i=!1),void 0===n&&(n=!1),N(t)){var s=document.createEvent("Event");s.initEvent(t,e,i),t=s}return n&&Qt(t,n),t}function g(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0);var n=F(t).getBoundingClientRect();return n.bottom>=-1*e&&n.right>=-1*i&&n.top<=window.innerHeight+e&&n.left<=window.innerWidth+i}function m(t){var e=0;do{e+=t.offsetTop}while(t=t.offsetParent);return e}function v(){return Math.max(Ot.offsetHeight,Ot.scrollHeight)}function w(t,e,i){void 0===i&&(i=0),e=St(e);var n=St(e).length;return(t=(B(t)?t:"next"===t?i+1:"previous"===t?i-1:N(t)?parseInt(t,10):e.index(t))%n)<0?t+n:t}function b(t){return Wt[F(t).tagName.toLowerCase()]}function y(t,e){var i=H(t);return i?i.reduce(function(t,e){return j(e,t)},e):j(t)}function $(t,e){t=F(t);for(var i=0,n=[e,"data-"+e];i<n.length;i++)if(t.hasAttribute(n[i]))return t.getAttribute(n[i])}function x(t,e){return function(i){var n=arguments.length;return n?n>1?t.apply(e,arguments):t.call(e,i):t.call(e)}}function k(t,e){return Rt.call(t,e)}function C(t){if(te)return new Promise(t);var e=St.Deferred();return t(e.resolve,e.reject),e}function T(t){return t.replace(/(?:^|[-_\/])(\w)/g,function(t,e){return e?e.toUpperCase():""})}function _(t){return t.replace(/([a-z\d])([A-Z])/g,"$1-$2").toLowerCase()}function A(t){return t.replace(Yt,E)}function E(t,e){return e?e.toUpperCase():""}function S(t){return"function"==typeof t}function O(t){return null!==t&&"object"==typeof t}function D(t){return O(t)&&Object.getPrototypeOf(t)===Object.prototype}function I(t){return"boolean"==typeof t}function N(t){return"string"==typeof t}function B(t){return"number"==typeof t}function P(t){return void 0===t}function M(t){return N(t)&&t.match(/^[!>+-]/)}function H(t){return M(t)&&t.split(/(?=\s[!>+-])/g).map(function(t){return t.trim()})}function j(t,e){if(!0===t)return null;try{if(e&&M(t)&&">"!==t[0]){var i=Ut[t[0]],n=t.substr(1);e=St(e),"closest"===i&&(e=e.parent(),n=n||"*"),t=e[i](n)}else t=St(t,e)}catch(t){return null}return t.length?t:null}function F(t){return t&&(c(t)?t[0]:t)}function z(t){return I(t)?t:"true"===t||"1"===t||""===t||"false"!==t&&"0"!==t&&t}function L(t){var e=Number(t);return!isNaN(e)&&e}function W(e){return Vt(e)?e:N(e)?e.split(",").map(function(e){return t.isNumeric(e)?L(e):z(e.trim())}):[e]}function q(t){if(N(t))if("@"===t[0]){var e="media-"+t.substr(1);t=Xt[e]||(Xt[e]=parseFloat(J(e)))}else if(isNaN(t))return t;return!(!t||isNaN(t))&&"(min-width: "+t+"px)"}function R(t,e,i){return t===Boolean?z(e):t===Number?L(e):"jQuery"===t?y(e,i):"list"===t?W(e):"media"===t?q(e):t?t(e):e}function Y(t){return t?"ms"===t.substr(-2)?parseFloat(t):1e3*parseFloat(t):0}function V(t,e,i){return t.replace(new RegExp(e+"|"+i,"mg"),function(t){return t===e?i:e})}function U(t,e,i){return void 0===e&&(e=0),void 0===i&&(i=1),Math.min(Math.max(t,e),i)}function X(){}function Q(t,e,i){return(window.getComputedStyle(F(t),i)||{})[e]}function J(t){var e,i=document.documentElement,n=i.appendChild(document.createElement("div"));n.classList.add("var-"+t);try{e=Q(n,"content",":before").replace(/^["'](.*)["']$/,"$1"),e=JSON.parse(e)}catch(t){}return i.removeChild(n),e||void 0}function G(t,e){var i,n=T(t),s=T(e).toLowerCase(),o=T(e),r=document.body||document.documentElement,a=(i={},i[t]=s,i["Webkit"+n]="webkit"+o,i["Moz"+n]=s,i["o"+n]="o"+o+" o"+s,i);for(t in a)if(void 0!==r.style[t])return a[t]}function Z(){ce.scheduled||(ce.scheduled=!0,Gt(ce.flush.bind(ce)))}function K(t){for(var e;e=t.shift();)e()}function tt(t,e){var i=t.indexOf(e);return!!~i&&!!t.splice(i,1)}function et(){}function it(t,e){return(e.y-t.y)/(e.x-t.x)}function nt(t,e){function i(i){s[i]=(ue[i]||de)(t[i],e[i])}var n,s={};if(e.mixins)for(var o=0,r=e.mixins.length;o<r;o++)t=nt(t,e.mixins[o]);for(n in t)i(n);for(n in e)k(t,n)||i(n);return s}function st(t,e){try{t.contentWindow.postMessage(JSON.stringify(Qt({event:"command"},e)),"*")}catch(t){}}function ot(t){return C(function(e){o(window,"message",function(t,i){return e(i)},!1,function(e){var i=e.data;if(i&&N(i)){try{i=JSON.parse(i)}catch(t){return}return i&&t(i)}})})}function rt(e,i,n,s,o,r,a,l){n=dt(n),s=dt(s);var h={element:n,target:s};if(!e)return h;var c=at(e),u=at(i),d=u;return ut(d,n,c,-1),ut(d,s,u,1),o=ft(o,c.width,c.height),r=ft(r,u.width,u.height),o.x+=r.x,o.y+=r.y,d.left+=o.x,d.top+=o.y,l=at(l||window),a&&t.each(be,function(t,e){function i(e,i){var n=d[f]+e+i-2*o[t];if(n>=l[f]&&n+c[r]<=l[p])return d[f]=n,["element","target"].forEach(function(i){h[i][t]=e?h[i][t]===be[t][1]?be[t][2]:be[t][1]:h[i][t]}),!0}var r=e[0],f=e[1],p=e[2];if(!0===a||~a.indexOf(t)){var g=n[t]===f?-c[r]:n[t]===p?c[r]:0,m=s[t]===f?u[r]:s[t]===p?-u[r]:0;if(d[f]<l[f]||d[f]+c[r]>l[p]){var v=c[r]/2,w="center"===s[t]?-u[r]/2:0;"center"===n[t]&&(i(v,w)||i(-v,-w))||i(g,m)}}}),lt(e,d),h}function at(t){var e=ct(t=F(t)),i=e.pageYOffset,n=e.pageXOffset;if(!t.ownerDocument)return{top:i,left:n,height:e.innerHeight,width:e.innerWidth,bottom:i+e.innerHeight,right:n+e.innerWidth};var s=!1;t.offsetHeight||(s=t.style.display,t.style.display="block");var o=t.getBoundingClientRect();return!1!==s&&(t.style.display=s),{height:o.height,width:o.width,top:o.top+i,left:o.left+n,bottom:o.bottom+i,right:o.right+n}}function lt(t,e){var i=e.left,n=e.top;St(t).offset({left:i-ye.clientLeft,top:n-ye.clientTop})}function ht(t){return(t=F(t)).getBoundingClientRect().top+ct(t).pageYOffset}function ct(t){return t&&t.ownerDocument?t.ownerDocument.defaultView:window}function ut(e,i,n,s){t.each(be,function(t,o){var r=o[0],a=o[1],l=o[2];i[t]===l?e[a]+=n[r]*s:"center"===i[t]&&(e[a]+=n[r]*s/2)})}function dt(t){var e=/left|center|right/,i=/top|center|bottom/;return 1===(t=(t||"").split(" ")).length&&(t=e.test(t[0])?t.concat(["center"]):i.test(t[0])?["center"].concat(t):["center","center"]),{x:e.test(t[0])?t[0]:"center",y:i.test(t[1])?t[1]:"center"}}function ft(t,e,i){return t=(t||"").split(" "),{x:t[0]?parseFloat(t[0])*("%"===t[0][t[0].length-1]?e/100:1):0,y:t[1]?parseFloat(t[1])*("%"===t[1][t[1].length-1]?i/100:1):0}}function pt(t){switch(t){case"left":return"right";case"right":return"left";case"top":return"bottom";case"bottom":return"top";default:return t}}function gt(t){var e=t.x1,i=t.x2,n=t.y1,s=t.y2;return Math.abs(e-i)>=Math.abs(n-s)?e-i>0?"Left":"Right":n-s>0?"Up":"Down"}function mt(){ge&&clearTimeout(ge),me&&clearTimeout(me),ve&&clearTimeout(ve),ge=me=ve=null,$e={}}function vt(t){return xe||"touch"===(t.originalEvent||t).pointerType}function wt(t){It.on((e={},e["click."+t]=function(t){Ae&&Ae.bgClose&&!t.isDefaultPrevented()&&!u(t.target,Ae.panel)&&Ae.hide()},e["keydown."+t]=function(t){27===t.keyCode&&Ae&&Ae.escClose&&(t.preventDefault(),Ae.hide())},e));var e}function bt(t){It.off("click."+t).off("keydown."+t)}function yt(t){function e(t){var e=t-Date.now();return{total:e,seconds:e/1e3%60,minutes:e/1e3/60%60,hours:e/1e3/60/60%24,days:e/1e3/60/60/24}}yt.installed||t.component("countdown",{mixins:[t.mixin.class],attrs:!0,props:{date:String,clsWrapper:String},defaults:{date:"",clsWrapper:".uk-countdown-%unit%"},computed:{date:function(){return Date.parse(this.$props.date)},days:function(){return this.$el.find(this.clsWrapper.replace("%unit%","days"))},hours:function(){return this.$el.find(this.clsWrapper.replace("%unit%","hours"))},minutes:function(){return this.$el.find(this.clsWrapper.replace("%unit%","minutes"))},seconds:function(){return this.$el.find(this.clsWrapper.replace("%unit%","seconds"))},units:function(){var t=this;return["days","hours","minutes","seconds"].filter(function(e){return t[e].length})}},connected:function(){this.start()},disconnected:function(){var t=this;this.stop(),this.units.forEach(function(e){return t[e].empty()})},update:{write:function(){var t=this,i=e(this.date);i.total<=0&&(this.stop(),i.days=i.hours=i.minutes=i.seconds=0),this.units.forEach(function(e){var n=String(Math.floor(i[e]));if(n=n.length<2?"0"+n:n,t[e].text()!==n){var s=t[e];(n=n.split("")).length!==s.children().length&&s.empty().append(n.map(function(){return"<span></span>"}).join("")),n.forEach(function(t,e){return s[0].childNodes[e].innerText=t})}})}},methods:{start:function(){var t=this;this.stop(),this.date&&this.units.length&&(this.$emit(),this.timer=setInterval(function(){return t.$emit()},1e3))},stop:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}}})}function $t(t){function e(t,e,i,n,o,r){var a=(t=t in w?w[t]:w.slide).show(o);return{dir:o,current:i,next:n,show:function(t,s){var o=this;return void 0===s&&(s=0),t-=Math.round(t*s),this.translate(s),g.all([v.start(i,a[0],t,e),v.start(n,a[1],t,e)]).then(function(){o.reset(),r()},l)},stop:function(){return g.all([v.stop(n),v.stop(i)])},cancel:function(){return g.all([v.cancel(n),v.cancel(i)])},reset:function(){for(var t in a[0])s([n[0],i[0]]).css(t,"")},forward:function(t){var e=this,s=this.percent();return g.all([v.cancel(n),v.cancel(i)]).then(function(){return e.show(t,s)})},translate:function(e){var s=t.translate(e,o);i.css(s[0]),n.css(s[1])},percent:function(){return t.percent(i,n,o)}}}function i(t){return~~t}if(!$t.installed){var n=t.util,s=n.$,o=n.doc,r=n.fastdom,a=n.getIndex,l=n.noop,h=n.on,c=n.off,u=n.pointerDown,d=n.pointerMove,f=n.pointerUp,p=n.preventClick,g=n.promise,m=n.requestAnimationFrame,v=n.Transition;t.mixin.slideshow={attrs:!0,props:{autoplay:Number,animation:String,transition:String,duration:Number},defaults:{autoplay:0,animation:"slide",transition:"linear",duration:400,index:0,stack:[],threshold:10,percent:0,clsActive:"uk-active"},computed:{slides:function(){return this.list.children("."+this.clsItem)},forwardDuration:function(){return this.duration/4}},init:function(){var t=this;["start","move","end"].forEach(function(e){var i=t[e];t[e]=function(e){e=e.originalEvent||e,t.prevPos=t.pos,t.pos=(e.touches&&e.touches[0]||e).pageX,i(e)}})},connected:function(){this.startAutoplay()},events:[{name:"click",delegate:function(){return"["+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.show(s(t.currentTarget).blur().attr(this.attrItem))}},{name:u,delegate:function(){return"."+this.clsItem},handler:"start"},{name:u,handler:"stopAutoplay"},{name:"mouseenter",filter:function(){return this.autoplay},handler:function(){this.isHovering=!0}},{name:"mouseleave",filter:function(){return this.autoplay},handler:function(){this.isHovering=!1}}],methods:{start:function(t){if(!(t.button&&0!==t.button||this.slides.length<2)){t.preventDefault();var e=0;if(this.stack.length){this.percent=this._animation.percent();var i=this._animation.dir;e=this.percent*i,this.stack.splice(0,this.stack.length),this._animation.cancel(),this._animation.translate(Math.abs(e)),this.index=this.getIndex(this.index-i),this.touching=!0}h(o,d,this.move,!0),h(o,f,this.end,!0);var n=this.slides.eq(this.index);this.touch={el:n,start:this.pos+(e?n.outerWidth()*e:0)}}},move:function(n){var s=this;n.preventDefault();var o=this.touch,r=o.start,h=o.el;if(!(this.pos===this.prevPos||!this.touching&&Math.abs(r-this.pos)<this.threshold)){this.touching=!0;var c=(this.pos-r)/h.outerWidth();if(this.percent!==c){var u=i(this.percent)!==i(c),d=this.getIndex(this.index-i(c)),f=this.slides.eq(d),p=c<0?1:-1,g=a(c<0?"next":"previous",this.slides,d),m=this.slides.eq(g);this.slides.each(function(t,e){return s.$toggleClass(e,s.clsActive,t===d||t===g)}),u&&this._animation&&this._animation.reset(),this._animation=new e(this.animation,this.transition,f,m,p,l),this._animation.translate(Math.abs(c%1)),this.percent=c,t.update(null,f),t.update(null,m)}}},end:function(t){if(t.preventDefault(),c(o,d,this.move,!0),c(o,f,this.end,!0),this.touching){var e=this.percent;this.percent=Math.abs(this.percent)%1,this.index=this.getIndex(this.index-i(e)),this.percent<.2&&(this.index=this.getIndex(e>0?"previous":"next"),this.percent=1-this.percent,e*=-1),this.show(e>0?"previous":"next",!0),p()}this.pos=this.prevPos=this.touch=this.touching=this.percent=null},show:function(i,n){var s=this;if(void 0===n&&(n=!1),n||!this.touch)if(this.stack[n?"unshift":"push"](i),!n&&this.stack.length>1)2===this.stack.length&&this._animation.forward(this.forwardDuration);else{var o=this.slides.hasClass("uk-active"),a="next"===i?1:"previous"===i?-1:i<this.index?-1:1;if(i=this.getIndex(i),o&&i===this.index)this.stack[n?"shift":"pop"]();else{var l=o&&this.slides.eq(this.index),h=this.slides.eq(i);this.$el.trigger("beforeitemshow",[this,h]),l&&this.$el.trigger("beforeitemhide",[this,l]),this.index=i,this.$addClass(h,this.clsActive),this._animation=new e(l?this.animation:"scale",this.transition,l||h,h,a,function(){l&&s.$removeClass(l,s.clsActive),s.stack.shift(),s.stack.length?m(function(){return s.show(s.stack.shift(),!0)}):s._animation=null,s.$el.trigger("itemshown",[s,h]),t.update(null,h),l&&(s.$el.trigger("itemhidden",[s,l]),t.update(null,l))}),this._animation.show(this.stack.length>1?this.forwardDuration:this.duration,this.percent),this.$el.trigger("itemshow",[this,h]),l&&(this.$el.trigger("itemhide",[this,l]),t.update(null,l)),t.update(null,h),r.flush()}}},getIndex:function(t){return void 0===t&&(t=this.index),a(t,this.slides,this.index)},startAutoplay:function(){var t=this;this.stopAutoplay(),this.autoplay&&(this.interval=setInterval(function(){!t.isHovering&&t.show("next")},this.autoplay))},stopAutoplay:function(){this.interval&&clearInterval(this.interval)}}};var w={fade:{show:function(){return[{opacity:0},{opacity:1}]},percent:function(t){return 1-t.css("opacity")},translate:function(t){return[{opacity:1-t},{opacity:t}]}},slide:{show:function(t){return[{transform:"translate3d("+-100*t+"%, 0, 0)"},{transform:"translate3d(0, 0, 0)"}]},percent:function(t){return Math.abs(t.css("transform").split(",")[4]/t.outerWidth())},translate:function(t,e){return[{transform:"translate3d("+-100*e*t+"%, 0, 0)"},{transform:"translate3d("+100*e*(1-t)+"%, 0, 0)"}]}},scale:{show:function(){return[{opacity:0,transform:"scale3d(0.8, 0.8, 1)"},{opacity:1,transform:"scale3d(1, 1, 1)"}]},percent:function(t){return 1-t.css("opacity")},translate:function(t){var e=1-.2*t,i=.8+.2*t;return[{opacity:1-t,transform:"scale3d("+e+", "+e+", 1)"},{opacity:t,transform:"scale3d("+i+", "+i+", 1)"}]}},swipe:{show:function(t){return t<0?[{opacity:1,transform:"translate3d(100%, 0, 0)",zIndex:0},{opacity:1,transform:"scale3d(1, 1, 1) translate3d(0, 0, 0)",zIndex:-1}]:[{opacity:.3,transform:"scale3d(0.8, 0.8, 1) translate3d(-20%, 0, 0)",zIndex:-1},{opacity:1,transform:"translate3d(0, 0, 0)",zIndex:0}]},percent:function(t,e,i){var n=i<0?t:e,s=Math.abs(n.css("transform").split(",")[4]/n.outerWidth());return i<0?s:1-s},translate:function(t,e){var i;return e<0?(i=1-.2*(1-t),[{opacity:1,transform:"translate3d("+100*t+"%, 0, 0)",zIndex:0},{opacity:.3+.7*t,transform:"scale3d("+i+", "+i+", 1) translate3d("+-20*(1-t)+"%, 0, 0)",zIndex:-1}]):(i=1-.2*t,[{opacity:1-.7*t,transform:"scale3d("+i+", "+i+", 1) translate3d("+-20*t+"%, 0, 0)",zIndex:-1},{opacity:1,transform:"translate3d("+100*(1-t)+"%, 0, 0)",zIndex:0}])}}}}}function xt(t){function e(t,e,i){void 0===i&&(i="in"),t.each(function(n){return a[i](t.eq(n).attr("hidden",!1),e).then(function(){"out"===i&&t.eq(n).attr("hidden",!0)})})}function i(t,e,i){return'<iframe src="'+t+'" width="'+e+'" height="'+i+'" style="max-width: 100%; box-sizing: border-box;" uk-video uk-responsive></iframe>'}if(!xt.installed){t.use($t);var n=t.mixin,s=t.util,o=s.$,r=s.$trigger,a=s.Animation,l=s.ajax,h=s.assign,c=s.doc,u=s.docElement,d=s.getData,f=s.getImage,p=s.pointerDown,g=s.pointerMove,m=s.Transition;t.component("lightbox",{attrs:!0,props:{animation:String,toggle:String},defaults:{animation:void 0,toggle:"a"},computed:{toggles:function(){var t=this,e=o(this.toggle,this.$el);return this._changed=!this._toggles||e.length!==this._toggles.length||e.toArray().some(function(e,i){return e!==t._toggles.get(i)}),this._toggles=e}},disconnected:function(){this.panel&&(this.panel.$destroy(!0),this.panel=null)},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),this.show(this.toggles.index(o(t.currentTarget).blur()))}}],update:function(){this.panel&&this.animation&&(this.panel.$props.animation=this.animation,this.panel.$emit()),this.toggles.length&&this._changed&&this.panel&&(this.panel.$destroy(!0),this._init())},methods:{_init:function(){return this.panel=this.panel||t.lightboxPanel({animation:this.animation,items:this.toggles.toArray().reduce(function(t,e){return t.push(["href","caption","type"].reduce(function(t,i){return t["href"===i?"source":i]=d(e,i),t},{})),t},[])})},show:function(t){return this.panel||this._init(),this.panel.show(t)},hide:function(){return this.panel&&this.panel.hide()}}}),t.component("lightbox-panel",{mixins:[n.togglable,n.slideshow],functional:!0,defaults:{preload:1,delayControls:3e3,items:[],cls:"uk-open",clsPage:"uk-lightbox-page",clsItem:"uk-lightbox-item",attrItem:"uk-lightbox-item",template:' <div class="uk-lightbox uk-overflow-hidden"> <ul class="uk-lightbox-items"></ul> <div class="uk-lightbox-toolbar uk-position-top uk-text-right"> <button class="uk-lightbox-toolbar-icon uk-close-large" type="button" uk-close uk-toggle="!.uk-lightbox"></button> </div> <a class="uk-lightbox-button uk-position-center-left uk-position-medium" href="#" uk-slidenav-previous uk-lightbox-item="previous"></a> <a class="uk-lightbox-button uk-position-center-right uk-position-medium" href="#" uk-slidenav-next uk-lightbox-item="next"></a> <div class="uk-lightbox-toolbar uk-lightbox-caption uk-position-bottom uk-text-center"></div> </div>'},computed:{container:function(){return o(!0===this.$props.container&&t.container||this.$props.container||t.container)}},created:function(){var t=this;this.$mount(o(this.template).appendTo(this.container)[0]),this.list=this.$el.find(".uk-lightbox-items"),this.toolbars=this.$el.find(".uk-lightbox-toolbar"),this.nav=this.$el.find("a[uk-lightbox-item]"),this.caption=this.$el.find(".uk-lightbox-caption"),this.items.forEach(function(e,i){return t.list.append('<li class="'+t.clsItem+" item-"+i+'"></li>')})},events:[{name:g+" "+p+" keydown",handler:"showControls"},{name:"click",self:!0,handler:function(t){t.preventDefault(),this.hide()}},{name:"click",self:!0,delegate:function(){return"."+this.clsItem},handler:function(t){t.preventDefault(),this.hide()}},{name:"show",self:!0,handler:function(){this.$addClass(u,this.clsPage)}},{name:"shown",self:!0,handler:function(){this.$addClass(this.caption,"uk-animation-slide-bottom"),this.toolbars.attr("hidden",!0),this.nav.attr("hidden",!0),this.showControls()}},{name:"hide",self:!0,handler:function(){this.$removeClass(this.caption,"uk-animation-slide-bottom"),this.toolbars.attr("hidden",!0),this.nav.attr("hidden",!0)}},{name:"hidden",self:!0,handler:function(){this.$removeClass(u,this.clsPage)}},{name:"keydown",el:function(){return c},handler:function(t){if(this.isToggled(this.$el))switch(t.keyCode){case 27:this.hide();break;case 37:this.show("previous");break;case 39:this.show("next")}}},{name:"toggle",handler:function(t){t.preventDefault(),this.toggle()}},{name:"beforeitemshow",self:!0,handler:function(){this.isToggled()||this.toggleNow(this.$el,!0)}},{name:"itemshow",self:!0,handler:function(){var t=this,e=this.getItem().caption;this.caption.toggle(!!e).html(e);for(var i=0;i<=this.preload;i++)t.loadItem(t.getIndex(t.index+i)),t.loadItem(t.getIndex(t.index-i))}},{name:"itemload",handler:function(t,e){var n,s=this,r=e.source,a=e.type;if(this.setItem(e,"<span uk-spinner></span>"),r){if("image"===a||r.match(/\.(jp(e)?g|png|gif|svg)$/i))f(r).then(function(t){return s.setItem(e,'<img width="'+t.width+'" height="'+t.height+'" src="'+r+'">')},function(){return s.setError(e)});else if("video"===a||r.match(/\.(mp4|webm|ogv)$/i))var h=o("<video controls playsinline uk-video></video>").on("loadedmetadata",function(){return s.setItem(e,h.attr({width:h[0].videoWidth,height:h[0].videoHeight}))}).on("error",function(){return s.setError(e)}).attr("src",r);else if("iframe"===a)this.setItem(e,'<iframe class="uk-lightbox-iframe" src="'+r+'" frameborder="0" allowfullscreen></iframe>');else if(n=r.match(/\/\/.*?youtube\.[a-z]+\/watch\?v=([^&\s]+)/)||r.match(/youtu\.be\/(.*)/)){var c=n[1],u=function(t,n){return void 0===t&&(t=640),void 0===n&&(n=450),s.setItem(e,i("//www.youtube.com/embed/"+c,t,n))};f("//img.youtube.com/vi/"+c+"/maxresdefault.jpg").then(function(t){120===t.width&&90===t.height?f("//img.youtube.com/vi/"+c+"/0.jpg").then(function(t){return u(t.width,t.height)},u):u(t.width,t.height)},u)}else{if(!(n=r.match(/(\/\/.*?)vimeo\.[a-z]+\/([0-9]+).*?/)))return;l({type:"GET",url:"//vimeo.com/api/oembed.json?url="+encodeURI(r),jsonp:"callback",dataType:"jsonp"}).then(function(t){var o=t.height,r=t.width;return s.setItem(e,i("//player.vimeo.com/video/"+n[2],r,o))})}return!0}}}],methods:{toggle:function(){return this.isToggled()?this.hide():this.show()},hide:function(){this.isToggled()&&this.toggleNow(this.$el,!1),this.slides.removeClass(this.clsActive).each(function(t,e){return m.stop(e)}),delete this.index,delete this.percent,delete this._animation},loadItem:function(t){void 0===t&&(t=this.index);var e=this.getItem(t);e.content||r(this.$el,"itemload",[e],!0).result||this.setError(e)},getItem:function(t){return void 0===t&&(t=this.index),this.items[t]||{}},setItem:function(e,i){h(e,{content:i});var n=this.slides.eq(this.items.indexOf(e)).html(i);this.$el.trigger("itemloaded",[this,n]),t.update(null,n)},setError:function(t){this.setItem(t,'<span uk-icon="icon: bolt; ratio: 2"></span>')},showControls:function(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),this.toolbars.attr("hidden")&&(e(this.toolbars.eq(0),"uk-animation-slide-top"),e(this.toolbars.eq(1),"uk-animation-slide-bottom"),this.nav.attr("hidden",this.items.length<=1),this.items.length>1&&e(this.nav,"uk-animation-fade"))},hideControls:function(){this.toolbars.attr("hidden")||(e(this.toolbars.eq(0),"uk-animation-slide-top","out"),e(this.toolbars.eq(1),"uk-animation-slide-bottom","out"),this.items.length>1&&e(this.nav,"uk-animation-fade","out"))}}})}}function kt(t){if(!kt.installed){var e=t.util,i=e.$,n=e.each,s=e.pointerEnter,o=e.pointerLeave,r=e.Transition,a={};t.component("notification",{functional:!0,args:["message","status"],defaults:{message:"",status:"",timeout:5e3,group:null,pos:"top-center",onClose:null,clsClose:"uk-notification-close",clsMsg:"uk-notification-message"},created:function(){a[this.pos]||(a[this.pos]=i('<div class="uk-notification uk-notification-'+this.pos+'"></div>').appendTo(t.container)),this.$mount(i('<div class="'+this.clsMsg+(this.status?" "+this.clsMsg+"-"+this.status:"")+'"> <a href="#" class="'+this.clsClose+'" data-uk-close></a> <div>'+this.message+"</div> </div>").appendTo(a[this.pos].show())[0])},ready:function(){var t=this,e=parseInt(this.$el.css("margin-bottom"),10);r.start(this.$el.css({opacity:0,marginTop:-1*this.$el.outerHeight(),marginBottom:0}),{opacity:1,marginTop:0,marginBottom:e}).then(function(){t.timeout&&(t.timer=setTimeout(t.close,t.timeout))})},events:(l={click:function(t){i(t.target).closest('a[href="#"]').length&&t.preventDefault(),this.close()}},l[s]=function(){this.timer&&clearTimeout(this.timer)},l[o]=function(){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},l),methods:{close:function(t){var e=this,i=function(){e.onClose&&e.onClose(),e.$el.trigger("close",[e]).remove(),a[e.pos].children().length||a[e.pos].hide()};this.timer&&clearTimeout(this.timer),t?i():r.start(this.$el,{opacity:0,marginTop:-1*this.$el.outerHeight(),marginBottom:0}).then(i)}}});var l;t.notification.closeAll=function(e,i){n(t.instances,function(t,n){"notification"!==n.$options.name||e&&e!==n.group||n.close(i)})}}}function Ct(t){function e(i){return t.getComponent(i,"sortable")||i.parentNode&&e(i.parentNode)}if(!Ct.installed){var i=t.mixin,n=t.util,s=n.$,o=n.assign,r=n.docElement,a=n.docHeight,l=n.fastdom,h=n.getDimensions,c=n.isWithin,u=n.offset,d=n.offsetTop,f=n.pointerDown,p=n.pointerMove,g=n.pointerUp,m=n.preventClick,v=n.promise,w=n.win;t.component("sortable",{mixins:[i.class],props:{group:String,animation:Number,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},defaults:{group:!1,animation:150,threshold:5,clsItem:"uk-sortable-item",clsPlaceholder:"uk-sortable-placeholder",clsDrag:"uk-sortable-drag",clsDragState:"uk-drag",clsBase:"uk-sortable",clsNoDrag:"uk-sortable-nodrag",clsEmpty:"uk-sortable-empty",clsCustom:"",handle:!1},init:function(){var t=this;["init","start","move","end"].forEach(function(e){var i=t[e];t[e]=function(e){e=e.originalEvent||e,t.scrollY=window.scrollY;var n=e.touches&&e.touches[0]||e,s=n.pageX,o=n.pageY;t.pos={x:s,y:o},i(e)}})},events:(b={},b[f]="init",b),update:{write:function(){var t=this;if(this.clsEmpty&&this.$toggleClass(this.clsEmpty,!this.$el.children().length),this.drag){u(this.drag,{top:this.pos.y+this.origin.top,left:this.pos.x+this.origin.left});var e=d(this.drag),i=e+this.drag[0].offsetHeight;e>0&&e<this.scrollY?setTimeout(function(){return w.scrollTop(t.scrollY-5)},5):i<a()&&i>window.innerHeight+this.scrollY&&setTimeout(function(){return w.scrollTop(t.scrollY+5)},5)}}},methods:{init:function(t){var e=s(t.target),i=this.$el.children().filter(function(e,i){return c(t.target,i)});!i.length||e.is(":input")||this.handle&&!c(e,this.handle)||t.button&&0!==t.button||c(e,"."+this.clsNoDrag)||t.defaultPrevented||(t.preventDefault(),this.touched=[this],this.placeholder=i,this.origin=o({target:e,index:this.placeholder.index()},this.pos),r.on(p,this.move),r.on(g,this.end),w.on("scroll",this.scroll),this.threshold||this.start(t))},start:function(e){this.drag=s(this.placeholder[0].outerHTML.replace(/^<li/i,"<div").replace(/li>$/i,"div>")).attr("uk-no-boot","").addClass(this.clsDrag+" "+this.clsCustom).css({boxSizing:"border-box",width:this.placeholder.outerWidth(),height:this.placeholder.outerHeight()}).css(this.placeholder.css(["paddingLeft","paddingRight","paddingTop","paddingBottom"])).appendTo(t.container),this.drag.children().first().height(this.placeholder.children().height());var i=h(this.placeholder),n=i.left,a=i.top;o(this.origin,{left:n-this.pos.x,top:a-this.pos.y}),this.placeholder.addClass(this.clsPlaceholder),this.$el.children().addClass(this.clsItem),r.addClass(this.clsDragState),this.$el.trigger("start",[this,this.placeholder,this.drag]),this.move(e)},move:function(t){if(this.drag){this.$emit();var i="mousemove"===t.type?t.target:document.elementFromPoint(this.pos.x-document.body.scrollLeft,this.pos.y-document.body.scrollTop),n=e(i),o=e(this.placeholder[0]),r=n!==o;if(n&&!c(i,this.placeholder)&&(!r||n.group&&n.group===o.group)){if(i=n.$el.is(i.parentNode)&&s(i)||n.$el.children().has(i),r)o.remove(this.placeholder);else if(!i.length)return;n.insert(this.placeholder,i),~this.touched.indexOf(n)||this.touched.push(n)}}else(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t)},scroll:function(){var t=window.scrollY;t!==this.scrollY&&(this.pos.y+=t-this.scrollY,this.scrollY=t,this.$emit())},end:function(t){if(r.off(p,this.move),r.off(g,this.end),w.off("scroll",this.scroll),this.drag){m();var i=e(this.placeholder[0]);this===i?this.origin.index!==this.placeholder.index()&&this.$el.trigger("change",[this,this.placeholder,"moved"]):(i.$el.trigger("change",[i,this.placeholder,"added"]),this.$el.trigger("change",[this,this.placeholder,"removed"])),this.$el.trigger("stop",[this]),this.drag.remove(),this.drag=null;var n=this.touched.map(function(t){return t.clsPlaceholder+" "+t.clsItem}).join(" ");this.touched.forEach(function(t){return t.$el.children().removeClass(n)}),r.removeClass(this.clsDragState)}else"mouseup"!==t.type&&c(t.target,"a[href]")&&(location.href=s(t.target).closest("a[href]").attr("href"))},insert:function(t,e){var i=this;this.$el.children().addClass(this.clsItem);var n=function(){e.length?!i.$el.has(t).length||t.prevAll().filter(e).length?t.insertBefore(e):t.insertAfter(e):i.$el.append(t)};this.animation?this.animate(n):n()},remove:function(t){this.$el.has(t).length&&(this.animation?this.animate(function(){return t.detach()}):t.detach())},animate:function(t){var e=this,i=[],n=this.$el.children().toArray().map(function(t){return t=s(t),i.push(o({position:"absolute",pointerEvents:"none",width:t.outerWidth(),height:t.outerHeight()},t.position())),t}),r={position:"",width:"",height:"",pointerEvents:"",top:"",left:""};t(),n.forEach(function(t){return t.stop()}),this.$el.children().css(r),this.$update("update",!0),l.flush(),this.$el.css("min-height",this.$el.height());var a=n.map(function(t){return t.position()});v.all(n.map(function(t,n){return t.css(i[n]).animate(a[n],e.animation).promise()})).then(function(){e.$el.css("min-height","").children().css(r),e.$update("update",!0),l.flush()})}}});var b}}function Tt(t){if(!Tt.installed){var e=t.util,i=t.mixin,n=e.$,s=e.doc,o=e.fastdom,r=e.flipPosition,a=e.isTouch,l=e.isWithin,h=e.pointerDown,c=e.pointerEnter,u=e.pointerLeave,d=[];t.component("tooltip",{attrs:!0,mixins:[i.togglable,i.position],props:{delay:Number,container:Boolean,title:String},defaults:{pos:"top",title:"",delay:0,animation:["uk-animation-scale-up"],duration:100,cls:"uk-active",clsPos:"uk-tooltip",container:!0},computed:{container:function(){return n(!0===this.$props.container&&t.container||this.$props.container||t.container)}},connected:function(){var t=this;o.mutate(function(){return t.$el.removeAttr("title").attr("aria-expanded",!1)})},disconnected:function(){this.hide()},methods:{show:function(){var t=this;~d.indexOf(this)||(d.forEach(function(t){return t.hide()}),d.push(this),s.on("click."+this.$options.name,function(e){l(e.target,t.$el)||t.hide()}),clearTimeout(this.showTimer),this.tooltip=n('<div class="'+this.clsPos+'" aria-hidden="true"><div class="'+this.clsPos+'-inner">'+this.title+"</div></div>").appendTo(this.container),this.$el.attr("aria-expanded",!0),this.positionAt(this.tooltip,this.$el),this.origin="y"===this.getAxis()?r(this.dir)+"-"+this.align:this.align+"-"+r(this.dir),this.showTimer=setTimeout(function(){t.toggleElement(t.tooltip,!0),t.hideTimer=setInterval(function(){t.$el.is(":visible")||t.hide()},150)},this.delay))},hide:function(){var t=d.indexOf(this);!~t||this.$el.is("input")&&this.$el[0]===document.activeElement||(d.splice(t,1),clearTimeout(this.showTimer),clearInterval(this.hideTimer),this.$el.attr("aria-expanded",!1),this.toggleElement(this.tooltip,!1),this.tooltip&&this.tooltip.remove(),this.tooltip=!1,s.off("click."+this.$options.name))}},events:(f={blur:"hide"},f["focus "+c+" "+h]=function(t){t.type===h&&a(t)||this.show()},f[u]=function(t){a(t)||this.hide()},f)});var f}}function _t(t){function e(t,e){return e.match(new RegExp("^"+t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")+"$","i"))}function i(t,e){for(var i=[],n=0;n<t.length;n+=e){for(var s=[],o=0;o<e;o++)s.push(t[n+o]);i.push(s)}return i}if(!_t.installed){var n=t.util,s=n.$,o=n.ajax,r=n.on;t.component("upload",{props:{allow:String,clsDragover:String,concurrent:Number,dataType:String,mime:String,msgInvalidMime:String,msgInvalidName:String,multiple:Boolean,name:String,params:Object,type:String,url:String},defaults:{allow:!1,clsDragover:"uk-dragover",concurrent:1,dataType:void 0,mime:!1,msgInvalidMime:"Invalid File Type: %s",msgInvalidName:"Invalid File Name: %s",multiple:!1,name:"files[]",params:{},type:"POST",url:"",abort:null,beforeAll:null,beforeSend:null,complete:null,completeAll:null,error:null,fail:function(t){alert(t)},load:null,loadEnd:null,loadStart:null,progress:null},events:{change:function(t){s(t.target).is('input[type="file"]')&&(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop:function(t){t.preventDefault(),t.stopPropagation();var e=t.originalEvent.dataTransfer;e&&e.files&&(this.$removeClass(this.clsDragover),this.upload(e.files))},dragenter:function(t){t.preventDefault(),t.stopPropagation()},dragover:function(t){t.preventDefault(),t.stopPropagation(),this.$addClass(this.clsDragover)},dragleave:function(t){t.preventDefault(),t.stopPropagation(),this.$removeClass(this.clsDragover)}},methods:{upload:function(t){var n=this;if(t.length){this.$el.trigger("upload",[t]);for(var a=0;a<t.length;a++){if(n.allow&&!e(n.allow,t[a].name))return void n.fail(n.msgInvalidName.replace(/%s/,n.allow));if(n.mime&&!e(n.mime,t[a].type))return void n.fail(n.msgInvalidMime.replace(/%s/,n.mime))}this.multiple||(t=[t[0]]),this.beforeAll&&this.beforeAll(this,t);var l=i(t,this.concurrent),h=function(t){var e=new FormData;t.forEach(function(t){return e.append(n.name,t)});for(var i in n.params)e.append(i,n.params[i]);o({data:e,url:n.url,type:n.type,dataType:n.dataType,beforeSend:n.beforeSend,complete:[n.complete,function(t,e){l.length?h(l.shift()):n.completeAll&&n.completeAll(t),"abort"===e&&n.abort&&n.abort(t)}],cache:!1,contentType:!1,processData:!1,xhr:function(){var t=s.ajaxSettings.xhr();return t.upload&&n.progress&&r(t.upload,"progress",n.progress),["loadStart","load","loadEnd","error","abort"].forEach(function(e){return n[e]&&r(t,e.toLowerCase(),n[e])}),t}})};h(l.shift())}}}})}}function At(t){function e(t,e){return t.sort(function(t,i){return t[e]>i[e]?1:i[e]>t[e]?-1:0})}if(!At.installed){var i=t.util.scrolledOver;t.component("grid-parallax",t.components.grid.extend({props:{target:String,translate:Number},defaults:{target:!1,translate:150},init:function(){this.$addClass("uk-grid")},disconnected:function(){this.reset(),this.$el.css("margin-bottom","")},computed:{translate:function(){return Math.abs(this.$props.translate)},items:function(){return(this.target?this.$el.find(this.target):this.$el.children()).toArray()}},update:[{read:function(){this.columns=this.rows&&this.rows[0]&&this.rows[0].length||0,this.rows=this.rows&&this.rows.map(function(t){return e(t,"offsetLeft")})},write:function(){this.$el.css("margin-bottom","").css("margin-bottom",this.columns>1?this.translate+parseFloat(this.$el.css("margin-bottom")):"")},events:["load","resize"]},{read:function(){this.scrolled=i(this.$el)*this.translate},write:function(){var t=this;if(!this.rows||1===this.columns||!this.scrolled)return this.reset();this.rows.forEach(function(e){return e.forEach(function(e,i){return e.style.transform="translateY("+(i%2?t.scrolled:t.scrolled/8)+"px)"})})},events:["scroll","load","resize"]}],methods:{reset:function(){this.items.forEach(function(t){return t.style.transform=""})}}})),t.component("grid-parallax").options.update.unshift({read:function(){this.reset()},events:["load","resize"]})}}function Et(t){function e(t){return t.split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(function(t){return parseFloat(t)})}function i(t,e){return+(h(t.diff)?+t.end:t.start+t.diff*e*(t.start<t.end?1:-1)).toFixed(2)}if(!Et.installed){var n=t.mixin,s=t.util,o=s.assign,r=s.clamp,a=s.Dimensions,l=s.getImage,h=s.isUndefined,c=s.scrolledOver,u=s.query,d=["x","y","bgx","bgy","rotate","scale","color","backgroundColor","borderColor","opacity","blur","hue","grayscale","invert","saturate","sepia","fopacity"];n.parallax={props:d.reduce(function(t,e){return t[e]="list",t},{easing:Number,media:"media"}),defaults:d.reduce(function(t,e){return t[e]=void 0,t},{easing:1,media:!1}),computed:{props:function(){var t=this;return d.reduce(function(i,n){if(h(t.$props[n]))return i;var s=n.match(/color/i),r=s||"opacity"===n,a=t.$props[n];r&&t.$el.css(n,"");var l,c=(h(a[1])?"scale"===n?1:r?t.$el.css(n):0:a[0])||0,u=h(a[1])?a[0]:a[1],d=~a.join("").indexOf("%")?"%":"px";if(s){var f=t.$el[0].style.color;t.$el[0].style.color=c,c=e(t.$el.css("color")),t.$el[0].style.color=u,u=e(t.$el.css("color")),t.$el[0].style.color=f}else c=parseFloat(c),u=parseFloat(u),l=Math.abs(c-u);if(i[n]={start:c,end:u,diff:l,unit:d},n.match(/^bg/)){var p="background-position-"+n[2];i[n].pos=t.$el.css(p,"").css("background-position").split(" ")["x"===n[2]?0:1],t.covers&&o(i[n],{start:0,end:c<=u?l:-l})}return i},{})},bgProps:function(){var t=this;return["bgx","bgy"].filter(function(e){return e in t.props})},covers:function(){return"cover"===this.$el.css("backgroundSize","").css("backgroundSize")}},disconnected:function(){delete this._image},update:[{read:function(){var t=this;if(delete this._computeds.props,this._active=!this.media||window.matchMedia(this.media).matches,this._image&&(this._image.dimEl={width:this.$el[0].offsetWidth,height:this.$el[0].offsetHeight}),h(this._image)&&this.covers&&this.bgProps.length){var e=this.$el.css("backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");e&&(this._image=!1,l(e).then(function(e){t._image={width:e.naturalWidth,height:e.naturalHeight},t.$emit()}))}},write:function(){var t=this;if(this._image)if(this._active){var e=this._image,i=e.dimEl,n=a.cover(e,i);this.bgProps.forEach(function(s){var o=t.props[s],l=o.start,h=o.end,c=o.pos,u=o.diff,d="bgy"===s?"height":"width",f=n[d]-i[d];if(c.match(/%$/)){if(l>=h)f<u?(i[d]=n[d]+u-f,t.props[s].pos="0px"):(c=-1*f/100*parseFloat(c),c=r(c,u-f,0),t.props[s].pos=c+"px");else{if(f<u)i[d]=n[d]+u-f;else if(f/100*parseFloat(c)>u)return;t.props[s].pos="-"+u+"px"}n=a.cover(e,i)}}),this.$el.css({backgroundSize:n.width+"px "+n.height+"px",backgroundRepeat:"no-repeat"})}else this.$el.css({backgroundSize:"",backgroundRepeat:""})},events:["load","resize"]}],methods:{reset:function(){var t=this;Object.keys(this.getCss(0)).forEach(function(e){return t.$el.css(e,"")})},getCss:function(t){var e=!1,n=this.props;return Object.keys(n).reduce(function(s,o){var r=n[o],a=i(r,t);switch(o){case"x":case"y":if(e)break;var l=["x","y"].map(function(e){return o===e?a+r.unit:n[e]?i(n[e],t)+n[e].unit:0}),h=l[0],c=l[1];e=s.transform+=" translate3d("+h+", "+c+", 0)";break;case"rotate":s.transform+=" rotate("+a+"deg)";break;case"scale":s.transform+=" scale("+a+")";break;case"bgy":case"bgx":s["background-position-"+o[2]]="calc("+r.pos+" + "+(a+r.unit)+")";break;case"color":case"backgroundColor":case"borderColor":s[o]="rgba("+r.start.map(function(e,i){return e+=t*(r.end[i]-e),3===i?parseFloat(e):parseInt(e,10)}).join(",")+")";break;case"blur":s.filter+=" blur("+a+"px)";break;case"hue":s.filter+=" hue-rotate("+a+"deg)";break;case"fopacity":s.filter+=" opacity("+a+"%)";break;case"grayscale":case"invert":case"saturate":case"sepia":s.filter+=" "+o+"("+a+"%)";break;default:s[o]=a}return s},{transform:"",filter:""})}}},t.component("parallax",{mixins:[n.parallax],props:{target:String,viewport:Number},defaults:{target:!1,viewport:1},computed:{target:function(){return this.$props.target&&u(this.$props.target,this.$el)||this.$el}},disconnected:function(){delete this._prev},update:[{read:function(){delete this._prev}},{read:function(){var t=c(this.target)/(this.viewport||1);this._percent=r(t*(1-(this.easing-this.easing*t)))},write:function(){this._active?this._prev!==this._percent&&(this.$el.css(this.getCss(this._percent)),this._prev=this._percent):this.reset()},events:["scroll","load","resize"]}]})}}var St="default"in t?t.default:t,Ot=document.documentElement,Dt=St(window),It=St(document),Nt=St(Ot),Bt="rtl"===Ot.getAttribute("dir"),Pt="transitioncancel",Mt={start:l,stop:function(t){return r(t,ae),C.resolve()},cancel:function(t){return r(t,Pt),C.resolve()},inProgress:function(t){return St(t).hasClass("uk-transition")}},Ht="animationcancel",jt="uk-animation-",Ft="uk-cancel-animation",zt=new RegExp(jt+"(enter|leave)"),Lt={in:function(t,e,i,n){return h(t,e,i,n,!1)},out:function(t,e,i,n){return h(t,e,i,n,!0)},inProgress:function(t){return zt.test(St(t).attr("class"))},cancel:function(t){return r(t,Ht),C.resolve()}},Wt={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},qt={ratio:function(t,e,i){var n="width"===e?"height":"width";return s={},s[n]=Math.round(i*t[n]/t[e]),s[e]=i,s;var s},contain:function(e,i){var n=this;return e=Qt({},e),t.each(e,function(t){return e=e[t]>i[t]?n.ratio(e,t,i[t]):e}),e},cover:function(e,i){var n=this;return e=this.contain(e,i),t.each(e,function(t){return e=e[t]<i[t]?n.ratio(e,t,i[t]):e}),e}},Rt=Object.prototype.hasOwnProperty;C.resolve=function(t){return C(function(e){e(t)})},C.reject=function(t){return C(function(e,i){i(t)})},C.all=function(t){return te?Promise.all(t):St.when.apply(St,t)};var Yt=/-(\w)/g,Vt=Array.isArray,Ut={"!":"closest","+":"nextAll","-":"prevAll"},Xt={},Qt=Object.assign||function(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];t=Object(t);for(var n=0;n<e.length;n++){var s=e[n];if(null!==s)for(var o in s)k(s,o)&&(t[o]=s[o])}return t},Jt=window.MutationObserver||window.WebKitMutationObserver,Gt=window.requestAnimationFrame||function(t){return setTimeout(t,1e3/60)},Zt="ontouchstart"in window,Kt=window.PointerEvent,te="Promise"in window,ee="ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch||navigator.msPointerEnabled&&navigator.msMaxTouchPoints||navigator.pointerEnabled&&navigator.maxTouchPoints,ie=ee?"mousedown "+(Zt?"touchstart":"pointerdown"):"mousedown",ne=ee?"mousemove "+(Zt?"touchmove":"pointermove"):"mousemove",se=ee?"mouseup "+(Zt?"touchend":"pointerup"):"mouseup",oe=ee&&Kt?"pointerenter":"mouseenter",re=ee&&Kt?"pointerleave":"mouseleave",ae=G("transition","transition-end"),le=G("animation","animation-start"),he=G("animation","animation-end"),ce={reads:[],writes:[],measure:function(t){return this.reads.push(t),Z(),t},mutate:function(t){return this.writes.push(t),Z(),t},clear:function(t){return tt(this.reads,t)||tt(this.writes,t)},flush:function(){K(this.reads),K(this.writes.splice(0,this.writes.length)),this.scheduled=!1,(this.reads.length||this.writes.length)&&Z()}};et.prototype={positions:[],position:null,init:function(){var t=this;this.positions=[],this.position=null;var e=!1;this.handler=function(i){e||setTimeout(function(){var n=Date.now(),s=t.positions.length;s&&n-t.positions[s-1].time>100&&t.positions.splice(0,s),t.positions.push({time:n,x:i.pageX,y:i.pageY}),t.positions.length>5&&t.positions.shift(),e=!1},5),e=!0},It.on("mousemove",this.handler)},cancel:function(){this.handler&&It.off("mousemove",this.handler)},movesTo:function(t){if(this.positions.length<2)return!1;var e=at(t),i=this.positions[this.positions.length-1],n=this.positions[0];if(e.left<=i.x&&i.x<=e.right&&e.top<=i.y&&i.y<=e.bottom)return!1;var s=[[{x:e.left,y:e.top},{x:e.right,y:e.bottom}],[{x:e.right,y:e.top},{x:e.left,y:e.bottom}]];return e.right<=i.x||(e.left>=i.x?(s[0].reverse(),s[1].reverse()):e.bottom<=i.y?s[0].reverse():e.top>=i.y&&s[1].reverse()),!!s.reduce(function(t,e){return t+(it(n,e[0])<it(i,e[0])&&it(n,e[1])>it(i,e[1]))},0)}};var ue={};ue.args=ue.created=ue.events=ue.init=ue.ready=ue.connected=ue.disconnected=ue.destroy=function(t,e){return t=t&&!Vt(t)?[t]:t,e?t?t.concat(e):Vt(e)?e:[e]:t},ue.update=function(t,e){return ue.args(t,S(e)?{read:e}:e)},ue.props=function(t,e){return Vt(e)&&(e=e.reduce(function(t,e){return t[e]=String,t},{})),ue.methods(t,e)},ue.computed=ue.defaults=ue.methods=function(t,e){return e?t?Qt({},t,e):e:t};var de=function(t,e){return P(e)?t:e},fe=0,pe=function(t){this.id=++fe,this.el=F(t)};pe.prototype.isVideo=function(){return this.isYoutube()||this.isVimeo()||this.isHTML5()},pe.prototype.isHTML5=function(){return"VIDEO"===this.el.tagName},pe.prototype.isIFrame=function(){return"IFRAME"===this.el.tagName},pe.prototype.isYoutube=function(){return this.isIFrame()&&!!this.el.src.match(/\/\/.*?youtube\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)},pe.prototype.isVimeo=function(){return this.isIFrame()&&!!this.el.src.match(/vimeo\.com\/video\/.*/)},pe.prototype.enableApi=function(){var t=this;if(this.ready)return this.ready;var e,i=this.isYoutube(),n=this.isVimeo();return i||n?this.ready=C(function(s){o(t.el,"load",function(){if(i){var n=function(){return st(t.el,{event:"listening",id:t.id})};e=setInterval(n,100),n()}}),ot(function(e){return i&&e.id===t.id&&"onReady"===e.event||n&&Number(e.player_id)===t.id}).then(function(){s(),e&&clearInterval(e)}),t.el.setAttribute("src",t.el.src+(~t.el.src.indexOf("?")?"&":"?")+(i?"enablejsapi=1":"api=1&player_id="+fe))}):C.resolve()},pe.prototype.play=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return st(t.el,{func:"playVideo",method:"play"})}):this.isHTML5()&&this.el.play())},pe.prototype.pause=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return st(t.el,{func:"pauseVideo",method:"pause"})}):this.isHTML5()&&this.el.pause())},pe.prototype.mute=function(){var t=this;this.isVideo()&&(this.isIFrame()?this.enableApi().then(function(){return st(t.el,{func:"mute",method:"setVolume",value:0})}):this.isHTML5()&&(this.el.muted=!0,this.el.setAttribute("muted","")))};var ge,me,ve,we,be={x:["width","left","right"],y:["height","top","bottom"]},ye=document.documentElement,$e={};i(function(){n(document,"click",function(){return we=!0},!0),n(document,ie,function(t){var e=t.touches?t.touches[0]:t,i=e.target,n=e.pageX,s=e.pageY,o=Date.now();$e.el="tagName"in i?i:i.parentNode,ge&&clearTimeout(ge),$e.x1=n,$e.y1=s,$e.last&&o-$e.last<=250&&($e={}),$e.last=o,we=t.button>0}),n(document,ne,function(t){var e=t.touches?t.touches[0]:t,i=e.pageX,n=e.pageY;$e.x2=i,$e.y2=n}),n(document,se,function(t){var e=t.target;$e.x2&&Math.abs($e.x1-$e.x2)>30||$e.y2&&Math.abs($e.y1-$e.y2)>30?me=setTimeout(function(){$e.el&&(r($e.el,"swipe"),r($e.el,"swipe"+gt($e))),$e={}}):"last"in $e?(ve=setTimeout(function(){return $e.el&&r($e.el,"tap")}),$e.el&&u(e,$e.el)&&(ge=setTimeout(function(){ge=null,$e.el&&!we&&r($e.el,"click"),$e={}},350))):$e={}}),n(document,"touchcancel",mt),n(window,"scroll",mt)});var xe=!1;n(document,"touchstart",function(){return xe=!0},!0),n(document,"click",function(){xe=!1}),n(document,"touchcancel",function(){return xe=!1},!0);var ke,Ce,Te=Object.freeze({win:Dt,doc:It,docElement:Nt,isRtl:Bt,isReady:e,ready:i,on:n,off:s,one:o,trigger:r,$trigger:a,transition:l,Transition:Mt,animate:h,Animation:Lt,isJQuery:c,isWithin:u,attrFilter:d,removeClass:f,createEvent:p,isInView:g,scrolledOver:function(t){var e=(t=F(t)).offsetHeight,i=m(t),n=window.innerHeight,s=n+Math.min(0,i-n),o=Math.max(0,n-(v()-(i+e)));return U((s+window.pageYOffset-i)/((s+(e-(o<n?o:0)))/100)/100)},docHeight:v,getIndex:w,isVoidElement:b,Dimensions:qt,query:y,preventClick:function(){var t=setTimeout(function(){return r(It,"click")},0);o(It,"click",function(e){e.preventDefault(),e.stopImmediatePropagation(),clearTimeout(t)},!0)},getData:$,Observer:Jt,requestAnimationFrame:Gt,hasPromise:te,hasTouch:ee,pointerDown:ie,pointerMove:ne,pointerUp:se,pointerEnter:oe,pointerLeave:re,transitionend:ae,animationstart:le,animationend:he,getStyle:Q,getCssVar:J,getImage:function(t){return C(function(e,i){var n=new Image;n.onerror=i,n.onload=function(){return e(n)},n.src=t})},fastdom:ce,$:St,bind:x,hasOwn:k,promise:C,classify:T,hyphenate:_,camelize:A,isArray:Vt,isFunction:S,isObject:O,isPlainObject:D,isBoolean:I,isString:N,isNumber:B,isUndefined:P,isContextSelector:M,getContextSelectors:H,toJQuery:j,toNode:F,toBoolean:z,toNumber:L,toList:W,toMedia:q,coerce:R,toMs:Y,swap:V,assign:Qt,clamp:U,noop:X,ajax:t.ajax,each:t.each,Event:t.Event,isNumeric:t.isNumeric,MouseTracker:et,mergeOptions:nt,Player:pe,position:rt,getDimensions:at,offset:lt,offsetTop:ht,flipPosition:pt,isTouch:vt});!function(){var t=document.createElement("_").classList;t&&(t.add("a","b"),t.toggle("c",!1),ke=t.contains("b"),Ce=!t.contains("c")),t=null}();var _e=function(t){this._init(t)};_e.util=Te,_e.data="__uikit__",_e.prefix="uk-",_e.options={},_e.instances={},_e.elements=[],function(t){function e(t){return new Function("return function "+T(t)+" (options) { this._init(options); }")()}function i(t,e){if(t.nodeType===Node.ELEMENT_NODE)for(e(t),t=t.firstChild;t;)i(t,e),t=t.nextSibling}function n(t,e){if(t)for(var i in t)t[i]._isReady&&t[i]._callUpdate(e)}var s=t.data;t.use=function(t){if(!t.installed)return t.call(null,this),t.installed=!0,this},t.mixin=function(e,i){i=(N(i)?t.components[i]:i)||this,(e=nt({},e)).mixins=i.options.mixins,delete i.options.mixins,i.options=nt(e,i.options)},t.extend=function(t){var i=this,n=e((t=t||{}).name||i.options.name||"UIkitComponent");return n.prototype=Object.create(i.prototype),n.prototype.constructor=n,n.options=nt(i.options,t),n.super=i,n.extend=i.extend,n},t.update=function(e,o,r){if(void 0===r&&(r=!1),e=p(e||"update"),o)if(o=F(o),r)do{n(o[s],e),o=o.parentNode}while(o);else i(o,function(t){return n(t[s],e)});else n(t.instances,e)};var o;Object.defineProperty(t,"container",{get:function(){return o||document.body},set:function(t){o=t}})}(_e),function(t){t.prototype._callHook=function(t){var e=this,i=this.$options[t];i&&i.forEach(function(t){return t.call(e)})},t.prototype._callReady=function(){this._isReady||(this._isReady=!0,this._callHook("ready"),this._callUpdate())},t.prototype._callConnected=function(){var e=this;this._connected||(~t.elements.indexOf(this.$options.el)||t.elements.push(this.$options.el),t.instances[this._uid]=this,this._initEvents(),this._callHook("connected"),this._connected=!0,this._initObserver(),this._isReady||i(function(){return e._callReady()}),this._callUpdate())},t.prototype._callDisconnected=function(){if(this._connected){this._observer&&(this._observer.disconnect(),this._observer=null);var e=t.elements.indexOf(this.$options.el);~e&&t.elements.splice(e,1),delete t.instances[this._uid],this._initEvents(!0),this._callHook("disconnected"),this._connected=!1}},t.prototype._callUpdate=function(t){var e=this;"update"===(t=p(t||"update")).type&&(this._computeds={});var i=this.$options.update;i&&i.forEach(function(i,n){("update"===t.type||i.events&&~i.events.indexOf(t.type))&&(i.read&&!~ce.reads.indexOf(e._frames.reads[n])&&(e._frames.reads[n]=ce.measure(function(){i.read.call(e,t),delete e._frames.reads[n]})),i.write&&!~ce.writes.indexOf(e._frames.writes[n])&&(e._frames.writes[n]=ce.mutate(function(){i.write.call(e,t),delete e._frames.writes[n]})))})}}(_e),function(t){function e(t,e){var n={},s=t.args;void 0===s&&(s=[]);var o=t.props;void 0===o&&(o={});var r,a,l=t.el;if(!o)return n;for(r in o)if(a=_(r),l.hasAttribute(a)){var h=R(o[r],l.getAttribute(a),l);if("target"===a&&(!h||0===h.lastIndexOf("_",0)))continue;n[r]=h}var c=i($(l,e),s);for(r in c)void 0!==o[a=A(r)]&&(n[a]=R(o[a],c[r],l));return n}function i(t,e){void 0===e&&(e=[]);try{return t?"{"===t[0]?JSON.parse(t):e.length&&!~t.indexOf(":")?(i={},i[e[0]]=t,i):t.split(";").reduce(function(t,e){var i=e.split(/:(.+)/),n=i[0],s=i[1];return n&&s&&(t[n.trim()]=s.trim()),t},{}):{};var i}catch(t){return{}}}function n(t,e,i){Object.defineProperty(t,e,{enumerable:!0,get:function(){return k(t._computeds,e)||(t._computeds[e]=i.call(t)),t._computeds[e]},set:function(i){t._computeds[e]=i}})}function s(t,e,i,n){D(i)||(i={name:n,handler:i});var s=i.name,r=i.el,a=i.delegate,l=i.self,h=i.filter,c=i.handler,u="."+t.$options.name+"."+t._uid;if(r=r&&r.call(t)||t.$el,s=s.split(" ").map(function(t){return t+"."+u}).join(" "),e)r.off(s);else{if(h&&!h.call(t))return;c=N(c)?t[c]:x(c,t),l&&(c=o(c,t)),a?r.on(s,N(a)?a:a.call(t),c):r.on(s,c)}}function o(t,e){return function(i){if(i.target===i.currentTarget)return t.call(e,i)}}function r(t,e){return t.every(function(t){return!t||!k(t,e)})}function a(t,e){return P(t)||t===e||c(t)&&c(e)&&t.is(e)}var l=0;t.prototype.props={},t.prototype._init=function(e){e=e||{},e=this.$options=nt(this.constructor.options,e,this),this.$el=null,this.$name=t.prefix+_(this.$options.name),this.$props={},this._frames={reads:{},writes:{}},this._uid=l++,this._initData(),this._initMethods(),this._initComputeds(),this._callHook("created"),e.el&&this.$mount(e.el)},t.prototype._initData=function(){var t=this,e=this.$options,i=e.defaults,n=e.data;void 0===n&&(n={});var s=e.args;void 0===s&&(s=[]);var o=e.props;void 0===o&&(o={});var r=e.el;s.length&&Vt(n)&&(n=n.slice(0,s.length).reduce(function(t,e,i){return D(e)?Qt(t,e):t[s[i]]=e,t},{}));for(var a in i)t.$props[a]=t[a]=k(n,a)&&!P(n[a])?R(o[a],n[a],r):Vt(i[a])?i[a].concat():i[a]},t.prototype._initMethods=function(){var t=this,e=this.$options.methods;if(e)for(var i in e)t[i]=x(e[i],t)},t.prototype._initComputeds=function(){var t=this,e=this.$options.computed;if(this._computeds={},e)for(var i in e)n(t,i,e[i])},t.prototype._initProps=function(t){var i=this;this._computeds={},Qt(this.$props,t||e(this.$options,this.$name));var n=[this.$options.computed,this.$options.methods];for(var s in i.$props)r(n,s)&&(i[s]=i.$props[s])},t.prototype._initEvents=function(t){var e=this,i=this.$options.events;i&&i.forEach(function(i){if(k(i,"handler"))s(e,t,i);else for(var n in i)s(e,t,i[n],n)})},t.prototype._initObserver=function(){var t=this,i=this.$options,n=i.attrs,s=i.props,o=i.el;!this._observer&&s&&n&&Jt&&(n=Vt(n)?n:Object.keys(s).map(function(t){return _(t)}),this._observer=new Jt(function(){var i=e(t.$options,t.$name);n.some(function(e){return!a(i[e],t.$props[e])})&&t.$reset(i)}),this._observer.observe(o,{attributes:!0,attributeFilter:n.concat([this.$name,"data-"+this.$name])}))}}(_e),function(t){var e=t.data;t.prototype.$mount=function(t){var i=this.$options.name;t[e]||(t[e]={}),t[e][i]||(t[e][i]=this,this.$options.el=this.$options.el||t,this.$el=St(t),this._initProps(),this._callHook("init"),document.documentElement.contains(t)&&this._callConnected())},t.prototype.$emit=function(t){this._callUpdate(t)},t.prototype.$update=function(e,i){t.update(e,this.$options.el,i)},t.prototype.$reset=function(t){this._callDisconnected(),this._initProps(t),this._callConnected()},t.prototype.$destroy=function(t){void 0===t&&(t=!1);var i=this.$options,n=i.el,s=i.name;n&&this._callDisconnected(),this._callHook("destroy"),n&&n[e]&&(delete n[e][s],Object.keys(n[e]).length||delete n[e],t&&this.$el.remove())}}(_e),function(t){var e=t.data;t.components={},t.component=function(e,i){var n=A(e);if(D(i))i.name=n,i=t.extend(i);else{if(P(i))return t.components[n];i.options.name=n}return t.components[n]=i,t[n]=function(e,i){function s(e){return t.getComponent(e,n)||new t.components[n]({el:e,data:i||{}})}for(var o=arguments.length,r=Array(o);o--;)r[o]=arguments[o];return D(e)?new t.components[n]({data:e}):t.components[n].options.functional?new t.components[n]({data:[].concat(r)}):e&&e.nodeType?s(e):St(e).toArray().map(s)[0]},t._initialized&&!i.options.functional&&ce.measure(function(){return t[n]("[uk-"+e+"],[data-uk-"+e+"]")}),t.components[n]},t.getComponents=function(t){return t&&(t=c(t)?t[0]:t)&&t[e]||{}},t.getComponent=function(e,i){return t.getComponents(e)[i]},t.connect=function(i){var n;if(i[e])for(n in i[e])i[e][n]._callConnected();for(var s=0;s<i.attributes.length;s++)0!==(n=i.attributes[s].name).lastIndexOf("uk-",0)&&0!==n.lastIndexOf("data-uk-",0)||(n=A(n.replace("data-uk-","").replace("uk-","")),t[n]&&t[n](i))},t.disconnect=function(t){for(var i in t[e])t[e][i]._callDisconnected()}}(_e),function(t){function e(t,e,n){(e=i(e,t))&&(ke?e[0][n].apply(e[0],e.slice(1)):e.slice(1).forEach(function(t){return e[0][n](t)}))}function i(t,e){return N(t[0])&&t.unshift(e),t[0]=(F(t[0])||{}).classList,t.forEach(function(e,i){return i>0&&N(e)&&~e.indexOf(" ")&&Array.prototype.splice.apply(t,[i,1].concat(t[i].split(" ")))}),t[0]&&t[1]&&t.length>1&&t}t.prototype.$addClass=function(){for(var t=[],i=arguments.length;i--;)t[i]=arguments[i];e(this.$options.el,t,"add")},t.prototype.$removeClass=function(){for(var t=[],i=arguments.length;i--;)t[i]=arguments[i];e(this.$options.el,t,"remove")},t.prototype.$hasClass=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return(t=i(t,this.$options.el))&&t[0].contains(t[1])},t.prototype.$toggleClass=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];for(var n=(t=i(t,this.$options.el))&&!N(t[t.length-1])?t.pop():void 0,s=1;s<(t&&t.length);s++)t[0]&&Ce?t[0].toggle(t[s],n):t[0][(P(n)?!t[0].contains(t[s]):n)?"add":"remove"](t[s])}}(_e);var Ae,Ee,Se={init:function(){this.$addClass(this.$name)}},Oe={props:{cls:Boolean,animation:"list",duration:Number,origin:String,transition:String,queued:Boolean},defaults:{cls:!1,animation:[!1],duration:200,origin:!1,transition:"linear",queued:!1,initProps:{overflow:"",height:"",paddingTop:"",paddingBottom:"",marginTop:"",marginBottom:""},hideProps:{overflow:"hidden",height:0,paddingTop:0,paddingBottom:0,marginTop:0,marginBottom:0}},computed:{hasAnimation:function(){return!!this.animation[0]},hasTransition:function(){return this.hasAnimation&&!0===this.animation[0]}},methods:{toggleElement:function(t,e,i){var n=this;return C(function(s){var o,r=function(t){return C.all(t.map(function(t){return n._toggleElement(t,e,i)}))},a=(t=St(t).toArray()).filter(function(t){return n.isToggled(t)}),l=t.filter(function(t){return!~a.indexOf(t)});if(n.queued&&P(i)&&P(e)&&n.hasAnimation&&!(t.length<2)){var h=document.body,c=h.scrollTop,u=a[0],d=Lt.inProgress(u)&&n.$hasClass(u,"uk-animation-leave")||Mt.inProgress(u)&&"0px"===u.style.height;o=r(a),d||(o=o.then(function(){var t=r(l);return h.scrollTop=c,t}))}else o=r(l.concat(a));o.then(s,X)})},toggleNow:function(t,e){var i=this;return C(function(n){return C.all(St(t).toArray().map(function(t){return i._toggleElement(t,e,!1)})).then(n,X)})},isToggled:function(t){return t=t&&St(t)||this.$el,this.cls?t.hasClass(this.cls.split(" ")[0]):!t.attr("hidden")},updateAria:function(t){!1===this.cls&&t.attr("aria-hidden",!this.isToggled(t))},_toggleElement:function(e,i,n){var s=this;if(e=St(e),i=I(i)?i:Lt.inProgress(e)?this.$hasClass(e,"uk-animation-leave"):Mt.inProgress(e)?"0px"===e[0].style.height:!this.isToggled(e),!1===a(e,"before"+(i?"show":"hide"),[this]).result)return C.reject();var o=(!1!==n&&this.hasAnimation?this.hasTransition?this._toggleHeight:this._toggleAnimation:this._toggleImmediate)(e,i),r=t.Event(i?"show":"hide");return r.preventDefault(),a(e,r,[this]),o.then(function(){a(e,i?"shown":"hidden",[s]),_e.update(null,e)})},_toggle:function(t,e){t=St(t),this.cls?t.toggleClass(this.cls,~this.cls.indexOf(" ")?void 0:e):t.attr("hidden",!e),t.find("[autofocus]:visible").focus(),this.updateAria(t),_e.update(null,t)},_toggleImmediate:function(t,e){return this._toggle(t,e),C.resolve()},_toggleHeight:function(t,e){var i,n=this,s=t.children(),o=Mt.inProgress(t),r=s.length?parseFloat(s.first().css("margin-top"))+parseFloat(s.last().css("margin-bottom")):0,a=t[0].offsetHeight?t.height()+(o?0:r):0;return Mt.cancel(t),this.isToggled(t)||this._toggle(t,!0),t.height(""),ce.flush(),i=t.height()+(o?0:r),t.height(a),(e?Mt.start(t,Qt({},this.initProps,{overflow:"hidden",height:i}),Math.round(this.duration*(1-a/i)),this.transition):Mt.start(t,this.hideProps,Math.round(this.duration*(a/i)),this.transition).then(function(){return n._toggle(t,!1)})).then(function(){return t.css(n.initProps)})},_toggleAnimation:function(t,e){var i=this;return Lt.inProgress(t)?Lt.cancel(t).then(function(){return Lt.inProgress(t)?C.resolve().then(function(){return i._toggleAnimation(t,e)}):i._toggleAnimation(t,e)}):e?(this._toggle(t,!0),Lt.in(t,this.animation[0],this.duration,this.origin)):Lt.out(t,this.animation[1]||this.animation[0],this.duration,this.origin).then(function(){return i._toggle(t,!1)})}}},De={mixins:[Se,Oe],props:{clsPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean,container:Boolean},defaults:{cls:"uk-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1,container:!0},computed:{body:function(){return St(document.body)},panel:function(){return this.$el.find("."+this.clsPanel)},container:function(){return F(!0===this.$props.container&&_e.container||this.$props.container&&j(this.$props.container))},transitionElement:function(){return this.panel},transitionDuration:function(){return Y(this.transitionElement.css("transition-duration"))},component:function(){return _e[this.$options.name]}},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.hide()}},{name:"toggle",handler:function(t){t.preventDefault(),this.toggle()}},{name:"show",self:!0,handler:function(){Nt.hasClass(this.clsPage)||(this.scrollbarWidth=window.innerWidth-Nt[0].offsetWidth,this.body.css("overflow-y",this.scrollbarWidth&&this.overlay?"scroll":"")),Nt.addClass(this.clsPage)}},{name:"hidden",self:!0,handler:function(){this.component.active===this&&(Nt.removeClass(this.clsPage),this.body.css("overflow-y",""),this.component.active=null)}}],methods:{toggle:function(){return this.isToggled()?this.hide():this.show()},show:function(){var t=this;if(!this.isToggled()){if(this.container&&!this.$el.parent().is(this.container))return this.container.appendChild(this.$el[0]),C(function(e){return Gt(function(){return e(t.show())})});var e=Ae&&Ae!==this&&Ae;if(Ae=this,this.component.active=this,e){if(!this.stack)return void e.hide().then(this.show);this.prev=e}else Gt(function(){return wt(t.$options.name)});return this.toggleNow(this.$el,!0)}},hide:function(){if(this.isToggled())return(Ae=Ae&&Ae!==this&&Ae||this.prev)||bt(this.$options.name),this.toggleNow(this.$el,!1)},getActive:function(){return Ae},_toggleImmediate:function(t,e){var i=this;return Gt(function(){return i._toggle(t,e)}),this.transitionDuration?C(function(t,e){i._transition&&(i.transitionElement.off(ae,i._transition.handler),i._transition.reject()),i._transition={reject:e,handler:function(){t(),i._transition=null}},i.transitionElement.one(ae,i._transition.handler)}):C.resolve()}}},Ie={props:{pos:String,offset:null,flip:Boolean,clsPos:String},defaults:{pos:Bt?"bottom-right":"bottom-left",flip:!0,offset:!1,clsPos:""},computed:{pos:function(){return(this.$props.pos+(~this.$props.pos.indexOf("-")?"":"-center")).split("-")},dir:function(){return this.pos[0]},align:function(){return this.pos[1]}},methods:{positionAt:function(t,e,i){f(t,this.clsPos+"-(top|bottom|left|right)(-[a-z]+)?").css({top:"",left:""});var n=L(this.offset)||0,s=this.getAxis(),o=rt(t,e,"x"===s?pt(this.dir)+" "+this.align:this.align+" "+pt(this.dir),"x"===s?this.dir+" "+this.align:this.align+" "+this.dir,"x"===s?""+("left"===this.dir?-1*n:n):" "+("top"===this.dir?-1*n:n),null,this.flip,i);this.dir="x"===s?o.target.x:o.target.y,this.align="x"===s?o.target.y:o.target.x,t.toggleClass(this.clsPos+"-"+this.dir+"-"+this.align,!1===this.offset)},getAxis:function(){return"top"===this.dir||"bottom"===this.dir?"y":"x"}}},Ne=function(t){t.component("accordion",{mixins:[Se,Oe],props:{targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,transition:String},defaults:{targets:"> *",active:!1,animation:[!0],collapsible:!0,multiple:!1,clsOpen:"uk-open",toggle:"> .uk-accordion-title",content:"> .uk-accordion-content",transition:"ease"},computed:{items:function(){var t=this,e=St(this.targets,this.$el);return this._changed=!this._items||e.length!==this._items.length||e.toArray().some(function(e,i){return e!==t._items.get(i)}),this._items=e}},events:[{name:"click",delegate:function(){return this.targets+" "+this.$props.toggle},handler:function(t){t.preventDefault(),this.toggle(this.items.find(this.$props.toggle).index(t.currentTarget))}}],update:function(){var t=this;if(this.items.length&&this._changed){this.items.each(function(e,i){i=St(i),t.toggleNow(i.find(t.content),i.hasClass(t.clsOpen))});var e=!1!==this.active&&j(this.items.eq(Number(this.active)))||!this.collapsible&&j(this.items.eq(0));e&&!e.hasClass(this.clsOpen)&&this.toggle(e,!1)}},methods:{toggle:function(t,e){var i=this,n=w(t,this.items),s=this.items.filter("."+this.clsOpen);(t=this.items.eq(n)).add(!this.multiple&&s).each(function(n,o){var r=(o=St(o)).is(t),a=r&&!o.hasClass(i.clsOpen);if(a||!r||i.collapsible||!(s.length<2)){o.toggleClass(i.clsOpen,a);var l=o[0]._wrapper?o[0]._wrapper.children().first():o.find(i.content);o[0]._wrapper||(o[0]._wrapper=l.wrap("<div>").parent().attr("hidden",a)),i._toggleImmediate(l,!0),i.toggleElement(o[0]._wrapper,a,e).then(function(){o.hasClass(i.clsOpen)===a&&(a||i._toggleImmediate(l,!1),o[0]._wrapper=null,l.unwrap())})}})}}})},Be=function(t){t.component("alert",{attrs:!0,mixins:[Se,Oe],args:"animation",props:{close:String},defaults:{animation:[!0],selClose:".uk-alert-close",duration:150,hideProps:Qt({opacity:0},Oe.defaults.hideProps)},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.close()}}],methods:{close:function(){var t=this;this.toggleElement(this.$el).then(function(){return t.$destroy(!0)})}}})},Pe=function(t){t.component("cover",{mixins:[Se],props:{width:Number,height:Number},computed:{el:function(){return this.$el[0]},parent:function(){return this.el.parentNode}},ready:function(){this.$el.is("iframe")&&this.$el.css("pointerEvents","none");var t=new pe(this.$el);t.isVideo()&&t.mute()},update:{write:function(){0!==this.el.offsetHeight&&this.$el.css({width:"",height:""}).css(qt.cover({width:this.width||this.el.clientWidth,height:this.height||this.el.clientHeight},{width:this.parent.offsetWidth,height:this.parent.offsetHeight}))},events:["load","resize"]},events:{loadedmetadata:function(){this.$emit()}}})},Me=function(t){function e(){n||(n=!0,It.on("click",function(t){var e;if(!t.isDefaultPrevented())for(;i&&i!==e&&!u(t.target,i.$el)&&(!i.toggle||!u(t.target,i.toggle.$el));)e=i,i.hide(!1)}))}var i;t.component("drop",{mixins:[Ie,Oe],args:"pos",props:{mode:"list",toggle:Boolean,boundary:"jQuery",boundaryAlign:Boolean,delayShow:Number,delayHide:Number,clsDrop:String},defaults:{mode:["click","hover"],toggle:"- :first",boundary:window,boundaryAlign:!1,delayShow:0,delayHide:800,clsDrop:!1,hoverIdle:200,animation:["uk-animation-fade"],cls:"uk-open"},init:function(){this.tracker=new et,this.clsDrop=this.clsDrop||"uk-"+this.$options.name,this.clsPos=this.clsDrop,this.$addClass(this.clsDrop)},ready:function(){this.updateAria(this.$el),this.toggle&&(this.toggle=t.toggle(y(this.toggle,this.$el),{target:this.$el,mode:this.mode}))},events:[{name:"click",delegate:function(){return"."+this.clsDrop+"-close"},handler:function(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){if(!t.isDefaultPrevented()){var e=t.target.hash;e||t.preventDefault(),e&&u(e,this.$el)||this.hide(!1)}}},{name:"beforescroll",handler:function(){this.hide(!1)}},{name:"toggle",handler:function(t,e){e&&!this.$el.is(e.target)||(t.preventDefault(),this.isToggled()?this.hide(!1):this.show(e,!1))}},{name:oe,filter:function(){return~this.mode.indexOf("hover")},handler:function(t){vt(t)||(i&&i!==this&&i.toggle&&~i.toggle.mode.indexOf("hover")&&!u(t.target,i.$el)&&!u(t.target,i.toggle.$el)&&i.hide(!1),t.preventDefault(),this.show(this.toggle))}},{name:"toggleshow",handler:function(t,e){e&&!this.$el.is(e.target)||(t.preventDefault(),this.show(e||this.toggle))}},{name:"togglehide "+re,handler:function(t,e){vt(t)||e&&!this.$el.is(e.target)||(t.preventDefault(),this.toggle&&~this.toggle.mode.indexOf("hover")&&this.hide())}},{name:"beforeshow",self:!0,handler:function(){this.clearTimers()}},{name:"show",self:!0,handler:function(){this.tracker.init(),this.toggle.$el.addClass(this.cls).attr("aria-expanded","true"),e()}},{name:"beforehide",self:!0,handler:function(){this.clearTimers()}},{name:"hide",handler:function(t){var e=t.target;this.$el.is(e)?(i=this.isActive()?null:i,this.toggle.$el.removeClass(this.cls).attr("aria-expanded","false").blur().find("a, button").blur(),this.tracker.cancel()):i=null===i&&u(e,this.$el)&&this.isToggled()?this:i}}],update:{write:function(){this.isToggled()&&!Lt.inProgress(this.$el)&&this.position()},events:["resize"]},methods:{show:function(t,e){var n=this;void 0===e&&(e=!0);var s=function(){n.isToggled()||(n.position(),n.toggleElement(n.$el,!0))},o=function(){if(n.toggle=t||n.toggle,n.clearTimers(),!n.isActive())if(e&&i&&i!==n&&i.isDelaying)n.showTimer=setTimeout(n.show,10);else{if(n.isParentOf(i)){if(!i.hideTimer)return;i.hide(!1)}else if(i&&!n.isChildOf(i)&&!n.isParentOf(i))for(var o;i&&i!==o&&!n.isChildOf(i);)o=i,i.hide(!1);e&&n.delayShow?n.showTimer=setTimeout(s,n.delayShow):s(),i=n}};t&&this.toggle&&!this.toggle.$el.is(t.$el)?(this.$el.one("hide",o),this.hide(!1)):o()},hide:function(t){var e=this;void 0===t&&(t=!0);var i=function(){return e.toggleNow(e.$el,!1)};this.clearTimers(),this.isDelaying=this.tracker.movesTo(this.$el),t&&this.isDelaying?this.hideTimer=setTimeout(this.hide,this.hoverIdle):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers:function(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null,this.isDelaying=!1},isActive:function(){return i===this},isChildOf:function(t){return t&&t!==this&&u(this.$el,t.$el)},isParentOf:function(t){return t&&t!==this&&u(t.$el,this.$el)},position:function(){f(this.$el,this.clsDrop+"-(stack|boundary)").css({top:"",left:""}),this.$el.show().toggleClass(this.clsDrop+"-boundary",this.boundaryAlign);var t=at(this.boundary),e=this.boundaryAlign?t:at(this.toggle.$el);if("justify"===this.align){var i="y"===this.getAxis()?"width":"height";this.$el.css(i,e[i])}else this.$el.outerWidth()>Math.max(t.right-e.left,e.right-t.left)&&(this.$addClass(this.clsDrop+"-stack"),this.$el.trigger("stack",[this]));this.positionAt(this.$el,this.boundaryAlign?this.boundary:this.toggle.$el,this.boundary),this.$el[0].style.display=""}}}),t.drop.getActive=function(){return i};var n},He=function(t){t.component("dropdown",t.components.drop.extend({name:"dropdown"}))},je=function(t){t.component("form-custom",{mixins:[Se],args:"target",props:{target:Boolean},defaults:{target:!1},computed:{input:function(){return this.$el.find(":input:first")},state:function(){return this.input.next()},target:function(){return this.$props.target&&y(!0===this.$props.target?"> :input:first + :first":this.$props.target,this.$el)}},connected:function(){this.input.trigger("change")},events:[{name:"focusin focusout mouseenter mouseleave",delegate:":input:first",handler:function(t){var e=t.type;this.state.toggleClass("uk-"+(~e.indexOf("focus")?"focus":"hover"),~["focusin","mouseenter"].indexOf(e))}},{name:"change",handler:function(){this.target&&this.target[this.target.is(":input")?"val":"text"](this.input[0].files&&this.input[0].files[0]?this.input[0].files[0].name:this.input.is("select")?this.input.find("option:selected").text():this.input.val())}}]})},Fe=function(t){t.component("gif",{update:{read:function(){var t=g(this.$el);!this.isInView&&t&&(this.$el[0].src=this.$el[0].src),this.isInView=t},events:["scroll","load","resize"]}})},ze=function(t){t.component("grid",t.components.margin.extend({mixins:[Se],name:"grid",defaults:{margin:"uk-grid-margin",clsStack:"uk-grid-stack"},update:{write:function(){this.$toggleClass(this.clsStack,this.stacks)},events:["load","resize"]}}))},Le=function(t){t.component("height-match",{args:"target",props:{target:String,row:Boolean},defaults:{target:"> *",row:!0},computed:{elements:function(){return St(this.target,this.$el)}},update:{read:function(){var t=this,e=!1;this.elements.css("minHeight",""),this.rows=this.row?this.elements.toArray().reduce(function(t,i){return e!==i.offsetTop?t.push([i]):t[t.length-1].push(i),e=i.offsetTop,t},[]).map(function(e){return t.match(St(e))}):[this.match(this.elements)]},write:function(){this.rows.forEach(function(t){var e=t.height,i=t.elements;return i&&i.each(function(t,i){return i.style.minHeight=e+"px"})})},events:["load","resize"]},methods:{match:function(t){if(t.length<2)return{};var e=0,i=[];return t=t.each(function(t,n){var s,o,r;0===n.offsetHeight&&(o=(s=St(n)).attr("style")||null,r=s.attr("hidden")||null,s.attr({style:o+";display:block !important;",hidden:null})),e=Math.max(e,n.offsetHeight),i.push(n.offsetHeight),s&&s.attr({style:o,hidden:r})}).filter(function(t){return i[t]<e}),{height:e,elements:t}}}})},We=function(e){e.component("height-viewport",{props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean},defaults:{expand:!1,offsetTop:!1,offsetBottom:!1},update:{write:function(){this.$el.css("boxSizing","border-box");var e,i=window.innerHeight,n=0;if(this.expand){this.$el.css({height:"",minHeight:""});var s=i-document.documentElement.offsetHeight;s>0&&this.$el.css("min-height",e=this.$el.outerHeight()+s)}else{var o=ht(this.$el);if(o<i/2&&this.offsetTop&&(n+=o),!0===this.offsetBottom)n+=this.$el.next().outerHeight()||0;else if(t.isNumeric(this.offsetBottom))n+=i/100*this.offsetBottom;else if(this.offsetBottom&&"px"===this.offsetBottom.substr(-2))n+=parseFloat(this.offsetBottom);else if(N(this.offsetBottom)){var r=y(this.offsetBottom,this.$el);n+=r&&r.outerHeight()||0}this.$el.css("min-height",e=n?"calc(100vh - "+n+"px)":"100vh")}this.$el.height(""),e&&i-n>=this.$el.outerHeight()&&this.$el.css("height",e)},events:["load","resize"]}})},qe=function(t){i(function(){if(ee){var e="uk-hover";Nt.on("tap",function(t){var i=t.target;return St("."+e).filter(function(t,e){return!u(i,e)}).removeClass(e)}),Object.defineProperty(t,"hoverSelector",{set:function(t){Nt.on("tap",t,function(t){return t.currentTarget.classList.add(e)})}}),t.hoverSelector=".uk-animation-toggle, .uk-transition-toggle, [uk-hover]"}})},Re=function(e){function i(t,i){e.component(t,e.components.icon.extend({name:t,mixins:i?[i]:[],defaults:{icon:t}}))}var n={},s={spinner:'<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"></circle></svg>',totop:'<svg width="18" height="10" viewBox="0 0 18 10" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9 "></polyline></svg>',marker:'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect x="9" y="4" width="1" height="11"></rect><rect x="4" y="9" width="11" height="1"></rect></svg>',"close-icon":'<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"></line><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"></line></svg>',"close-large":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"></line><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"></line></svg>',"navbar-toggle-icon":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect y="9" width="20" height="2"></rect><rect y="3" width="20" height="2"></rect><rect y="15" width="20" height="2"></rect></svg>',"overlay-icon":'<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><rect x="19" y="0" width="1" height="40"></rect><rect x="0" y="19" width="40" height="1"></rect></svg>',"pagination-next":'<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"></polyline></svg>',"pagination-previous":'<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"></polyline></svg>',"search-icon":'<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"></circle><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"></path></svg>',"search-large":'<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"></circle><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"></line></svg>',"search-navbar":'<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',"slidenav-next":'<svg width="11" height="20" viewBox="0 0 11 20" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 10 10 1 19"></polyline></svg>',"slidenav-next-large":'<svg width="18" height="34" viewBox="0 0 18 34" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1 1 17 17 1 33"></polyline></svg>',"slidenav-previous":'<svg width="11" height="20" viewBox="0 0 11 20" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="10 1 1 10 10 19"></polyline></svg>',"slidenav-previous-large":'<svg width="18" height="34" viewBox="0 0 18 34" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="17 1 1 17 17 33"></polyline></svg>'};e.component("icon",e.components.svg.extend({attrs:["icon","ratio"],mixins:[Se],name:"icon",args:"icon",props:["icon"],defaults:{exclude:["id","style","class","src","icon"]},init:function(){this.$addClass("uk-icon"),Bt&&(this.icon=V(V(this.icon,"left","right"),"previous","next"))},disconnected:function(){delete this.delay},update:{read:function(){if(this.delay){var t=this.getIcon();t&&(this.delay(t),delete this.delay)}},events:["load"]},methods:{getSvg:function(){var t=this,e=this.getIcon();return e?C.resolve(e):"complete"!==document.readyState?C(function(e){t.delay=e}):C.reject("Icon not found.")},getIcon:function(){return s[this.icon]?(n[this.icon]||(n[this.icon]=this.parse(s[this.icon])),n[this.icon]):null}}})),["marker","navbar-toggle-icon","overlay-icon","pagination-previous","pagination-next","totop"].forEach(function(t){return i(t)}),["slidenav-previous","slidenav-next"].forEach(function(t){return i(t,{init:function(){this.$addClass("uk-slidenav"),this.$hasClass("uk-slidenav-large")&&(this.icon+="-large")}})}),i("search-icon",{init:function(){this.$hasClass("uk-search-icon")&&this.$el.parents(".uk-search-large").length?this.icon="search-large":this.$el.parents(".uk-search-navbar").length&&(this.icon="search-navbar")}}),i("close",{init:function(){this.icon="close-"+(this.$hasClass("uk-close-large")?"large":"icon")}}),i("spinner",{connected:function(){var t=this;this.svg.then(function(e){return 1!==t.ratio&&St(e).find("circle").css("stroke-width",1/t.ratio)},X)}}),e.icon.add=function(i){Qt(s,i),Object.keys(i).forEach(function(t){return delete n[t]}),e._initialized&&t.each(e.instances,function(t,e){"icon"===e.$options.name&&e.$reset()})}},Ye=function(t){t.component("margin",{props:{margin:String,firstColumn:Boolean},defaults:{margin:"uk-margin-small-top",firstColumn:"uk-first-column"},computed:{items:function(){return this.$el[0].children}},update:{read:function(){var t=this;if(this.items.length&&0!==this.$el[0].offsetHeight){this.stacks=!0;for(var e=[[]],i=0;i<this.items.length;i++){var n=t.items[i],s=n.getBoundingClientRect();if(s.height)for(var o=e.length-1;o>=0;o--){var r=e[o];if(!r[0]){r.push(n);break}var a=r[0].getBoundingClientRect();if(s.top>=Math.floor(a.bottom)){e.push([n]);break}if(Math.floor(s.bottom)>a.top){if(t.stacks=!1,s.left<a.left&&!Bt){r.unshift(n);break}r.push(n);break}if(0===o){e.unshift([n]);break}}}this.rows=e}else this.rows=!1},write:function(){var t=this;this.rows&&this.rows.forEach(function(e,i){return e.forEach(function(e,n){t.$toggleClass(e,t.margin,0!==i),t.$toggleClass(e,t.firstColumn,0===n)})})},events:["load","resize"]}})},Ve=function(t){t.component("modal",{mixins:[De],defaults:{clsPage:"uk-modal-page",clsPanel:"uk-modal-dialog",selClose:".uk-modal-close, .uk-modal-close-default, .uk-modal-close-outside, .uk-modal-close-full"},events:[{name:"show",self:!0,handler:function(){this.panel.hasClass("uk-margin-auto-vertical")?this.$el.addClass("uk-flex"):this.$el.css("display","block"),this.$el.height()}},{name:"hidden",self:!0,handler:function(){this.$el.css("display","").removeClass("uk-flex")}}]}),t.component("overflow-auto",{mixins:[Se],computed:{modal:function(){return this.$el.closest(".uk-modal")},panel:function(){return this.$el.closest(".uk-modal-dialog")}},connected:function(){this.$el.css("min-height",150)},update:{write:function(){var t=this.$el.css("max-height");this.$el.css("max-height",150).css("max-height",Math.max(150,150+this.modal.height()-this.panel.outerHeight(!0))),t!==this.$el.css("max-height")&&this.$el.trigger("resize")},events:["load","resize"]}}),t.modal.dialog=function(e,i){var n=t.modal(' <div class="uk-modal"> <div class="uk-modal-dialog">'+e+"</div> </div> ",i);return n.$el.on("hidden",function(t){t.target===t.currentTarget&&n.$destroy(!0)}),n.show(),n},t.modal.alert=function(e,i){return i=Qt({bgClose:!1,escClose:!1,labels:t.modal.labels},i),C(function(n){return t.modal.dialog(' <div class="uk-modal-body">'+(N(e)?e:St(e).html())+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-primary uk-modal-close" autofocus>'+i.labels.ok+"</button> </div> ",i).$el.on("hide",n)})},t.modal.confirm=function(e,i){return i=Qt({bgClose:!1,escClose:!1,labels:t.modal.labels},i),C(function(n,s){return t.modal.dialog(' <div class="uk-modal-body">'+(N(e)?e:St(e).html())+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close">'+i.labels.cancel+'</button> <button class="uk-button uk-button-primary uk-modal-close" autofocus>'+i.labels.ok+"</button> </div> ",i).$el.on("click",".uk-modal-footer button",function(t){return 0===St(t.target).index()?s():n()})})},t.modal.prompt=function(e,i,n){return n=Qt({bgClose:!1,escClose:!1,labels:t.modal.labels},n),C(function(s){var o=!1,r=t.modal.dialog(' <form class="uk-form-stacked"> <div class="uk-modal-body"> <label>'+(N(e)?e:St(e).html())+'</label> <input class="uk-input" type="text" autofocus> </div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+n.labels.cancel+'</button> <button class="uk-button uk-button-primary" type="submit">'+n.labels.ok+"</button> </div> </form> ",n),a=r.$el.find("input").val(i);r.$el.on("submit","form",function(t){t.preventDefault(),s(a.val()),o=!0,r.hide()}).on("hide",function(){o||s(null)})})},t.modal.labels={ok:"Ok",cancel:"Cancel"}},Ue=function(t){t.component("nav",t.components.accordion.extend({name:"nav",defaults:{targets:"> .uk-parent",toggle:"> a",content:"ul:first"}}))},Xe=function(t){t.component("navbar",{mixins:[Se],props:{dropdown:String,mode:"list",align:String,offset:Number,boundary:Boolean,boundaryAlign:Boolean,clsDrop:String,delayShow:Number,delayHide:Number,dropbar:Boolean,dropbarMode:String,dropbarAnchor:"jQuery",duration:Number},defaults:{dropdown:".uk-navbar-nav > li",align:Bt?"right":"left",clsDrop:"uk-navbar-dropdown",mode:void 0,offset:void 0,delayShow:void 0,delayHide:void 0,boundaryAlign:void 0,flip:"x",boundary:!0,dropbar:!1,dropbarMode:"slide",dropbarAnchor:!1,duration:200},computed:{boundary:function(){return!0===this.$props.boundary||this.boundaryAlign?this.$el:this.$props.boundary},pos:function(){return"bottom-"+this.align}},ready:function(){this.dropbar&&t.navbarDropbar(y(this.dropbar,this.$el)||St("<div></div>").insertAfter(this.dropbarAnchor||this.$el),{clsDrop:this.clsDrop,mode:this.dropbarMode,duration:this.duration,navbar:this})},update:function(){t.drop(St(this.dropdown+" ."+this.clsDrop,this.$el).filter(function(e,i){return!t.getComponent(i,"dropdown")}),Qt({},this.$props,{boundary:this.boundary,pos:this.pos}))},events:[{name:oe,delegate:function(){return this.dropdown},handler:function(t){var e=t.currentTarget,i=this.getActive();i&&i.toggle&&!u(i.toggle.$el,e)&&!i.tracker.movesTo(i.$el)&&i.hide(!1)}}],methods:{getActive:function(){var e=t.drop.getActive();return e&&!~e.mode.indexOf("click")&&u(e.toggle.$el,this.$el)&&e}}}),t.component("navbar-dropbar",{mixins:[Se],defaults:{clsDrop:"",mode:"slide",navbar:null,duration:200},init:function(){"slide"===this.mode&&this.$addClass("uk-navbar-dropbar-slide")},events:[{name:"beforeshow",el:function(){return this.navbar.$el},handler:function(t,e){var i=e.$el;if("bottom"===e.dir&&!u(i,this.$el))return i.appendTo(this.$el),e.show(),!1}},{name:"mouseleave",handler:function(){var t=this.navbar.getActive();t&&!this.$el.is(":hover")&&t.hide()}},{name:"beforeshow",handler:function(t,e){var i=e.$el;this.clsDrop&&i.addClass(this.clsDrop+"-dropbar"),this.transitionTo(i.outerHeight(!0))}},{name:"beforehide",handler:function(t,e){var i=e.$el,n=this.navbar.getActive();if(this.$el.is(":hover")&&n&&n.$el.is(i))return!1}},{name:"hide",handler:function(t,e){var i=e.$el,n=this.navbar.getActive();(!n||n&&n.$el.is(i))&&this.transitionTo(0)}}],methods:{transitionTo:function(t){return this.$el.height(this.$el[0].offsetHeight?this.$el.height():0),Mt.cancel(this.$el),Mt.start(this.$el,{height:t},this.duration).then(null,X)}}})},Qe=function(t){t.component("offcanvas",{mixins:[De],args:"mode",props:{content:String,mode:String,flip:Boolean,overlay:Boolean},defaults:{content:".uk-offcanvas-content:first",mode:"slide",flip:!1,overlay:!1,clsPage:"uk-offcanvas-page",clsContainer:"uk-offcanvas-container",clsPanel:"uk-offcanvas-bar",clsFlip:"uk-offcanvas-flip",clsContent:"uk-offcanvas-content",clsContentAnimation:"uk-offcanvas-content-animation",clsSidebarAnimation:"uk-offcanvas-bar-animation",clsMode:"uk-offcanvas",clsOverlay:"uk-offcanvas-overlay",selClose:".uk-offcanvas-close"},computed:{content:function(){return St(y(this.$props.content,this.$el))},clsFlip:function(){return this.flip?this.$props.clsFlip:""},clsOverlay:function(){return this.overlay?this.$props.clsOverlay:""},clsMode:function(){return this.$props.clsMode+"-"+this.mode},clsSidebarAnimation:function(){return"none"===this.mode||"reveal"===this.mode?"":this.$props.clsSidebarAnimation},clsContentAnimation:function(){return"push"!==this.mode&&"reveal"!==this.mode?"":this.$props.clsContentAnimation},transitionElement:function(){return"reveal"===this.mode?this.panel.parent():this.panel}},update:{write:function(){this.isToggled()&&((this.overlay||this.clsContentAnimation)&&this.content.width(window.innerWidth-this.scrollbarWidth),this.overlay&&(this.content.height(window.innerHeight),Ee&&this.content.scrollTop(Ee.y)))},events:["resize"]},events:[{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){var e=t.currentTarget;e.hash&&this.content.find(e.hash).length&&(Ee=null,this.hide())}},{name:"beforescroll",filter:function(){return this.overlay},handler:function(t,e,i){if(e&&i&&this.isToggled()&&this.content.find(i).length)return this.$el.one("hidden",function(){return e.scrollTo(i)}),!1}},{name:"show",self:!0,handler:function(){Ee=Ee||{x:window.pageXOffset,y:window.pageYOffset},"reveal"!==this.mode||this.panel.parent().hasClass(this.clsMode)||this.panel.wrap("<div>").parent().addClass(this.clsMode),Nt.css("overflow-y",(!this.clsContentAnimation||this.flip)&&this.scrollbarWidth&&this.overlay?"scroll":""),this.body.addClass(this.clsContainer+" "+this.clsFlip+" "+this.clsOverlay).height(),this.content.addClass(this.clsContentAnimation),this.panel.addClass(this.clsSidebarAnimation+" "+("reveal"!==this.mode?this.clsMode:"")),this.$el.addClass(this.clsOverlay).css("display","block").height()}},{name:"hide",self:!0,handler:function(){this.content.removeClass(this.clsContentAnimation),("none"===this.mode||this.getActive()&&this.getActive()!==this)&&this.panel.trigger(ae)}},{name:"hidden",self:!0,handler:function(){if("reveal"===this.mode&&this.panel.unwrap(),this.overlay){if(!Ee){var t=this.content[0],e=t.scrollLeft,i=t.scrollTop;Ee={x:e,y:i}}}else Ee={x:window.pageXOffset,y:window.pageYOffset};this.panel.removeClass(this.clsSidebarAnimation+" "+this.clsMode),this.$el.removeClass(this.clsOverlay).css("display",""),this.body.removeClass(this.clsContainer+" "+this.clsFlip+" "+this.clsOverlay).scrollTop(Ee.y),Nt.css("overflow-y",""),this.content.width("").height(""),window.scrollTo(Ee.x,Ee.y),Ee=null}},{name:"swipeLeft swipeRight",handler:function(t){this.isToggled()&&vt(t)&&("swipeLeft"===t.type&&!this.flip||"swipeRight"===t.type&&this.flip)&&this.hide()}}]})},Je=function(t){t.component("responsive",{props:["width","height"],init:function(){this.$addClass("uk-responsive-width")},update:{read:function(){this.dim=!!(this.$el.is(":visible")&&this.width&&this.height)&&{width:this.$el.parent().width(),height:this.height}},write:function(){this.dim&&this.$el.height(qt.contain({height:this.height,width:this.width},this.dim).height)},events:["load","resize"]}})},Ge=function(t){t.component("scroll",{props:{duration:Number,easing:String,offset:Number},defaults:{duration:1e3,easing:"easeOutExpo",offset:0},methods:{scrollTo:function(t){var e=this,i=ht(St(t))-this.offset,n=v(),s=window.innerHeight;i+s>n&&(i=n-s),!1!==a(this.$el,"beforescroll",[this,t]).result&&St("html,body").stop().animate({scrollTop:Math.round(i)},this.duration,this.easing).promise().then(function(){return e.$el.trigger("scrolled",[e,t])})}},events:{click:function(t){t.isDefaultPrevented()||(t.preventDefault(),this.scrollTo(St(this.$el[0].hash).length?this.$el[0].hash:"body"))}}}),St.easing.easeOutExpo=St.easing.easeOutExpo||function(t,e,i,n,s){return e===s?i+n:n*(1-Math.pow(2,-10*e/s))+i}},Ze=function(t){t.component("scrollspy",{args:"cls",props:{cls:"list",target:String,hidden:Boolean,offsetTop:Number,offsetLeft:Number,repeat:Boolean,delay:Number},defaults:{cls:["uk-scrollspy-inview"],target:!1,hidden:!0,offsetTop:0,offsetLeft:0,repeat:!1,delay:0,inViewClass:"uk-scrollspy-inview"},computed:{elements:function(){return this.target&&St(this.target,this.$el)||this.$el}},update:[{write:function(){this.hidden&&this.elements.filter(":not(."+this.inViewClass+")").css("visibility","hidden")}},{read:function(){var t=this;this.elements.each(function(e,i){if(!i._scrollspy){var n=St(i).attr("uk-scrollspy-class");i._scrollspy={toggles:n&&n.split(",")||t.cls}}i._scrollspy.show=g(i,t.offsetTop,t.offsetLeft)})},write:function(){var t=this,e=1===this.elements.length?1:0;this.elements.each(function(i,n){var s=St(n),o=n._scrollspy,r=o.toggles[i]||o.toggles[0];if(o.show){if(!o.inview&&!o.timer){var a=function(){s.css("visibility","").addClass(t.inViewClass).toggleClass(r).trigger("inview"),t.$update(),o.inview=!0,delete o.timer};t.delay&&e?o.timer=setTimeout(a,t.delay*e):a(),e++}}else o.inview&&t.repeat&&(o.timer&&(clearTimeout(o.timer),delete o.timer),s.removeClass(t.inViewClass).toggleClass(r).css("visibility",t.hidden?"hidden":"").trigger("outview"),t.$update(),o.inview=!1)})},events:["scroll","load","resize"]}]})},Ke=function(t){t.component("scrollspy-nav",{props:{cls:String,closest:String,scroll:Boolean,overflow:Boolean,offset:Number},defaults:{cls:"uk-active",closest:!1,scroll:!1,overflow:!0,offset:0},computed:{links:function(){return this.$el.find('a[href^="#"]').filter(function(t,e){return e.hash})},elements:function(){return this.closest?this.links.closest(this.closest):this.links},targets:function(){return St(this.links.toArray().map(function(t){return t.hash}).join(","))}},update:[{read:function(){this.scroll&&t.scroll(this.links,{offset:this.offset||0})}},{read:function(){var t=this,e=window.pageYOffset+this.offset+1,i=v()-window.innerHeight+this.offset;this.active=!1,this.targets.each(function(n,s){var o=ht(s),r=n+1===t.targets.length;if(!t.overflow&&(0===n&&o>e||r&&o+s.offsetTop<e))return!1;if(r||!(ht(t.targets.eq(n+1))<=e)){if(e>=i)for(var a=t.targets.length-1;a>n;a--)if(g(t.targets[a])){s=t.targets[a];break}return!(t.active=j(t.links.filter('[href="#'+s.id+'"]')))}})},write:function(){this.links.blur(),this.elements.removeClass(this.cls),this.active&&this.$el.trigger("active",[this.active,(this.closest?this.active.closest(this.closest):this.active).addClass(this.cls)])},events:["scroll","load","resize"]}]})},ti=function(e){e.component("sticky",{mixins:[Se],attrs:!0,props:{top:null,bottom:Boolean,offset:Number,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,widthElement:"jQuery",showOnUp:Boolean,media:"media",target:Number},defaults:{top:0,bottom:!1,offset:0,animation:"",clsActive:"uk-active",clsInactive:"",clsFixed:"uk-sticky-fixed",clsBelow:"uk-sticky-below",selTarget:"",widthElement:!1,showOnUp:!1,media:!1,target:!1},computed:{selTarget:function(){return this.$props.selTarget&&j(this.$props.selTarget,this.$el)||this.$el}},connected:function(){this.placeholder=St('<div class="uk-sticky-placeholder"></div>'),this.widthElement=this.$props.widthElement||this.placeholder,this.isActive||this.hide()},disconnected:function(){this.isActive&&(this.isActive=!1,this.hide(),this.$removeClass(this.clsInactive)),this.placeholder.remove(),this.placeholder=null,this.widthElement=null},ready:function(){var t=this;if(this.target&&location.hash&&window.pageYOffset>0){var e=y(location.hash);e&&Gt(function(){var i=ht(e),n=ht(t.$el),s=t.$el[0].offsetHeight;n+s>=i&&n<=i+e[0].offsetHeight&&window.scrollTo(0,i-s-t.target-t.offset)})}},events:[{name:"active",handler:function(){this.$addClass(this.selTarget,this.clsActive),this.$removeClass(this.selTarget,this.clsInactive)}},{name:"inactive",handler:function(){this.$addClass(this.selTarget,this.clsInactive),this.$removeClass(this.selTarget,this.clsActive)}}],update:[{write:function(){var e,i=this,n=(this.isActive?this.placeholder:this.$el)[0].offsetHeight;this.placeholder.css("height","absolute"!==this.$el.css("position")?n:"").css(this.$el.css(["marginTop","marginBottom","marginLeft","marginRight"])),document.documentElement.contains(this.placeholder[0])||this.placeholder.insertAfter(this.$el).attr("hidden",!0),this.width=this.widthElement.attr("hidden",null)[0].offsetWidth,this.widthElement.attr("hidden",!this.isActive),this.topOffset=ht(this.isActive?this.placeholder:this.$el),this.bottomOffset=this.topOffset+n,["top","bottom"].forEach(function(n){i[n]=i.$props[n],i[n]&&(t.isNumeric(i[n])?i[n]=i[n+"Offset"]+parseFloat(i[n]):N(i[n])&&i[n].match(/^-?\d+vh$/)?i[n]=window.innerHeight*parseFloat(i[n])/100:(e=!0===i[n]?i.$el.parent():y(i[n],i.$el))&&(i[n]=ht(e)+e[0].offsetHeight))}),this.top=Math.max(parseFloat(this.top),this.topOffset)-this.offset,this.bottom=this.bottom&&this.bottom-n,this.inactive=this.media&&!window.matchMedia(this.media).matches,this.isActive&&this.update()},events:["load","resize"]},{read:function(){this.offsetTop=ht(this.$el),this.scroll=window.pageYOffset,this.visible=this.$el.is(":visible")},write:function(t){var e=this;void 0===t&&(t={});var i=t.dir,n=this.scroll;if(!(n<0||!this.visible||this.disabled||this.showOnUp&&!i))if(this.inactive||n<this.top||this.showOnUp&&(n<=this.top||"down"===i||"up"===i&&!this.isActive&&n<=this.bottomOffset)){if(!this.isActive)return;this.isActive=!1,this.animation&&n>this.topOffset?Lt.cancel(this.$el).then(function(){return Lt.out(e.$el,e.animation).then(function(){return e.hide()},X)}):this.hide()}else this.isActive?this.update():this.animation?Lt.cancel(this.$el).then(function(){e.show(),Lt.in(e.$el,e.animation).then(null,X)}):this.show()},events:["scroll"]}],methods:{show:function(){this.isActive=!0,this.update(),this.placeholder.attr("hidden",null)},hide:function(){this.isActive&&!this.$hasClass(this.selTarget,this.clsActive)||this.$el.trigger("inactive"),this.$removeClass(this.clsFixed,this.clsBelow),this.$el.css({position:"",top:"",width:""}),this.placeholder.attr("hidden",!0)},update:function(){var t=this,e=Math.max(0,this.offset),i=this.scroll>this.top;this.bottom&&this.scroll>this.bottom-this.offset&&(e=this.bottom-this.scroll),this.$el.css({position:"fixed",top:e+"px",width:this.width}),this.$hasClass(this.selTarget,this.clsActive)?i||this.$el.trigger("inactive"):i&&this.$el.trigger("active"),this.$toggleClass(this.clsBelow,this.scroll>this.bottomOffset),this.showOnUp?Gt(function(){return t.$addClass(t.clsFixed)}):this.$addClass(this.clsFixed)}}})},ei={},ii=new DOMParser,ni=function(e){e.component("svg",{attrs:!0,props:{id:String,icon:String,src:String,style:String,width:Number,height:Number,ratio:Number,class:String},defaults:{ratio:1,id:!1,exclude:["src"],class:""},init:function(){this.class+=" uk-svg"},connected:function(){var t=this;if(!this.icon&&this.src&&~this.src.indexOf("#")){var e=this.src.split("#");e.length>1&&(this.src=e[0],this.icon=e[1])}this.width=this.$props.width,this.height=this.$props.height,this.svg=this.getSvg().then(function(e){return C(function(i,n){var s,o;if(e){if(t.icon)if(s=e.getElementById(t.icon)){var r=s.outerHTML;if(!r){var a=document.createElement("div");a.appendChild(s.cloneNode(!0)),r=a.innerHTML}r=r.replace(/<symbol/g,"<svg"+(~r.indexOf("xmlns")?"":' xmlns="http://www.w3.org/2000/svg"')).replace(/symbol>/g,"svg>"),o=ii.parseFromString(r,"image/svg+xml").documentElement}else e.querySelector("symbol")||(o=e.documentElement.cloneNode(!0));else o=e.documentElement.cloneNode(!0);if(o){var l=o.getAttribute("viewBox");l&&(l=l.split(" "),t.width=t.width||l[2],t.height=t.height||l[3]),t.width*=t.ratio,t.height*=t.ratio;for(var h in t.$options.props)t[h]&&!~t.exclude.indexOf(h)&&o.setAttribute(h,t[h]);t.id||o.removeAttribute("id"),t.width&&!t.height&&o.removeAttribute("height"),t.height&&!t.width&&o.removeAttribute("width");var c=t.$el[0];b(c)||"CANVAS"===c.tagName?(t.$el.attr({hidden:!0,id:null}),c.nextSibling?o.isEqualNode(c.nextSibling)?o=c.nextSibling:c.parentNode.insertBefore(o,c.nextSibling):c.parentNode.appendChild(o)):c.lastChild&&o.isEqualNode(c.lastChild)?o=c.lastChild:c.appendChild(o),i(o)}else n("SVG not found.")}else n("SVG not found.")})})},disconnected:function(){b(this.$el)&&this.$el.attr({hidden:null,id:this.id||null}),this.svg&&(this.svg.then(function(t){return t.parentNode&&t.parentNode.removeChild(t)},X),this.svg=null)},methods:{getSvg:function(){var e=this;return this.src?ei[this.src]?ei[this.src]:(ei[this.src]=C(function(i,n){0===e.src.lastIndexOf("data:",0)?i(e.parse(decodeURIComponent(e.src.split(",")[1]))):t.ajax(e.src,{dataType:"html"}).then(function(t){i(e.parse(t))},function(){n("SVG not found.")})}),ei[this.src]):C.reject()},parse:function(t){var e=ii.parseFromString(t,"image/svg+xml");return e.documentElement&&"svg"===e.documentElement.nodeName?e:null}}})},si=function(t){t.component("switcher",{mixins:[Oe],args:"connect",props:{connect:String,toggle:String,active:Number,swiping:Boolean},defaults:{connect:!1,toggle:" > *",active:0,swiping:!0,cls:"uk-active",clsContainer:"uk-switcher",attrItem:"uk-switcher-item",queued:!0},computed:{connects:function(){return y(this.connect,this.$el)||St(this.$el.next("."+this.clsContainer))},toggles:function(){return St(this.toggle,this.$el)}},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),this.show(t.currentTarget)}},{name:"click",el:function(){return this.connects},delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.show(St(t.currentTarget)[t.currentTarget.hasAttribute(this.attrItem)?"attr":"data"](this.attrItem))}},{name:"swipeRight swipeLeft",filter:function(){return this.swiping},el:function(){return this.connects},handler:function(t){vt(t)&&(t.preventDefault(),window.getSelection().toString()||this.show("swipeLeft"===t.type?"next":"previous"))}}],update:function(){this.updateAria(this.connects.children()),this.show(j(this.toggles.filter("."+this.cls+":first"))||j(this.toggles.eq(this.active))||this.toggles.first())},methods:{show:function(t){for(var e,i=this,n=this.toggles.length,s=this.connects.children("."+this.cls).index(),o=s>=0,r=w(t,this.toggles,s),a="previous"===t?-1:1,l=0;l<n;l++,r=(r+a+n)%n)if(!i.toggles.eq(r).is(".uk-disabled, [disabled]")){e=i.toggles.eq(r);break}!e||s>=0&&e.hasClass(this.cls)||s===r||(this.toggles.removeClass(this.cls).attr("aria-expanded",!1),e.addClass(this.cls).attr("aria-expanded",!0),o?this.toggleElement(this.connects.children(":nth-child("+(s+1)+"),:nth-child("+(r+1)+")")):this.toggleNow(this.connects.children(":nth-child("+(r+1)+")")))}}})},oi=function(t){t.component("tab",t.components.switcher.extend({mixins:[Se],name:"tab",props:{media:"media"},defaults:{media:960,attrItem:"uk-tab-item"},init:function(){var e=this.$hasClass("uk-tab-left")&&"uk-tab-left"||this.$hasClass("uk-tab-right")&&"uk-tab-right";e&&t.toggle(this.$el,{cls:e,mode:"media",media:this.media})}}))},ri=function(t){t.component("toggle",{mixins:[t.mixin.togglable],args:"target",props:{href:String,target:null,mode:"list",media:"media"},defaults:{href:!1,target:!1,mode:"click",queued:!0,media:!1},computed:{target:function(){return y(this.$props.target||this.href,this.$el)||this.$el}},events:[{name:oe+" "+re,filter:function(){return~this.mode.indexOf("hover")},handler:function(t){vt(t)||this.toggle("toggle"+(t.type===oe?"show":"hide"))}},{name:"click",filter:function(){return~this.mode.indexOf("click")||ee},handler:function(t){if(vt(t)||~this.mode.indexOf("click")){var e=St(t.target).closest("a[href]")[0];(St(t.target).closest('a[href="#"], button').length||e&&(this.cls||!this.target.is(":visible")||e.hash&&this.target.is(e.hash)))&&t.preventDefault(),this.toggle()}}}],update:{write:function(){if(~this.mode.indexOf("media")&&this.media){var t=this.isToggled(this.target);(window.matchMedia(this.media).matches?!t:t)&&this.toggle()}},events:["load","resize"]},methods:{toggle:function(t){a(this.target,t||"toggle",[this],!0).isDefaultPrevented()||this.toggleElement(this.target)}}})},ai=function(t){t.component("leader",{mixins:[Se],props:{fill:String,media:"media"},defaults:{fill:"",media:!1,clsWrapper:"uk-leader-fill",clsHide:"uk-leader-hide",attrFill:"data-fill"},computed:{fill:function(){return this.$props.fill||J("leader-fill")}},connected:function(){this.wrapper=this.$el.wrapInner('<span class="'+this.clsWrapper+'">').children().first()},disconnected:function(){this.wrapper.contents().unwrap()},update:[{read:function(){var t=this._width;this._width=Math.floor(this.$el[0].offsetWidth/2),this._changed=t!==this._width,this._hide=this.media&&!window.matchMedia(this.media).matches},write:function(){this.wrapper.toggleClass(this.clsHide,this._hide),this._changed&&this.wrapper.attr(this.attrFill,new Array(this._width).join(this.fill))},events:["load","resize"]}]})},li=function(t){t.component("video",{props:{automute:Boolean,autoplay:Boolean},defaults:{automute:!1,autoplay:!0},computed:{el:function(){return this.$el[0]}},ready:function(){this.player=new pe(this.el),this.automute&&this.player.mute()},update:{write:function(){this.player&&this.autoplay&&(0===this.el.offsetHeight||"hidden"===this.$el.css("visibility")?this.player.pause():this.player.play())},events:["load"]}})};return _e.version="3.0.0-beta.30",function(t){t.mixin.class=Se,t.mixin.modal=De,t.mixin.position=Ie,t.mixin.togglable=Oe}(_e),function(t){var e=0,i=0;n(window,"load resize",t.update),n(window,"scroll",function(i){i.dir=e<window.pageYOffset?"down":"up",e=window.pageYOffset,t.update(i),ce.flush()}),le&&n(document,le,function(t){var e=t.target;(Q(e,"animationName")||"").match(/^uk-.*(left|right)/)&&(i++,document.body.style.overflowX="hidden",setTimeout(function(){--i||(document.body.style.overflowX="")},Y(Q(e,"animationDuration"))+100))},!0),t.use(ri),t.use(Ne),t.use(Be),t.use(li),t.use(Pe),t.use(Me),t.use(He),t.use(je),t.use(Le),t.use(We),t.use(qe),t.use(Ye),t.use(Fe),t.use(ze),t.use(ai),t.use(Ve),t.use(Ue),t.use(Xe),t.use(Qe),t.use(Je),t.use(Ge),t.use(Ze),t.use(Ke),t.use(ti),t.use(ni),t.use(Re),t.use(si),t.use(oi)}(_e),_e.use(yt),_e.use(xt),_e.use(kt),_e.use(Ct),_e.use(Tt),_e.use(_t),_e.use(At),_e.use(Et),function(t){function e(){s(document.body,r),ce.flush(),new Jt(function(e){return e.forEach(function(e){for(var i=e.addedNodes,n=e.removedNodes,o=e.target,l=0;l<i.length;l++)s(i[l],r);for(l=0;l<n.length;l++)s(n[l],a);t.update("update",o,!0)})}).observe(o,{childList:!0,subtree:!0,characterData:!0,attributes:!0,attributeFilter:["href"]}),t._initialized=!0}function s(t,e){if(t.nodeType===Node.ELEMENT_NODE&&!t.hasAttribute("uk-no-boot"))for(e(t),t=t.firstChild;t;){var i=t.nextSibling;s(t,e),t=i}}var o=document.documentElement,r=t.connect,a=t.disconnect;Jt?document.body?e():new Jt(function(){document.body&&(this.disconnect(),e())}).observe(o,{childList:!0,subtree:!0}):i(function(){s(document.body,r),n(o,"DOMNodeInserted",function(t){return s(t.target,r)}),n(o,"DOMNodeRemoved",function(t){return s(t.target,a)})})}(_e),_e});