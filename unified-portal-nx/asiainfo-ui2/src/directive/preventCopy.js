/**
 * 防复制指令
 * 使用方法：
 * 1. 全局禁用复制：v-prevent-copy
 * 2. 局部禁用复制：v-prevent-copy="true"
 * 3. 启用复制：v-prevent-copy="false"
 */

const preventCopyDirective = {
  // Vue 3: 元素挂载到DOM时
  mounted(el, binding) {
    // 获取指令值，默认为true
    const shouldPrevent = binding.value !== false

    if (shouldPrevent) {
      // 为元素添加防复制功能
      addPreventCopyToElement(el)
    }
  },

  // Vue 3: 指令值更新时
  updated(el, binding) {
    const shouldPrevent = binding.value !== false

    if (shouldPrevent) {
      addPreventCopyToElement(el)
    } else {
      removePreventCopyFromElement(el)
    }
  },

  // Vue 3: 元素从DOM移除时
  beforeUnmount(el) {
    removePreventCopyFromElement(el)
  },

  // Vue 2 兼容性
  inserted(el, binding) {
    this.mounted(el, binding)
  },

  update(el, binding) {
    this.updated(el, binding)
  },

  unbind(el) {
    this.beforeUnmount(el)
  }
}

/**
 * 为元素添加防复制功能
 */
function addPreventCopyToElement(el) {
  // 如果已经添加过，先移除
  if (el._preventCopyEnabled) {
    removePreventCopyFromElement(el)
  }

  // 创建事件处理器
  const handlers = createEventHandlers()

  // 添加CSS类
  el.classList.add('prevent-copy')

  // 添加内联样式
  el.style.userSelect = 'none'
  el.style.webkitUserSelect = 'none'
  el.style.mozUserSelect = 'none'
  el.style.msUserSelect = 'none'
  el.style.webkitTouchCallout = 'none'
  el.style.webkitTapHighlightColor = 'transparent'

  // 禁用右键菜单
  el.addEventListener('contextmenu', handlers.preventContextMenu, false)

  // 禁用文本选择
  el.addEventListener('selectstart', handlers.preventSelection, false)
  el.addEventListener('mousedown', handlers.preventMouseDown, false)

  // 禁用拖拽
  el.addEventListener('dragstart', handlers.preventDrag, false)

  // 禁用键盘快捷键
  el.addEventListener('keydown', handlers.preventKeyboardShortcuts, false)

  // 为图片添加特殊处理
  const images = el.querySelectorAll('img')
  images.forEach(img => {
    img.style.pointerEvents = 'none'
    img.style.webkitUserDrag = 'none'
    img.style.userDrag = 'none'
    img.draggable = false
  })

  // 保存处理器引用，用于后续移除
  el._preventCopyHandlers = handlers
  // 标记元素已添加防复制功能
  el._preventCopyEnabled = true
}

/**
 * 从元素移除防复制功能
 */
function removePreventCopyFromElement(el) {
  if (!el._preventCopyEnabled || !el._preventCopyHandlers) return

  const handlers = el._preventCopyHandlers

  // 移除CSS类
  el.classList.remove('prevent-copy')

  // 恢复样式
  el.style.userSelect = ''
  el.style.webkitUserSelect = ''
  el.style.mozUserSelect = ''
  el.style.msUserSelect = ''
  el.style.webkitTouchCallout = ''
  el.style.webkitTapHighlightColor = ''

  // 移除事件监听器
  el.removeEventListener('contextmenu', handlers.preventContextMenu, false)
  el.removeEventListener('selectstart', handlers.preventSelection, false)
  el.removeEventListener('mousedown', handlers.preventMouseDown, false)
  el.removeEventListener('dragstart', handlers.preventDrag, false)
  el.removeEventListener('keydown', handlers.preventKeyboardShortcuts, false)

  // 恢复图片
  const images = el.querySelectorAll('img')
  images.forEach(img => {
    img.style.pointerEvents = ''
    img.style.webkitUserDrag = ''
    img.style.userDrag = ''
    img.draggable = true
  })

  // 清理引用
  el._preventCopyHandlers = null
  // 标记元素已移除防复制功能
  el._preventCopyEnabled = false
}

// 创建事件处理函数的工厂，确保每个元素都有独立的处理函数
function createEventHandlers() {
  return {
    /**
     * 阻止右键菜单
     */
    preventContextMenu(e) {
      e.preventDefault()
      e.stopPropagation()
      return false
    },

    /**
     * 阻止文本选择
     */
    preventSelection(e) {
      e.preventDefault()
      e.stopPropagation()
      return false
    },

    /**
     * 阻止鼠标按下事件
     */
    preventMouseDown(e) {
      // 只阻止非输入元素的鼠标事件
      if (!isInputElement(e.target)) {
        e.preventDefault()
        return false
      }
    },

    /**
     * 阻止拖拽
     */
    preventDrag(e) {
      e.preventDefault()
      e.stopPropagation()
      return false
    },

    /**
     * 阻止键盘快捷键
     */
    preventKeyboardShortcuts(e) {
      // 如果是输入元素，允许正常的键盘操作
      if (isInputElement(e.target)) {
        return true
      }

      // 禁用常见的复制快捷键
      if (
        // Ctrl+A (全选)
        (e.ctrlKey && e.keyCode === 65) ||
        // Ctrl+C (复制)
        (e.ctrlKey && e.keyCode === 67) ||
        // Ctrl+V (粘贴)
        (e.ctrlKey && e.keyCode === 86) ||
        // Ctrl+X (剪切)
        (e.ctrlKey && e.keyCode === 88) ||
        // Ctrl+S (保存)
        (e.ctrlKey && e.keyCode === 83) ||
        // Ctrl+P (打印)
        (e.ctrlKey && e.keyCode === 80) ||
        // Ctrl+U (查看源码)
        (e.ctrlKey && e.keyCode === 85) ||
        // F12 (开发者工具)
        e.keyCode === 123 ||
        // Ctrl+Shift+I (开发者工具)
        (e.ctrlKey && e.shiftKey && e.keyCode === 73) ||
        // Ctrl+Shift+J (控制台)
        (e.ctrlKey && e.shiftKey && e.keyCode === 74) ||
        // Ctrl+Shift+C (元素选择器)
        (e.ctrlKey && e.shiftKey && e.keyCode === 67)
      ) {
        e.preventDefault()
        e.stopPropagation()
        return false
      }
    }
  }
}

/**
 * 判断是否为输入元素
 */
function isInputElement(element) {
  const inputTags = ['INPUT', 'TEXTAREA', 'SELECT']
  return inputTags.includes(element.tagName) ||
         element.contentEditable === 'true' ||
         element.isContentEditable
}

export default preventCopyDirective
