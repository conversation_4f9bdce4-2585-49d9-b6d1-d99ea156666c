<template>
  <div class="protal">
    <div class="bj_protal">
      <div class="protal_header">
        <div class="header-left-wrap">
          <img src="@/assets/staticpage_images/logox.png" alt="" class="logo">
          <ul>
            <li
              v-for="(item, key) of filteredMenuSubNav"
              :key="key"
              @mouseover="Subnav(item, key)"
              @click="jumpPage(item)"
            >
              {{ item.menuName }}
              <img
                v-if="item.showImg"
                src="@/assets/staticpage_images/zuoyoushangxiatongyong.png"
                alt=""
                :class="showSubnav == true && key == cut ? 'sImgs' : 'sImg'"
                width="10"
                height="6"
                style="
                  margin-top: -3px;
                  margin-left: 4px;
                  transition: all linear 0.3s;
                "
              >
            </li>
          </ul>
        </div>

        <div class="header-right-wrap">
          <ul>
            <li
              v-for="(item, key) of filteredSubNav"
              :key="key"
              style="
                color: #ff9900;
                font-family: SourceHanSansSC-Medium;
                min-width: 85px;
              "
              @mouseover="Subnav(item, key)"
              @click="jumpPage(item)"
            >
              {{ item.menuName }}
              <img
                v-if="item.showImg"
                src="@/assets/staticpage_images/zuoyoushangxiatongyong.png"
                alt=""
                :class="showSubnav == true && key == cut ? 'sImgs' : 'sImg'"
                width="10"
                height="6"
                style="
                  margin-top: -3px;
                  margin-left: 4px;
                  transition: all linear 0.3s;
                "
              >
            </li>
          </ul>

          <div class="ico" style="margin-left: 16px" @click="showSupervise">
            <img
              src="@/assets/staticpage_images/xiaoxi1.png"
              alt=""
              class="smallImg"
            >
            <span v-if="total !== 0" class="numbers">{{ total }}</span>
          </div>

          <span class="monad" style="min-width: 50px">省系统</span>
          <img
            v-if="selectHeader == '1'"
            src="@/assets/staticpage_images/role_1.png"
            width="24px"
            alt=""
            class="headPortraits"
          >
          <img
            v-else-if="selectHeader == '2'"
            src="@/assets/staticpage_images/role_2.jpg"
            width="24px"
            alt=""
            class="headPortraits"
          >
          <img
            v-else
            src="@/assets/staticpage_images/role_3.jpg"
            width="24px"
            alt=""
            class="headPortraits"
          >
          <span class="people" style="min-width: 50px">
            {{ name }}
          </span>
          <img
            src="@/assets/staticpage_images/zuoyoushangxiatongyong.png"
            alt=""
            class="sImg"
            width="10"
            height="6"
          >
        </div>
      </div>
      <!-- 菜单 -->
      <transition name="fade">
        <div v-if="showSubnav" class="maskLayer" @click="showSubnav = false">
          <div class="nav-box" @click.stop>
            <div style="width: 25.4%; height: 100%">
              <div style="padding: 60px 46px 0 46px">
                <el-input
                  v-model="textSubnav"
                  placeholder="请输入您想要搜索的内容..."
                  suffix-icon="el-icon-search"
                  class="menu_input"
                  @change="searchSubnav"
                />
                <ul v-show="showSecondeNav" class="SubTitle">
                  <li
                    v-for="(item, index) of secondSubnav"
                    :key="index"
                    :class="index === current ? 'active' : ''"
                    @mouseover="addClass(index, item)"
                    @click="toggleNav(item)"
                  >
                    <span>{{ item.menuName }}</span>
                  </li>
                </ul>
              </div>
            </div>
            <div style="width: 75.6%; height: 100%; background: #fff">
              <div style="padding: 42px 161px 202px 81px">
                <div class="levelThree">{{ threeMenu }}</div>
                <ul v-show="showThreeNav" :class="{levelThreeTitle:true,moreItem:threeSubnav.length>5?true:false}">
                  <li
                    v-for="(value, keys) in threeSubnav"
                    :key="keys"
                    :class="keys === currents ? 'actives' : 'actives'"
                    @mouseover="setClass(keys)"
                    @click="goThreeSubnav(value)"
                  >
                    <span>{{ value.menuName }}</span>
                    <span class="threeDetail">{{ value.detail }}</span>
                  </li>
                </ul>
              </div>
            </div>
            <!-- <div class="closeX" @click="closeSubnav">X</div> -->
            <div class="closeX" @click="closeSubnav">
              <img

                src="./assets/close.png"
                alt=""
              >
            </div>
          </div>
        </div>
      </transition>
      <!-- 督办事件 -->
      <div
        v-if="supervise"
        class="supervise_masklayer"
        @click="supervise = false"
      >
        <div class="supervise" @click.stop>
          <div class="supervise_event">
            <div class="backlog">
              <p>待办（{{ total }}）</p>
            </div>
            <div
              v-if="this.total != 0"
              style="display: flex; flex-direction: column; height: 100%"
            >
              <ul style="flex-grow: 1">
                <li v-for="(item, index) of IdentyTypeList" :key="item.taskId">
                  <div class="image">
                    <p>督</p>
                  </div>
                  <div class="matter" @click="goTodetail(item)">
                    <span
                      style="
                        font-family: SourceHanSansSC-Medium;
                        font-size: 16px;
                        color: #262626;
                      "
                    >{{ item.orderName }}</span>
                    <span
                      style="
                        font-family: SourceHanSansSC-Regular;
                        font-size: 14px;
                        color: #595959;
                      "
                    >{{ item.orderDetail }}</span>
                    <span
                      style="
                        font-family: SourceHanSansSC-Regular;
                        font-size: 14px;
                        color: #595959;
                      "
                    >{{ item.opTime }}</span>
                  </div>
                </li>
              </ul>
              <div class="paging">
                <p
                  :class="pageNum == 1 ? 'nopage' : 'okpage'"
                  @click="change(-1, $event)"
                >
                  上一页
                </p>
                <p
                  :class="pageNum == pageCount ? 'nopage' : 'okpage'"
                  @click="change(+1, $event)"
                >
                  下一页
                </p>
              </div>
            </div>
            <!-- <div v-else class="noData">
                           暂无待办数据
                    </div> -->
            <div v-else class="nothing">
              <div class="nothing-img" />
              <p>暂无待办数据！</p>
            </div>
          </div>
        </div>
      </div>

      <el-scrollbar
        ref="scrollbar"
        style="width: 100%; height: 100px; flex-grow: 2;"
      >
        <router-view style="width: 100%" @roleName="handelRoleName" />

        <div class="protal_footer" style="display:none;">
          <div style="display: flex; width: auto">
            <img
              src="@/assets/staticpage_images/编组 2.png"
              alt=""
              class="bianzu2"
            >
            <div class="wire" />
            <div class="indicate">
              <div class="firseFloor">
                <p>权限申请</p>
                <p>绑定进程</p>
                <p>平台介绍</p>
                <p>帮助文档</p>
                <p>业务规范</p>
                <p>视觉规范</p>
              </div>
              <div class="secondFloor">
                <p>这里是一个链接</p>
                <p>这里是一个链接</p>
              </div>
              <div class="thirsFloor">
                <p>公司</p>
                <p>版权所有</p>
                <p>京</p>
              </div>
            </div>
          </div>
          <div class="phone">
            <p>联系我们</p>
            <p>联系电话：</p>
            <p>联系邮箱：</p>
          </div>
        </div>
      </el-scrollbar>
    </div>
     <!-- <el-dialog title="金库认证" width="55%" class="msgdialog" :visible.sync="jkDialogVis">
      <div>1111</div>
    </el-dialog> -->
    <JKDialog :visibleKey="jkDialogVis"  @closeJKdialog="jkDialogVis=false"/>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { eventBus } from '@/main.js'
import JKDialog from '@/views/bj_proatal_web/nx-components/jkauth/jkAuthDialog.vue'

import {
  getName,
  getFrontRouters,
  getFrontMenu,
  getMenu,
  getGroupToken,
  getIdenty,
  postLog,
  getForjump
} from '@/api/portal/charts'
import { setToken, getNameInCookie } from '@/utils/auth'
import './assets/icon/iconfont.css'
import CryptoJS from 'crypto-js'
import { checkToken } from '@/api/portal/charts'
import './nx-components/directive/permission.js'
import { recordAdd, recordAddTo4a } from '@/api/authority-data'
import timeTool from '@/views/bj_proatal_web/lib/date.js'


function aesEncryptEBC(key, word) {
  const _key = CryptoJS.enc.Utf8.parse(key)
  const _word = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(_word, _key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}

export default {
  name: 'Home',
  components:{JKDialog},
  provide() {
    const mapPermission = JSON.parse(sessionStorage.getItem('nx-map-permission') || '{}')
    // const userInfoString = localStorage.getItem('userInfo')
    // // *************************** 是否可以下载报表
    // let canDownReportsData = false
    // if (userInfoString) {
    //   const userInfoObj = JSON.parse(userInfoString)
    //   const { roleInfos = [] } = userInfoObj
    //   canDownReportsData = roleInfos.some((e) => e.roleId == 9)
    // }
    // // ***************************
    return {
      changeScrollTop: this.changeScrollTop,
      showExportBtn: false,
      // 地图权限，先默认为整个宁夏地区
      mapPermission: { ...mapPermission }
      // canDownReportsData: canDownReportsData
      // mapPermission: { name: '宁夏', mapLevel: 1, cityId: '640000', id: '640000', cityid: '640000', parentCityId: null, parentCityName: null },
      // mapPermission: { name: '银川市', mapLevel: 2, cityId: '640100', cityid: '640100', parentCityId: '640000', parentCityName: '宁夏'},
      // mapPermission: { name: '兴庆区', mapLevel: 3, cityId: '640104', cityid: '640104', parentCityId: '640100', parentCityName: '银川市'},
    }
  },
  data() {
    return {
      jkDialogVis:false,
      // 校验请求头信息
      token: '',
      // 待办事项token值
      access_token: '',
      pageCount: 1,
      // 总条数
      total: 0,
      // 待办列表数据
      IdentyTypeList: [],
      pageNum: 1,
      pageSize: 4,
      cut: 0,
      // 选择头像
      selectHeader: '',
      // 显示督办系统
      supervise: false,
      // 显示页尾
      showFooter: true,
      showThreeNav: true,
      showSecondeNav: true,
      currents: 0,
      threeMenu: '',
      current: 0,
      subNav: [],
      // 三级菜单
      threeSubnav: [],
      // 二级菜单
      secondSubnav: [],
      // 搜索值
      textSubnav: '',
      ltpaToken: null,
      name: '未登录',
      showContent: true,
      historyEchart: '',
      echartContrast: '',

      showSubnav: false,

      loading1: null,
      loading2: null,

      firstPage: '',
      // 宁夏大音新加入
      cookerObj: {}
    }
  },
  watch: {
    secondSubnav() {
      this.textSubnav = ''
      this.showSecondeNav = true
      this.showThreeNav = true
    },
    textSubnav(val, oldval) {
      if (val == '') {
        this.threeSubnav = this.secondSubnav[0].menuList
        this.showSecondeNav = true
        this.showThreeNav = true
      } else {
        this.searchSubnav()
      }
    }
  },
  async created() {
    if (window.location.hash == '#/index' || window.location.hash == '#/') {
      this.$router.push({
        name: 'satisfaction'
      })
    }
    // 校验token
    await checkToken().then(res => {
      if (res.code == 200) {
        localStorage.setItem('userInfo', JSON.stringify(res.data))
        localStorage.setItem('loginName', res.data.loginName)
        this.name = res?.data?.name
      }
    })
    this.menuList()
    this.getIdentyType()
  },
  computed:{
    filteredSubNav() {
      return this.subNav.map((item, index) => ({
        ...item,
        isSpecial: index === this.subNav.length - 1 && item.menuId === '10006'
      }));
    },
    filteredMenuSubNav() {
      return this.subNav.map((item, index) => ({
        ...item,
        isSpecial: index != this.subNav.length - 1|| item.menuId=='10001'||item.menuId=='10002'||item.menuId=='10003'||item.menuId=='10005'||item.menuId=='510103'
      }));
    },
  },
  mounted() {
    eventBus.on('startJKauth',(v)=>{
      this.jkDialogVis = true;
    })
  },
  methods: {
    changeScrollTop(val) {
      this.$refs.scrollbar.wrap.scrollTop = val
    },

    // 跳转待办事项详情
    goTodetail(item) {
      //
      getForjump(encodeURIComponent(`/workflow?id=${item.orderId}`)).then((res) => {
        if (res.code == 200) {
          window.open(res.data)
        }
      })
    },
    // 打开督办系统
    showSupervise() {
      this.supervise = !this.supervise
      this.showSubnav = false
    },
    // 搜索路由
    searchSubnav() {
      if (this.textSubnav) {
        const threeNav = []
        this.secondSubnav.forEach((item) => {
          item.menuList.forEach((value) => {
            if (value.menuName.indexOf(this.textSubnav) != -1) {
              threeNav.push(value)
            }
          })
        })
        if (threeNav.length === 0) {
          this.threeMenu = '暂无搜索结果'
          this.showSecondeNav = false
          this.showThreeNav = false
        } else {
          this.threeSubnav = threeNav
          this.threeMenu = '搜索结果'
          this.showSecondeNav = false
          this.showThreeNav = true
        }
      }
    },

    encode() {
      const name = localStorage.getItem('username')
      const token = aesEncryptEBC(
        'asiainfogriduser',
        localStorage.getItem('loginName')
      )

      const arr = []
      for (var i = 0; i < name.length; i++) {
        var code = '\\u' + name.charCodeAt(i).toString(16)
        arr.push(code)
      }

      const codeName = arr.join('')
      const aesName = aesEncryptEBC('asiainfogriduser', codeName)

      return {
        token: token,
        name: aesName
      }
    },

    // 模块记录
    modelViewRecord(value){
       try {
           recordAdd({
          eventType: 'PAGE_VIEW',
          pageName: value.menuName,
          routeId: value.menuId
        })
      recordAddTo4a({
        optTime: timeTool.formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        pracct: sessionStorage.getItem('mainId'), // 从url中取mainId
        slacct: sessionStorage.getItem('loginName'), // loginName
        result: 0,
        despCmd: `${value.menuName}`,
        ticket: sessionStorage.getItem('Authorization'), // 取token
        subAcctIsSensitive: 0, // 是否涉及敏感
        operateContent: `查看${value.menuName}`,
        operateContentIsSensitive: 0,
        isBlur: 0, // 是否模糊化
        isBatch: 0,
        involvingPhone: '',
        operationType: 4

      })
    } catch (error) {

    }

    },

    // 三级路由跳转
    goThreeSubnav(value) {

// 记录访问模块*********




     // 记录访问模块*********

      if (value.menuName == '互动调研') {
             let auth =   sessionStorage.getItem('Authorization') || sessionStorage.Authorization
             let loginName =  sessionStorage.getItem('loginName') || sessionStorage.loginName

              this.modelViewRecord(value)
             this.$nextTick(()=>{
              window.open(`${value.menuUrl}?token=${auth}&userAccount=${loginName}`);
             })

             return;

        }

      if (value.menuName == '工单管理') {
        this.loading2 = this.$loading({
          lock: true,
          text: '正在跳转工单管理',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })



      this.modelViewRecord(value)
        getForjump().then(res => {
          if (res.code == 200) {
            this.loading2.close()
            window.open(res.data)
          }
        })
        return
      }

      // if (value.delFlag == 0 ) {

      this.showSubnav = false

      if (value.isExternal == '0') {
        sessionStorage.setItem('menuName', value.menuName)
        sessionStorage.setItem('menuUrl', value.menuUrl)
        if (value.menuUrl.indexOf('complain') != -1) {
          this.$router.push(value.menuUrl)
        } else {
          let comName = value.componentName
          if(!comName && value && value.path){
            comName = value.path.replace('/','')
          }
          this.$router.push({
            name: comName
          })
        }
      } else {
        if (value.menuCode == 'pcxt') {
          var obj = this.encode()
          window.open(
            `${value.menuUrl}?token=${obj.token}&name=${obj.name}&module=acquisition`
          )
        } else {
          window.open(value.menuUrl)
        }
      }
      // 不需要
      // this.logRecorder(value, 3)
      // }
    },

    logRecorder(item, level) {
      postLog({
        menuName: item.menuName,
        menuUrl: item.menuUrl,
        menuCode: item.menuCode,
        level: level,
        id: item.id
      }).then((res) => {
        console.log(res)
      })
    },

    // 一级路由跳转网页
    jumpPage(item) {
      console.log('page', item)
      // if (item.delFlag == 0) {
      if (item.isExternal == '0') {
        this.$router.push({
          name: item.componentName
        })
      } else {
        if (item.menuId == '10006') {
          this.getDyToken(item.menuUrl)
        }  else{
          if (item.menuUrl) {
            window.open(item.menuUrl)
          }
        }
      }
      this.logRecorder(item, 1)
      // } else {
      //   this.msgWarning('该页面暂时无法预览')
      // }
    },

    getDyToken(menuUrl) {
      const userInfo = JSON.parse(localStorage.getItem('userInfo'))

      if (userInfo) {
        getGroupToken({
          loginName: userInfo.loginName
        })
          .then((response) => {
            try {
              window.open(
                `${menuUrl}?token=${response.data.token}&appAcctId=${response.data.appAcctId}&flag=${response.data.flag}&orgsys=${response.data.orgsys}`
              )
            } catch {
              window.open(`${menuUrl}`)
            }
          })
          .catch((err) => {
            window.open(`${menuUrl}`)
          })
      } else {
        window.open(`${menuUrl}`)
      }
    },

    // 三级菜单高亮显示
    setClass(keys) {
      this.currents = keys
    },
    // 二级菜单高亮显示
    addClass(index, item) {
      this.current = index
      this.currents = 0
      this.threeSubnav = item.menuList
      this.threeMenu = item.menuName
    },
    // 打开菜单
    Subnav(item, key) {
      if (item.menuList.length !== 0) {
        this.secondSubnav = item.menuList
        this.threeSubnav = this.secondSubnav[0].menuList
        this.threeMenu = this.secondSubnav[0].menuName
        this.current = 0
        this.currents = 0
        this.showSubnav = true
        this.supervise = false
        this.cut = key
      } else {
        this.showSubnav = false
      }
    },
    // 关闭菜单
    closeSubnav() {
      this.showSubnav = false
    },
    // 打通用户体系后重新替换获取菜单的接口 更换字段名字
    handleMenuProp(data) {
      data.forEach(i => {
        i.isExternal = i.isFrame
        i.menuList = i.children
        i.menuUrl = i.path
        i.detail = i.remark
        i.componentName = i.component
        if (i.children && i.children.length) {
          this.handleMenuProp(i.children)
        }
      })
      return data
    },
    // 获取菜单
    async menuList() {
      const name = localStorage.getItem('loginName')
      // const { data } = await getMenu(name)
      // 更换接口处理数据
      const { data } = await getFrontMenu()
      if (Array.isArray(data)) {
        this.handleMenuProp(data)
      } else {
        return
      }
      console.log('data:', data)
      // data = data.splice(0, 1)
      data.forEach((item) => {
        if (item.menuList && item.menuList.length != 0) {
          this.dealMenuList(item)
          this.getFirstMenue(item)
        }
      })
      data.forEach((item) => {
        if (item.menuList.length === 0) {
          item.showImg = false
        } else {
          item.showImg = true
        }
      })
      this.subNav = data
      this.getFirstMenue(this.subNav[0])
      /* this.firstPage = this.subNav[0].menuList[0].menuList[0].menuCode
            console.warn('-------this.firstPage',this.subNav[0]);*/
      // getMenu(name).then(response=>{
      // response.data.forEach(item=>{
      //     if(item.menuList && item.menuList.length!=0){
      //         this.dealMenuList(item);
      //     }
      // })
      // response.data.forEach(item=>{
      //     if(item.menuList.length === 0){
      //         item.showImg = false;
      //     }else {
      //          item.showImg =true;
      //     }
      // })
      // this.subNav = response.data;
      // this.firstPage = this.subNav[0].menuList[0].menuList[0].menuCode
      // })
    },

    getFirstMenue(menu) {
      if (menu.menuUrl && menu.isExternal == '0') {
        this.firstPage = menu.menuUrl
        this.gotoHome()
      } else {
        if (menu.menuList && menu.menuList.length != 0) {
          this.getFirstMenue(menu.menuList[0])
        }
      }
    },

    dealMenuList(menu) {
      const subMenuList = menu.menuList
      const date = new Date()
      let month = date.getMonth()
      if (month == 0) {
        month = 12
      }

      if (month < 10) {
        month = '0' + month
      }

      subMenuList.forEach((item) => {
        if (item.menuUrl == '/home') {
          item.menuUrl =
            '/home/<USER>/' +
            (month == 12 ? date.getFullYear() - 1 : date.getFullYear()) +
            month
        }

        if (item.menuUrl == '/complain') {
          // item.menuUrl='/complain/0/'+ date.getFullYear() + month+'/1/1'
          item.menuUrl = `/complain/0/${
            month == 12 ? date.getFullYear() - 1 : date.getFullYear()
          }${month}/1/1`
        }

        if (item.menuList && item.menuList.length != 0) {
          this.dealMenuList(item)
        }

        if (item.menuCode == 'pcxt' && item.menuUrl) {
          localStorage.setItem('externalHost', item.menuUrl)
          console.log(
            '==================================',
            localStorage.getItem('externalHost')
          )
        }
      })
    },

    // 获取cookies值
    selectCooking() {
      if (document.cookie == '') {
        this.loading1?.close()
        /* this.$router.push({
                    name:"blankPage"
                })*/
      } else {
        const cooker = document.cookie.split('; ').reduce((total, prev) => {
          const [key, value] = prev.split('=')
          return { ...total, [key]: value }
        }, {})

        this.cookerObj = cooker

        if (cooker.LtpaToken) {
          this.ltpaToken = cooker.LtpaToken
          this.access_token = cooker.kmportaltoken

          this.getList()
        } else {
          this.loading1?.close()
          /* this.$router.push({
                        name:"blankPage"
                    })*/
        }
      }
    },

    gotoHome() {
      // return
      // let date = new Date();
      // let month = date.getMonth();

      // if(month==0){
      //     month = 12;
      // }

      // if(month<10){
      //     month = '0'+month;
      // }

      // let year = month==12?date.getFullYear()-1:date.getFullYear();

      // this.$router.replace({
      //     name: 'home',
      //     params: {
      //       id: 0,
      //       date: year+''+month
      //     }
      // });
      // console.log('5555555');
      // this.$router.push(this.firstPage)
      // this.$router.push('/MobileNetwork')// WorkflowPanel
      // return false;
      if (sessionStorage.getItem('menuUrl')) {
        console.warn(
          '----------this.firstPage-2',
          sessionStorage.getItem('menuUrl')

        )
        this.$router.push(sessionStorage.getItem('menuUrl'))
      } else {
        console.warn('----------this.firstPage-3', this.firstPage)
        this.$router.push(this.firstPage)
      }

      // this.$router.replace({
      //     name: this.firstPage,
      // });
    },
    // 上下页数
    change(pageNum, e) {
      if (e.target.className.indexOf('nopage') == -1) {
        this.pageNum = parseInt(this.pageNum) + parseInt(pageNum)
        this.getIdentyType(this.pageNum)
      }
    },
    // 获取待办事件列表
    getIdentyType(pageNum = 1, pageSize = 4) {
      getIdenty({
        pageNum: pageNum.toString(),
        pageSize: pageSize.toString()
      }).then((response) => {
        if (response.code == 200) {
          this.IdentyTypeList = response.data
          this.total = response.total
          this.pageCount = Math.ceil(response.total / pageSize)
        } else {
          this.total = 0
        }
      })
    },
    // 获取用户信息
    getList() {
      // 发布时要注释掉！！！！！

      /* this.loading1?.close();
            this.menuList();
            this.getIdentyType();
            if(window.location.hash.indexOf('home')!=-1 || window.location.hash.indexOf('blank')!=-1|| window.location.hash.indexOf('index')!=-1|| window.location.hash=="#/"){
                this.gotoHome();
            }*/
      getName({
        ltpaToken: this.ltpaToken,
        // 宁夏大音新加入
        loginName: this.cookerObj.loginName,
        ticket: this.cookerObj.token,
        main_ID: this.cookerObj.MAIN_ID,
        op: this.cookerObj.op
      })
        .then((response) => {
          if (response.code === 200) {
            this.showContent = true
            this.name = response.data.name
            this.loginName = response.data.loginName
            this.token = response.data.token
            localStorage.setItem('userInfo', JSON.stringify(response.data))
            localStorage.setItem('username', this.name)
            localStorage.setItem('loginName', this.loginName)
            sessionStorage.setItem('Authorization', this.token)
            setToken(response.data.token)
            this.$store.commit('SET_TOKEN', response.data.token)
            // commit('SET_TOKEN', res.token)

            this.selectHeader = response.data.roleId
            this.menuList()
            this.getIdentyType()
            /* if(window.location.hash.indexOf('home')!=-1 || window.location.hash.indexOf('blank')!=-1|| window.location.hash.indexOf('index')!=-1|| window.location.hash=="#/"){
                        // this.gotoHome();
                        // console.log('hahahahah');
                        // console.log('0000',_this.firstPage,'1111');
                        this.gotoHome();
                        // this.$router.push('teleservice')
                    }*/
          } else {
            this.name = '未登录'
          }
          this.loading1?.close()
        })
        .catch((err) => {
          this.name = '未登录'
          this.loading1?.close()
        })
    },

    // 以下为迁移函数
    handelRoleName(params) {
      console.log(params)
      this.roleName = params.name || '暂无'
    },

    // 跳转至三级第一个页面
    toggleNav(item) {
      this.logRecorder(item, 2)
      this.goThreeSubnav(item.menuList[0])
    }
  }
}
</script>

<style lang="less" scoped>
.nothing {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  .nothing-img {
    width: 280px;
    height: 249px;
    background: url(./assets/img/empty.png);
  }
}
.nopage {
  cursor: not-allowed;
}
.protal {
  display: flex;
  flex-direction: column;
}
.supervise_masklayer {
  position: fixed;
  top: 56px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 100;
  .supervise {
    position: relative;
    .supervise_event {
      height: 489px;
      width: 385px;
      background-color: #ffffff;
      border: 1px solid #e5e5e5;
      box-shadow: 0 2px 4px 0 rgba(156, 156, 156, 0.4);
      border-radius: 2px;
      position: absolute;
      top: 0px;
      right: 50px;
      z-index: 200;
      .backlog {
        width: 100%;
        height: 43px;
        line-height: 43px;
        border-bottom: 1px solid #dadada;
        p {
          margin-left: 31px;
          //width: 83px;
          font-family: SourceHanSansSC-Medium;
          font-size: 18px;
          color: #262626;
          // border-bottom: 3px solid #F09C33;
          &::before {
            width: 40px;
            height: 3px;
            background-color: #f09c33;
            content: "";
            position: absolute;
            left: 40px;
            top: 40px;
            bottom: auto;
            right: auto;
          }
        }
      }
      ul {
        li {
          height: 100px;
          display: flex;
          margin: 0 20px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          .image {
            display: flex;
            justify-content: center;
            width: 100px;
            height: 100%;
            align-items: center;
            align-content: center;
            p {
              text-align: center;
              line-height: 44px;
              width: 44px;
              height: 44px;
              border-radius: 50%;
              background: #f09c33;
              font-family: SourceHanSansSC-Medium;
              font-size: 20px;
              color: #ffffff;
            }
          }
          .matter {
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-content: center;
            span {
              width: 210px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        // &>li:last-child{
        //     border-bottom: none;
        // }
      }
      .paging {
        display: flex;
        height: 87px;
        p {
          width: 50%;
          height: 45px;
          line-height: 45px;
          text-align: center;
          border-top: 1px solid #f0f0f0;
          font-family: SourceHanSansSC-Regular;
          font-size: 16px;
          color: #262626;
          // border-radius: 3px;
        }
        & > p:last-child {
          border-left: 1px solid #dadada;
        }
        .okpage {
          cursor: pointer;
        }
      }
    }
  }
}

.threeDetail {
  display: block;
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: #8c8c8c;
}
.maskLayer {
  position: fixed;
  top: 56px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 10000;
}

.nav-box {
  width: 100%;
  background: #eee;
  height: 365px;
  z-index: 9999;
  display: flex;
  border-top: 1px solid #ddd;
  box-sizing: border-box;
}
.active {
  span{
    border-bottom: 3px solid #ff9900;

  }
  // &::after {
  //   content: " ";
  //   width: 48px !important;
  //   height: 3px;
  //   display: block;
  //   background: #ff9900;
  //   font-family: SourceHanSansSC-Medium !important;
  //   color: #262626;
  //   font-weight: bold;
  // }
}
.actives {
  color: #ff9900 !important;
}
.closeX {
  position: absolute;
  top: 45px;
  right: 48px;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
  }
}
.levelThreeTitle {
  // width: auto;
  width:100%;
  overflow: hidden;
  margin-top: 33px;
  height: 100%;
  display: flex;
  align-content: flex-start;
  &.moreItem{
    flex-wrap:wrap;
    li{
       width: 14%;
    }
  }

  li {
    // flex:1;
    height: 100%;
    // width: 14%;
    // margin-right: 120px;
    margin-right:45px;
    font-family: SourceHanSansSC-Medium;
    font-size: 14px;
    color: #4d4d4d;
    cursor: pointer;
    transition: all 0.3s linear;
  }
  & > li:hover {
    font-family: SourceHanSansSC-Medium;
    font-size: 14px;
    color: #ff9900;
    &::after {
      content: "";
      width: 100%;
      color: #ff9900;
    }
  }
}
.levelThree {
  font-family: SourceHanSansSC-Regular;
  font-size: 24px;
  color: #595959;
  border-bottom: 1px solid #252631;
  padding-bottom: 18px;
}
.SubTitle {
  margin-top: 26px;
  width: auto;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  li {
    display: inline-block;
    width: auto;
    max-width: 200px;
    font-family: SourceHanSansSC-Regular;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.7);
    cursor: pointer;
    line-height: 40px;
    transition: all 0.3s linear;
    position: relative;
    transition: all 0.3s linear;
    span:hover{
      border-bottom: 3px solid #ff9900;
    }
    // &::after {
    //   content: "";
    //   height: 3px;
    //   width: 0px;
    //   background: #ff9900;
    //   position: absolute;
    //   bottom: 0px;
    //   left: 0px;
    //   transition: all 0.3s linear;
    // }
  }

  & > li:hover {
    font-family: SourceHanSansSC-Medium;
    font-size: 16px;
    color: #262626;
    // -border-bottom: 3px solid #ff9900;
    // &::after {
    //   width: 48px;
    // }
  }
}
.protal {
  width: 100%;
  height: 100%;
}
.protal_show {
  width: 1342px;
  height: 607px;
  margin-left: 56px;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  .showError {
    width: 599.9px;
    height: 271px;
  }
  .errotText {
    font-family: SourceHanSansSC-Regular;
    font-size: 28px;
    color: #4d4d4d;
    margin-top: 54.3px;
  }
}
.bj_protal {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .protal_header {
    width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    -position: fixed;
    top: 0px;
    background: #fff;
    z-index: 999;
    box-shadow: 0 3px 3px rgba(0, 0, 0, 0.03);

    .logo {
      width: 187px;
      height: 33px;
    }

    .header-left-wrap {
      display: flex;
      align-items: center;
      height: 100%;
    }

    img {
      transition: all 0.3 linear;
    }

    ul {
      width: auto;
      overflow: hidden;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 50px;

      li {
        text-align: center;
        height: 100%;
        width: auto;
        min-width: 80px;
        margin: 0 12px;
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #4d4d4d;
        cursor: pointer;
        position: relative;
        line-height: 56px;
        transition: all 0.3s linear;

        &::after {
          content: " ";
          display: block;
          width: 100%;
          transition: all 0.3s linear;
        }
      }

      & > li:hover {
        font-family: SourceHanSansSC-Medium;
        font-size: 14px;
        color: #262626;
        -border-bottom: 3px solid #ff9900;

        &::after {
          content: " ";
          width: 100%;
          height: 3px;
          background: #ff9900;
          position: absolute;
          bottom: 0px;
        }
      }
    }

    .header-right-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-left: 20px;
    }

    .headPortraits {
      width: 24px;
      height: 24px;
      border-radius: 12px;
      overflow: hidden;
    }

    .monad {
      margin: 0 20px 0 13px;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      line-height: 56px;
      color: #4d4d4d;
      text-align: center;
    }

    .people {
      font-family: SourceHanSansSC-Regular;
      font-size: 14px;
      color: #4d4d4d;
      margin: 0 9.7px 0 5px;
    }

    .ico {
      position: relative;
      margin-right: 10px;
      cursor: pointer;
      & > .smallImg {
        vertical-align: middle;
        font-size: 0;
      }

      & > .numbers {
        padding: 4px 6px;
        position: absolute;
        font-family: SourceHanSansSC-Regular;
        font-size: 10px;
        color: #ffffff;
        background: #e64c45;
        border-radius: 100%;
        line-height: 12px;
        text-align: center;
        left: 10px;
        top: -3px;
      }
    }

    .sImg {
      vertical-align: middle;
      font-size: 0;
      // transition: all 0.3 linear;
    }
    .sImgs {
      vertical-align: middle;
      font-size: 0;
      transform: rotate(180deg);
      //  transition: all 0.3 linear;
    }
  }

  .protal_content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 2;

    .banner {
      height: 240px;
      background: #e4e4e4;

      p {
        margin-left: 144px;
        padding-top: 78px;
        font-family: SourceHanSansSC-Medium;
        font-size: 22px;
        color: #6e6e6e;
        letter-spacing: -0.43px;
      }

      button {
        margin-left: 144px;
        margin-top: 24px;
        background: #f09c32;
        border-radius: 4px;
        width: 117px;
        height: 32px;
        border: none;
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #ffffff;
        letter-spacing: -0.27px;
        cursor: pointer;
      }
    }

    .chart {
      height: 1025px;
      background-color: #f5f5f5;
      -padding-left: 56px;

      .chart_title {
        font-family: SourceHanSansSC-Medium;
        font-size: 18px;
        color: #4e4e4e;
        letter-spacing: -0.35px;
        padding-top: 18px;
        margin-bottom: 12px;
      }

      .Satisfaction {
        width: 1342px;
        height: 98px;
        display: flex;
        justify-content: space-between;

        .present {
          width: 321px;
          height: 98px;
          border-radius: 4px;
          display: flex;
          background-color: #ffffff;
          .image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #ffffff;
            margin-left: 24px;
            margin-top: 19px;
            overflow: hidden;
            img {
              width: 60px;
              height: 60px;
            }
          }

          .mid {
            margin: 24px 52px 0 20px;
            .nub {
              font-family: Helvetica;
              font-size: 26px;
              color: #252631;
            }
            .faction {
              font-family: SourceHanSansSC-Normal;
              font-size: 14px;
              color: #8c8c8c;
              line-height: 21px;
              width: 70px;
            }
            .factions {
              width: 98px;
              font-family: SourceHanSansSC-Normal;
              font-size: 14px;
              color: #8c8c8c;
            }
          }

          .rt {
            position: relative;
            font-family: SourceHanSansSC-Regular;
            font-size: 14px;
            color: #339933;
            .ranking {
              position: absolute;
              top: 32px;
            }
            .percent {
              margin-top: 54px;
              color: #339933;
            }
          }
        }
      }

      .history {
        margin-top: 25px;
        width: 1342px;
        height: 340px;
        background: #ffffff;
        border: 1px solid #e1e6ea;
        position: relative;
        .time {
          position: absolute;
          right: 37px;
          top: 54px;
          width: 160px;
          height: 30px;
          background: #ffffff;
          border: 1px solid #ced1d4;
          border-radius: 2px;
          display: flex;
          align-items: center;

          .dateTime {
            font-family: SourceHanSansSC-Regular;
            font-size: 14px;
            color: #45494d;
            text-align: center;
            line-height: 30px;
            margin-left: 12px;
          }
          .riqi {
            width: 16px;
            height: 16px;
            margin-left: 76px;
          }
        }
        .history_title {
          height: 38px;
          padding-left: 28px;
          background: #f6fafd;
          box-shadow: 0 1px 0 0 #e0e5e8;
          font-family: SourceHanSansSC-Medium;
          font-size: 16px;
          color: #575b61;
          letter-spacing: -0.18px;
          line-height: 38px;
        }

        .history_echart {
          height: 302px;
        }
      }
    }
  }

  .protal_footer {
    width: 100%;
    padding: 0 24px;
    height: 172px;
    background: #05223e;
    border: 1px solid #05223e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 172px;
    .bianzu2 {
      width: 110px;
      height: 90px;
    }

    .wire {
      width: 1px;
      height: 92px;
      margin: 0 30px;
      background-color: #768799;
    }

    .indicate {
      width: 59.8%;

      & > div {
        display: flex;
        justify-content: space-between;
      }

      .firseFloor {
        width: 456px;
      }

      .secondFloor {
        width: 215px;
        margin: 12px 0 12px 0;
      }

      .thirsFloor {
        width: 291px;
      }

      p {
        justify-content: space-between;
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #bfbfbf;
      }
    }

    .phone {
      & > p {
        font-family: SourceHanSansSC-Medium;
        font-size: 14px;
        color: #bfbfbf;
      }

      & > p:nth-child(1) {
        font-family: SourceHanSansSC-Medium;
        font-size: 16px;
        color: #ffffff;
      }

      & > p:nth-child(2) {
        margin: 6px 0 8px 0;
      }
    }
  }
}
:deep(.menu_input > .el-input__inner ){
  border: 0;
  border-bottom: 1px solid #d8d8d8;
  background: #eee;
  border-radius: 0;
}

* {
  margin: 0;
  padding: 0;
}
/* 去掉a标签的下划线 */
a {
  text-decoration: none;
}
/* 去掉列表的标记 */
ul,
ol {
  list-style: none;
}
/* 你有可能把这个简单的css样式重置用于移动端开发 */
html,
body {
  width: 100%;
}
/* 清除浮动 */
.clearfix:after {
  content: "\200B";
  display: block;
  height: 0;
  clear: both;
}
/*IE/7/6*/
.clearfix {
  *zoom: 1;
}
</style>
<style type="text/css">
.el-scrollbar__wrap {
  overflow-x: hidden;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.el-scrollbar__view {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
