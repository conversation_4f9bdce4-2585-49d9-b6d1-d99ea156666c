<template>
  <div class="progressTop">
    <div class="dateTop">
      <DateChoose
        ref="date"
        :default-date="defaultDate"
        :default-date-type-list="defaultDateTypeListArr"
        @dateType="dateType"
        @dateChange="dateChange"
      />
    </div>

    <div ref="leftChart" class="progress-left">
      <div class="progress-title">{{ progressTitle }}</div>
      <div v-permission="mapPermission.mapLevel == 1" style="width: 100%; min-height: 300px; position: relative">
        <div class="header">
          <div class="body-name">套餐名称</div>
          <div class="hader-progress" />
          <div class="hader-cell">投诉量</div>
          <div class="hader-cell">占比</div>
          <div class="hader-cell">投诉量环比</div>
        </div>
        <div v-for="(elment, index) in productData" :key="index" class="body">
          <el-tooltip :content="elment.rankname" placement="top">
            <div
              class="body-name"
              style="line-height: 30px; cursor:pointer;color:#595959; text-align:left; font-size:12px;"
            >{{ elment.rankname }}</div>
          </el-tooltip>

          <div class="body-progress">
            <el-progress
              :percentage="parseFloat(elment.partrate * 100)"
              :show-text="false"
              stroke-linecap="square"
              :color="customColor"
            />
          </div>
          <div class="body-cell">{{ elment.score }}</div>
          <div
            class="body-cell"
          >{{ elment.partrate || elment.partrate === 0 ?parseFloat(elment.partrate * 100).toFixed(2)+'%':'-' }}</div>
          <div class="body-cell">
            {{
              elment.momrate || elment.momrate === 0
                ? parseFloat(elment.momrate * 100).toFixed(2) + "%"
                : "-"
            }}
          </div>
        </div>
        <div
          :style="{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            display: productData.length ? 'none' : 'block',
          }"
        >
          <Blank2 />
        </div>
      </div>
    </div>
    <div ref="rightChart" class="progress-right">
      <div class="progress-title">{{ progressTitle2 }}</div>
      <div v-permission="mapPermission.mapLevel == 1" style="width: 100%; min-height: 300px; position: relative">
        <div class="header">
          <div class="body-name">渠道名称</div>
          <div class="hader-progress" />
          <div class="hader-cell">投诉量</div>
          <div class="hader-cell">占比</div>
          <div class="hader-cell">投诉量环比</div>
        </div>
        <div v-for="(elment, index) in channelData" :key="index" class="body">
          <el-tooltip :content="elment.rankname" placement="top">
            <div
              class="body-name"
              style="cursor:pointer; line-height: 30px; color:#595959; text-align:left; font-size:12px;"
            >{{ elment.rankname }}</div>
          </el-tooltip>

          <div class="body-progress">
            <el-progress
              :percentage="parseFloat(elment.partrate * 100)"
              :show-text="false"
              stroke-linecap="square"
              :color="customColor2"
            />
          </div>
          <div class="body-cell">{{ elment.score }}</div>
          <div
            class="body-cell"
          >{{ elment.partrate || elment.partrate === 0 ?parseFloat(elment.partrate * 100).toFixed(2)+'%':'-' }}</div>
          <div class="body-cell">
            {{
              elment.momrate || elment.momrate === 0
                ? parseFloat(elment.momrate * 100).toFixed(2) + "%"
                : "-"
            }}
          </div>
        </div>
        <div
          :style="{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            display: channelData.length ? 'none' : 'block',
          }"
        >
          <Blank2 />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import DateChoose from '../../../components/common/DateChooseC.vue'
import { getChannelTOP, getProductTOP } from '@/api/complaint-analysis/api'
import * as echarts from 'echarts'
export default {
  name: 'ProgressTop',
  components: { DateChoose },
  filters: {
    // 判断超出5个字显示省略号
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 8) {
        return value.slice(0, 7) + '...'
      }
      return value
    }
  },
  inject: ['mapPermission'],
  data() {
    return {
      defaultDateTypeListArr: ['日', '年', '月', '季度', '日累计'],
      defaultDate: '202205',
      progressTitle: 'TOP10套餐投诉',
      progressTitle2: 'TOP10渠道投诉',
      channelData: [],
      productData: [],
      customColor: '#409EFF',
      customColor2: '#E6A23C',
      timeType: '3',
      targetId: '',
      leftChart: null,
      rightChart: null
    }
  },
  mounted() {},
  created() {
    // this.inAll();
  },
  methods: {
    dateType(val) {
      if (val == '日') {
        this.timeType = '1'
        this.defaultDate = ''
      } else if (val == '年') {
        this.timeType = '2'
        this.defaultDate = ''
      } else if (val == '月') {
        this.timeType = '3'
        this.defaultDate = ''
      } else if (val == '季度') {
        this.timeType = '4'
        this.defaultDate = ''
      } else {
        this.timeType = '5'
        this.defaultDate = ''
      }
    },
    dateChange(val) {
      if (val) {
        if (this.timeType == '4' && val.search('N') != 0) {
          this.defaultDate = val.slice(0, 4) + '-' + 'Q' + val.slice(5, 6)
        } else if (this.timeType == '5' && val.length > 0) {
          this.defaultDate = ''
          this.startTime = val[0]
          this.endTime = val[1]
        } else {
          this.defaultDate = val
        }
        console.log(this.defaultDate)
        this.inAll(this.targetId)
      }
    },
    inAll(item) {
      this.targetId = item
      if (this.timeType == 3 && this.defaultDate.length == 6) {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const datas = {
        opTime: this.defaultDate,
        statType: this.timeType,
        targetId: this.targetId,
        startTime: this.startTime || '2022-05-24',
        endTime: this.endTime || '2022-05-24'
      }
      getChannelTOP(datas).then(res => {
        if (res.code == 200) {
          this.channelData = res.data.data
        }
      })
      getProductTOP(datas).then(res => {
        if (res.code == 200) {
          this.productData = res.data.data
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.content {
  .progressTop {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    padding: 30px 0;
    justify-content: space-around;
  }
  .progress-title {
    font-size: 18px;
    font-weight: bolder;
    padding: 15px 0;
  }
  .progress-left,
  .progress-right {
    width: 46%;
    .header {
      color: rgb(140, 140, 140);
      font-size: 12px;
      padding: 10px 0;
    }
    .header,
    .body {
      width: 100%;
      display: flex;
      .hader-cell,
      .body-cell {
        width: 15%;
        text-align: center;
        font-size: 12px;
      }
      .body-name {
        width: 160px;
        padding-right: 20px;
        box-sizing: border-box;
        height: 30px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .hader-progress,
    .body-progress {
      width: 40%;
      margin-top: 10px;
    }
    :deep( .el-progress-bar .el-progress-bar_inner ) {
      border-radius: inherit;
    }
    :deep(.el-progress-bar .el-progress-bar__outer ) {
      height: 10px !important;
      border-radius: inherit;
    }
    .body {
      font-size: 16px;
      .body-cell {
        font-size: 12px;
      }
      .body-name .body-progress {
        padding: 5px 0;
      }
    }
  }
}
.dateTop {
  position: absolute;
  left: 74%;
  width: 400px;
}
</style>
