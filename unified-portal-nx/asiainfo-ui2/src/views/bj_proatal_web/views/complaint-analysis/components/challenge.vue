<template>
  <div class="challenge">
    <div class="setMeal">
      <div class="setMeal-left">
        <div style="width: 100%; min-height: 300px; position: relative">
          <img
            v-if="businessLevel>0"
            class="goBack"
            src="../../../assets/img/expCourse/return.png"
            @click="goBack"
          >
          <div ref="chart" class="leftChart" />
          <div
            :style="{
              position: 'absolute',
              left: 0,
              top: 0,
              width: '100%',
              height: '100%',
              display:
                lineList1.length || columnarList1.length ? 'none' : 'block',
            }"
          >
            <Blank2 />
          </div>
        </div>
      </div>
      <div ref="leftChart" class="setMeal-right">
        <div class="progress-title">{{ progressTitle }}</div>
        <div v-permission="mapPermission.mapLevel==1" style="width: 100%; min-height: 300px; position: relative">
          <div class="header">
            <div class="body-name">套餐名称</div>
            <div class="hader-progress" />
            <div class="hader-cell">投诉量</div>
            <div class="hader-cell">占比</div>
            <div class="hader-cell">投诉量环比</div>
          </div>
          <div v-for="(elment, index) in channelData" :key="index" class="body">
            <el-tooltip :content="elment.rankname" placement="top">
              <div
                class="body-name"
                style="line-height: 30px; color:#595959; text-align:left; font-size:12px;cursor:pointer;"
              >{{ elment.rankname }}</div>
            </el-tooltip>

            <div class="body-progress">
              <el-progress
                :percentage="Math.ceil(elment.partrate * 100)"
                :show-text="false"
                stroke-linecap="square"
                :color="customColor"
              />
            </div>
            <div class="body-cell">{{ elment.score }}</div>
            <div
              class="body-cell"
            >{{ elment.partrate||elment.partrate === 0? parseFloat(elment.partrate * 100).toFixed(2):'-' }}%</div>
            <div class="body-cell">
              {{
                elment.momrate || elment.momrate === 0
                  ? parseFloat(elment.momrate * 100).toFixed(2) + "%"
                  : "-"
              }}
            </div>
          </div>
          <div
            :style="{
              position: 'absolute',
              left: 0,
              top: 0,
              width: '100%',
              height: '100%',
              display: channelData.length ? 'none' : 'block',
            }"
          >
            <Blank2 />
          </div>
        </div>
      </div>
    </div>
    <div style="margin-top:30px;">
      <CustomerSatisfaction ref="CustomerSatisfaction" />
    </div>
  </div>
</template>
<script>
import {
  getReQuarter,
  getBusiness,
  getProductTOP,
  getQuarter
} from '@/api/complaint-analysis/api'
import CustomerSatisfaction from './customerSatisfaction.vue'
import upicon from '../../../assets/img/up-icon.png'
import downicon from '../../../assets/img/down-icon.png'
import { number } from 'echarts'
export default {
  name: 'Challenge',
  components: { CustomerSatisfaction },

  filters: {
    // 判断超出5个字显示省略号
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 8) {
        return value.slice(0, 7) + '...'
      }
      return value
    }
  },
  inject: ['mapPermission'],
  props: {},

  data() {
    return {
      timeType: '3',
      leftCharts: null,
      startTime: '',
      businessLevel: '',
      dName4: '',
      dName3: '',
      dName2: '',
      dName1: '',
      dName: '',
      opTime: '',
      businessRouteL: '',
      endTime: '',
      date: '2022-05',
      progressTitle: 'TOP10套餐投诉',
      channelData: [],
      customColor: '#409EFF',
      lineList1: [],
      columnarList1: []
    }
  },
  mounted() {},
  methods: {
    goBack() {
      this.businessLevel = (this.businessLevel - 1).toString()
      if (this.businessLevel == 1) {
        this.businessRoute = sessionStorage.getItem('businessRoute')
        this.opTime = this.date
        this.getBusinessList()
      } else if (this.businessLevel > 1) {
        this.businessRoute = this.businessRoute.substring(
          0,
          this.businessRoute.lastIndexOf('-')
        )
        this.getBusinessList()
      } else {
        this.getReQuarterData()
        // if (this.timeType != '4') {

        // } else {
        //   this.getQuarterData()
        // }
      }
    },
    async getReQuarterData(time) {
      this.businessLevel = ''
      this.businessRoute = ''
      if (time) {
        this.timeType = time.timeType
        this.date = time.date
        this.startTime = time.startTime
        this.endTime = time.endTime
      }
      if (this.timeType != '4') {
        const { data } = await getReQuarter({
          restSerialNo: 'L5zMbyxZ',
          statType: this.timeType,
          cityId: '640000',
          opTime: this.date,
          targetId: 'y0003',
          startTime: this.startTime || '2022-05-29',
          endTime: this.endTime || '2022-05-29'
        })
        if (data && data.data) {
          if (this.timeType == '1' || this.timeType == '3') {
            this.initCharts(data.data, this.timeType, 0)
          } else {
            const dataList = [].concat(data.data.first, data.data.second)
            this.initCharts(dataList, this.timeType, 0)
          }
        } else {
          this.initCharts([], this.timeType, 0)
        }
      } else {
        const { data } = await getQuarter({
          restSerialNo: 'ZhTsiZUA',
          statType: this.timeType,
          cityId: '640000',
          opTime: this.date,
          targetId: 'y0003',
          startTime: this.startTime || '2022-05-29',
          endTime: this.endTime || '2022-05-29'
        })
        // console.log(data.first);
        if (data && data.data) {
          const dataList = [].concat(
            data.data.first,
            data.data.second,
            data.data.third
          )
          this.initCharts(dataList, this.timeType, 0)
        } else {
          this.initCharts([], this.timeType, 0)
        }
      }

      getProductTOP({
        statType: this.timeType,
        opTime: this.date,
        targetId: 'y0003',
        startTime: this.startTime || this.date,
        endTime: this.endTime || this.date
      }).then(res => {
        if (res && res.data && res.data.data) {
          this.channelData = res.data.data
        } else {
          this.channelData = []
        }
      })
      this.$refs.CustomerSatisfaction.searchLabel()
    },
    initCharts(data, timeType, level) {
      var _this = this
      this.$nextTick(() => {
        let levels = parseFloat(level)
        if (
          this.leftCharts !== null &&
          this.leftCharts !== '' &&
          this.leftCharts !== undefined
        ) {
          this.leftCharts.dispose()
        }

        this.leftCharts = this.$echarts.init(this.$refs.chart)
        const toolboxs = {}
        const columnarList = []
        const lineList = []
        const timeList = []
        let linkRatio = ''
        let time = ''
        let eomstype = ''
        let line = ''
        let formatter = null
        // if (data.length < 1) {
        //   this.leftCharts.showLoading({
        //     text: "暂无数据"
        //   });
        //   return false;
        // }else{
        // this.leftCharts.hideLoading();
        // }
        if (data.length < 1 && levels == 0) {
          linkRatio = '万投比'
          formatter = ' {value}  ‱'
        }
        Object.keys(data).map(el => {
          const columnar = data[el].score || '0'
          if (levels > 0) {
            time = data[el].businessname || ''
            line = data[el].momrate || '0'
            // if (timeType == 1 || timeType == 3) {
            linkRatio = '环比'
            formatter = ' {value} %'
            lineList.push(eomstype + line)
            formatter = function(value) {
              return Number(value * 100).toFixed(0) + '%'
            }
            // } else {
            //   linkRatio = "";
            //   lineList = [];
            // }
          } else {
            linkRatio = '万投比'
            time = data[el].statdate || '-'
            formatter = ' {value}  ‱'
            eomstype = data[el].eomstype < 0 ? '-' : ''
            line = data[el].eoms && eomstype ? eomstype + data[el].eoms : '0'
            lineList.push(line)
          }
          columnarList.push(eomstype + columnar)
          timeList.push(time)
        })

        const option = {
          color: ['#7097F6', '#7CDDD3', '#FFBE4D', '#F9906F'],
          grid: {
            top: '30%',
            left: '5%',
            right: '5%',
            bottom: '0%',
            containLabel: true
          },
          toolbox: toolboxs,
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255,255,255)',
            formatter: function(val) {
              let content = ''
              content += `<div style="padding:5px;">
                                <span style="width:140px;display:inline-block;">${val[0].axisValue}</span>
                            </div>`
              val.forEach((el, index) => {
                let colorStr = el.color
                if (typeof el.color !== 'string') {
                  colorStr =
                    'linear-gradient(180deg, ' +
                    el.color.colorStops[0].color +
                    ', ' +
                    el.color.colorStops[1].color +
                    s;
                  (')')
                }
                if (index == '0') {
                  content += `<div style="padding:5px;">
                                <span style="width:140px;display:inline-block;">
                                    <i style="display: inline-block;width: 10px;height: 10px;background:${colorStr};margin-right: 5px;border-radius: 50%;}"></i><i style="margin-right: 5px;">${
  el.seriesName
}</i>
                                    ${Math.ceil(el.data)}</span>`
                } else if (el.seriesName == '万投比') {
                  content += `<div style="padding:5px;">
                                <span style="width:140px;display:inline-block;">
                                    <i style="display: inline-block;width: 10px;height: 10px;background:${colorStr};margin-right: 5px;border-radius: 50%;}"></i><i style="margin-right: 5px;">${
  el.seriesName
}</i>
                                    ${parseFloat(el.data).toFixed(2)}‱</span>`
                } else if (el.seriesName == '环比') {
                  content += `<div style="padding:5px;">
                                <span style="width:140px;display:inline-block;">
                                    <i style="display: inline-block;width: 10px;height: 10px;background:${colorStr};margin-right: 5px;border-radius: 50%;}"></i><i style="margin-right: 5px;">${
  el.seriesName
}</i>
                                    ${parseFloat(el.data * 100).toFixed(
    2
  )}%</span>`
                }

                if (el.seriesName == '万投比' && el.data > 0) {
                  content += `<img src="${upicon}" style="width: 10px;height: 10px;"/>`
                } else if (el.seriesName == '万投比' && el.data < 0) {
                  content += `<img src="${downicon}" style="width: 10px;height: 10px;"/>`
                }
                content += ` </div>`
              })
              return '<div class="showBox">' + content + '</div>'
            }
          },

          legend: {
            itemWidth: 15,
            selectedMode: false, // 是否允许点击
            itemHeight: 5,
            top: '12%',
            data: ['投诉量', linkRatio]
          },
          xAxis: [
            {
              type: 'category',
              data: timeList,
              axisPointer: {
                type: 'shadow'
              },

              axisLine: {
                lineStyle: {
                  color: '#ccc'
                }
              },

              axisTick: {
                show: false
              },

              axisLabel: {
                color: '#595959',
                fontSize: 12
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '满意度',
              nameGap: 20,
              axisLabel: {
                formatter: '{value} '
              }
            },
            {
              type: 'value',
              name: linkRatio,
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                show: false // 不显示网格线
              },
              axisLabel: {
                formatter: formatter
              }
            }
          ],
          series: [
            {
              name: '投诉量',
              type: 'bar',
              barWidth: 20,
              tooltip: {
                valueFormatter: function(value) {
                  return value
                }
              },
              data: columnarList
            },
            {
              name: linkRatio,
              type: 'line',
              yAxisIndex: 1,
              lineStyle: {
                color: 'rgba(255,153,0,1)',
                width: 1
              },
              itemStyle: {
                color: 'rgb(246, 175, 56)'
              },
              tooltip: {
                valueFormatter: function(value) {
                  return value
                }
              },
              data: lineList
            }
          ]
        }
        this.lineList1 = lineList
        this.columnarList1 = columnarList
        this.leftCharts.clear()
        this.leftCharts.setOption(option)
        this.leftCharts.getZr().on('click', data => {
          levels = levels + 1
          const pointInPixel = [data.offsetX, data.offsetY]
          if (this.leftCharts.containPixel('grid', pointInPixel)) {
            // 点击第几个柱子
            const pointInGrid = this.leftCharts.convertFromPixel(
              { seriesIndex: 0 },
              pointInPixel
            )
            // 也可以通过data.offsetY 来判断鼠标点击的位置是否是图表展示区里面的位置
            // 也可以通过name[xIndex] != undefined，name是x轴的坐标名称来判断是否还是点击的图表里面的内容
            // x轴数据的索引
            const xIndex = pointInGrid[0]
            // y轴数据的索引
            const yIndex = pointInGrid[1]

            const a = timeList[xIndex]
            if (levels < 2) {
              _this.dName = a
              _this.getBusinessList(_this.dName, levels)
            } else if (levels == 2) {
              _this.dName1 = a
              _this.getBusinessList(_this.dName1, levels)
            } else if (levels == 3) {
              _this.dName2 = _this.dName1 + '-' + a
              _this.getBusinessList(_this.dName2, levels)
            } else if (levels == 4) {
              _this.dName3 = _this.dName2 + '-' + a
              _this.getBusinessList(_this.dName3, levels)
            } else if (levels == 5) {
              _this.dName4 = _this.dName3 + '-' + a
              _this.getBusinessList(_this.dName4, levels)
            }
          }
        })
        window.addEventListener('resize', () => {
          this.leftCharts.resize()
        })
      })
    },
    // 左侧点击查询
    getBusinessList(dName3, levels) {
      if (dName3 && levels) {
        this.businessRoute = dName3
        this.businessLevel = levels.toString()
        if (levels == 1) {
          this.optime = dName3
        }
      }
      const data = {
        statType: this.timeType,
        opTime: this.optime,
        cityId: '640000',
        targetId: 'y0003',
        startTime: this.startTime || this.date,
        endTime: this.endTime || this.date,
        businessLevel: this.businessLevel,
        businessRoute: this.businessRoute
      }
      getBusiness(data).then(res => {
        if (res && res.data) {
          this.initCharts(res.data.data, this.timeType, this.businessLevel)
        } else {
          this.initCharts([], this.timeType, this.businessLevel)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.challenge {
  width: 100%;
  height: 100%;
  .setMeal {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;
    .setMeal-left,
    .setMeal-right {
      width: 46%;
      height: 100%;
    }
    .leftChart {
      width: 100%;
      height: 450px;
    }
    .setMeal-right {
      width: 46%;
      .header {
        color: rgb(140, 140, 140);
        font-size: 12px;
        padding: 10px 0;
        padding-top: 0px;
      }
      .progress-title {
        font-size: 18px;
        font-weight: bolder;
        padding: 20px 0;
      }
      .header,
      .body {
        width: 100%;
        display: flex;
        .hader-cell,
        .body-cell {
          width: 15%;
          text-align: center;
          color: #595959;
          font-size: 12px;
        }
        .body-name {
          width: 160px;
          padding-right: 20px;
          box-sizing: border-box;
          height: 30px;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .hader-progress,
      .body-progress {
        width: 40%;
        margin-top: 10px;
      }
      :deep( .el-progress-bar .el-progress-bar_inner ) {
        border-radius: inherit;
      }
      :deep(.el-progress-bar .el-progress-bar__outer ) {
        height: 10px !important;
        border-radius: inherit;
      }
      .body {
        font-size: 16px;
        .body-cell,
        .body-name .body-progress {
          padding: 8px 0;
        }
      }
    }
  }
}
.goBack {
  cursor: pointer;
  position: absolute;
  left: 90%;
  top: 42px;
  z-index: 12;
}
</style>
