<template>
  <div class="bar-main">
    <div ref="barChart" class="barChart" />
    <div id="blank" :style="{position:'absolute',left:0,top:0,width:'100%',height:'100%',display:black.length?'none':'block'}">
      <Blank2 />
    </div>
  </div>
</template>

<script>
import Blank2 from '@/views/bj_proatal_web/components/common/Blank2'
export default {
  name: 'BarC<PERSON>',
  components: {
    Blank2
  },

  data() {
    return {
      barName: '投诉量',
      data1: [],
      data2: [],
      black: []
    }
  },
  mounted() {
    this.createChart()
  },
  methods: {
    refreshData(nOption) {
      this.data1 = []
      this.data2 = []

      // for (let i = 0; i < 12; i++) {
      //   this.data1.push(Math.floor(Math.random() * 70));
      //   this.data2.push(Math.floor(Math.random() * 70));
      // }

      if (nOption.xdata.length === 0) {
        this.black = []
        this.barChart.showLoading({
          text: '暂无数据',
          color: 'rgba(255, 255, 255, 0)',
          fontSize: 20,
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 0.9)'
        })
      } else {
        this.black = nOption.data0
        this.barChart.hideLoading()
      }
      this.barOption.series[0].data = nOption.data0
      this.barOption.series[1].data = nOption.data1
      this.barOption.xAxis.data = nOption.xdata

      this.barOption.legend.data[0] = nOption.barName
      this.barOption.series[0].name = nOption.barName
      this.barOption.yAxis[0].name = nOption.barName
      this.barChart.setOption(this.barOption)
    },

    createChart() {
      this.barChart = this.$echarts.init(this.$refs.barChart)
      this.barOption = {
        grid: {
          top: '14%',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          },
          borderWidth: 0,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)',
          textStyle: {
            color: '#ffffff',
            fontSize: 14,
            align: 'left'
          },
          formatter: function(params) {
            console.log(params)
            const html = `
                          <div style="line-height: 30px;color:#000">
                           <div style="border-bottom:1px solid #eee;color:#777;text-align:center">${params[0].name}</div>
                           <div><i style="display: inline-block;width: 10px;height: 10px;background:${params[0].color}
                                    ;margin-right: 5px;border-radius: 50%;}"></i>${params[0].seriesName}: <b>${params[0].value}</b></div>
                           <div><i style="display: inline-block;width: 10px;height: 10px;background:${params[1].color}
                                    ;margin-right: 5px;border-radius: 50%;}"></i>环比：<b> ${params[1].value ? `${params[1].value}%` : '-'}</b></div>
                          </div>`
            return html
          }
        },

        legend: {
          data: ['投诉量', '环比'],
          top: '2%',
          textStyle: {
            color: '#747474'
          }
        },
        xAxis: {
          type: 'category',
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
          ],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#393939'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '投诉量',
            nameTextStyle: {
              color: '#393939'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#eeeeee'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              }
            }
          },
          {
            type: 'value',
            name: '环比',
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 0, 40] // 四个数字分别为上右下左与原位置距离
            },
            position: 'right',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              },
              formatter: function(val) {
                return val + '%'
              }
            }

          },
          {
            type: 'value',
            gridIndex: 0,
            min: 50,
            max: 100,
            splitNumber: 8,
            splitLine: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitArea: {
              show: false,
              areaStyle: {
                color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)']
              }
            }
          }
        ],
        series: [
          {
            name: '投诉量',
            type: 'bar',
            barWidth: 15,
            itemStyle: {
              normal: {
                color: '#FF9900'
              }
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                fontWeight: 'bolder',
                fontSize: '12',
                color: '#8C8C8C',
                opacity: 1
              }
            },
            data: this.data1
          },
          {
            name: '环比',
            type: 'line',
            yAxisIndex: 1,
            // showAllSymbol: false, //小圆点
            // symbol: "rect",
            symbolSize: 8,
            itemStyle: {
              color: '#83C084',
              borderWidth: '2',
              borderColor: '#83C084'
            },
            lineStyle: {
              color: '#83C084' // 线颜色
            },
            data: this.data2
            // smooth: true
          }
        ]
      }

      this.barChart.setOption(this.barOption)

      const self = this
      setTimeout(() => {
        window.addEventListener('resize', function() {
          self.chart = self.$echarts.init(self.$refs.barChart)
          self.chart.resize()
        })
      }, 100)
    }
  }
}
</script>

<style  lang='less' scoped>
.bar-main {
  position: relative;
  width: 100%;
  text-align: center;
  .barChart {
    width: 100%;
    height: 100%;
  }
}
</style>
