<template>
  <div class="reportforms">
    <div class="layout">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <el-form-item label="用户标识" prop="userId">
                <!-- <el-input v-model="formInline.userId" clearable /> -->
                <el-select
                v-model="formInline.userId"
                filterable
                clearable
                remote
                reserve-keyword
                placeholder=""
                :remote-method="remoteMethodUserId"
                :loading="userIdLoading">
                <el-option
                  v-for="item in userIdOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              </el-form-item>
              <el-form-item label="手机号码" prop="seryNumber">
                <el-input v-model="formInline.seryNumber" clearable />
              </el-form-item>
              <el-form-item label="是否有集团归属" prop="groupFlag">
                 <el-select v-model="formInline.groupFlag"  clearable placeholder="请选择">
                    <el-option
                      v-for="item in groupFlagOpts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                </el-select>
              </el-form-item>
              <el-form-item label="归属集团单位" prop="groupName">
                <!-- <el-input v-model="formInline.groupName" clearable /> -->
                <el-select
                v-model="formInline.groupName"
                filterable
                remote
                clearable
                reserve-keyword
                placeholder=""
                :remote-method="remoteMethodGroupName"
                :loading="groupNameLoading">
                <el-option
                  v-for="item in groupNameOpts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              </el-form-item>
              <el-form-item label="集团单位级别" prop="servLevelId">
                <el-select v-model="formInline.servLevelId"  clearable placeholder="请选择">
                  <el-option
                      v-for="item in servLevelIdOpts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                </el-select>
              </el-form-item>
              <el-form-item label="接触状态" prop="contactStatus">
                 <el-select v-model="formInline.contactStatus"  clearable placeholder="请选择">
                  <el-option
                      v-for="item in contact_status_opts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                </el-select>
              </el-form-item>
              <el-form-item label="接触结果（打分结果）" prop="contactResultList">
                 <el-input v-model="formInline.contactResultList" clearable />
                 <!-- <el-select v-model="formInline.contactResultList" multiple clearable placeholder="请选择">
                  <el-option
                      v-for="item in contact_result_opts"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                </el-select> -->


                 <!-- <el-select
                v-model="formInline.contactResultList"
                filterable
                multiple
                remote
                clearable
                reserve-keyword
                placeholder=""
                :remote-method="remoteMethodContactResult"
                :loading="contact_result_optsLoading">
                <el-option
                  v-for="item in contact_result_opts"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select> -->



              </el-form-item>
                 <el-form-item label="修复状态" prop="repairStatus">
                 <el-select v-model="formInline.repairStatus"  clearable placeholder="请选择">
                  <el-option
                    v-for="item in repair_status_opts"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="归属地市" prop="d2LevelDesc">
                <el-input v-model="formInline.d2LevelDesc" clearable />
              </el-form-item>
              <el-form-item label="归属区县" prop="d3LevelDesc">
                <el-input v-model="formInline.d3LevelDesc" clearable />
              </el-form-item>
              <el-form-item label="归属网格" prop="subName">
                <el-input v-model="formInline.subName" clearable />
              </el-form-item>
              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
                <el-button v-if="canDownReportsData" :loading="downLoading" :size="conditionsize" type="primary" @click="down">导出</el-button>
              </el-form-item>

            </el-form>


          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              :height="getTableHeight()"
              border>
               <el-table-column
                v-for="item in columnList"
                :key="item.key"
                :prop="item.key"
                :label="item.name"
                align="center"
                :show-overflow-tooltip="true"
                :width="item.width?item.width:'unset'"
                min-width="150"
              >
                <template slot-scope="scope">
                  <span >{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>

  </div>
</template>
<script>


import { getf1ReportList, exportf1,getF1FieldOpts,getFieldEnumByDim,getDownloadAuthority  } from '@/api/reportforms/index'
import colums from './report10086'
import { eventBus } from '@/main.js'



export default {

  props: { canDownReportsData: { type: Boolean }},
  data() {
    return {
      userIdOpts:[],
      groupNameOpts:[],
      userIdLoading:false,
      contact_result_optsLoading:false,
      groupNameLoading:false,
      contact_result_opts:[],
      repair_status_opts:[],
      contact_status_opts:[],
      servLevelIdOpts:[],
      groupFlagOpts:[],
      downLoading: false,
      // 文件导入新建客群
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6 // 如果没有后面的-8.64e6就是不可以选择今天的
        }
      },

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',

      activeTypeOptions: [

        { label: '手机重保', value: '手机重保' },
        { label: '宽带重保', value: '宽带重保' }
      ],

      userKeyOptions: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
        { label: '3', value: '3' },
        { label: '空值', value: '空值' }

      ],

      ifGroupOptions: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ],

      // 外呼成功/无人接听/用户拒接
      calloutResultOptions: [
        { value: '呼叫成功', label: '呼叫成功' },
        { value: '无人接听', label: '无人接听' },
        { value: '用户忙', label: '用户忙' },
        { value: '呼叫转移', label: '呼叫转移' },
        { value: '用户拒绝', label: '用户拒绝' },
        { value: '正在通话', label: '正在通话' },
        { value: '用户应答，提前挂机(秒挂)', label: '用户应答，提前挂机(秒挂)' },
        { value: '关机', label: '关机' },
        { value: '空号', label: '空号' },
        { value: '无法接通', label: '无法接通' },
        { value: '忙音', label: '忙音' },
        { value: '停机', label: '停机' },
        { value: '呼损', label: '呼损' },
        { value: '呼入受限', label: '呼入受限' },
        { value: '来电提醒', label: '来电提醒' }

      ],
      formInline: {
         "contactResultList": '',
          "contactStatus": "",
          "d2LevelDesc": "",
          "d3LevelDesc": "",
          "groupFlag": "",
          "groupName": "",
          "repairStatus": "",
          "servLevelId": "",
          "seryNumber": "",
          "subName": "",
          "userId": ""

      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 20,
        total: 0
      },

      tableDatas: [],

      columnList: colums.columsf1,

      dialogFormVisible: false,
      dialogFormVisible2: false,
      cacheQueryListParams: {},
      tableLoading: false

    }
  },

  watch: {
    dialogFormVisible: {
      deep: true,
      handler: function(v, oldv) {
        if (v == false) {
          this.checkRow = {}
        }
      }
    },
    // formInline:{
    //   deep:true,
    //   handler:function(v,oldv){
    //     this.queryList();
    //   }
    // }
  },
  created(){


     getF1FieldOpts({fieldName:'SERV_LEVEL_ID'}).then(res=>{
      let {code,data}=res;
      if(code===200 && Array.isArray(data)){
        this.servLevelIdOpts = data.map(i=>{
          return {label:i,value:i}
        })
      }
    })
    getF1FieldOpts({fieldName:'repair_status'}).then(res=>{
      let {code,data}=res;
      if(code===200 && Array.isArray(data)){
        this.repair_status_opts = data.map(i=>{
          return {label:i,value:i}
        })
      }
    })
    getF1FieldOpts({fieldName:'contact_status'}).then(res=>{
      let {code,data}=res;
      if(code===200 && Array.isArray(data)){
        this.contact_status_opts = data.map(i=>{
          return {label:i,value:i}
        })
      }
    })
    // getF1FieldOpts({fieldName:'contact_result'}).then(res=>{
    //   let {code,data}=res;
    //   if(code===200 && Array.isArray(data)){
    //     this.contact_result_opts = data.map(i=>{
    //       return {label:i,value:i}
    //     })
    //   }
    // })
     getF1FieldOpts({fieldName:'GROUP_FLAG'}).then(res=>{
      let {code,data}=res;
      if(code===200 && Array.isArray(data)){
        this.groupFlagOpts = data.map(i=>{
          return {label:i,value:i}
        })
      }
    })


  },
  mounted() {


    // this.$confirm('!本报表全量数据巨大，如需要导出报表数据，请增加筛选条件后再导出', '提示', {
    //       confirmButtonText: '确定',
    //       showCancelButton:false,
    //       type: 'warning'
    //     }).then(() => {

    //     })

    console.log('colums:', colums)
    // console.log('this.$store:', this.$store)
    // const code = this.$route.params.code
    // this.customerCode = code

    this.init()
  },
  methods: {

    // contact_result
    // remoteMethodContactResult(queryContent){
    //    if (queryContent !== '') {
    //       this.contact_result_optsLoading = true;
    //       getFieldEnumByDim({fieldName:'contact_result',dimVal:queryContent}).then(res=>{
    //         let {code,data} = res;
    //         if(code==200&&Array.isArray(data)){
    //           this.contact_result_opts = data.map(i=>{
    //             return {label:i,value:i}
    //           })
    //         }
    //       }).finally(()=>{
    //         this.contact_result_optsLoading = false;
    //       })
    //     } else {
    //       this.contact_result_opts = [];
    //     }
    // },
    remoteMethodGroupName(queryContent){
       if (queryContent !== '') {
          this.groupNameLoading = true;
          getFieldEnumByDim({fieldName:'GROUP_NAME',dimVal:queryContent}).then(res=>{
            let {code,data} = res;
            if(code==200&&Array.isArray(data)){
              this.groupNameOpts = data.map(i=>{
                return {label:i,value:i}
              })
            }

          }).finally(()=>{
            this.groupNameLoading = false;
          })
        } else {
          this.groupNameOpts = [];
        }
    },
    remoteMethodUserId(queryContent){
       if (queryContent !== '') {
          this.userIdLoading = true;
          getFieldEnumByDim({fieldName:'USER_ID',dimVal:queryContent}).then(res=>{
            let {code,data} = res;
            if(code==200&&Array.isArray(data)){
              this.userIdOpts = data.map(i=>{
                return {label:i,value:i}
              })
            }

          }).finally(()=>{
            this.userIdLoading = false;
          })
        } else {
          this.userIdOpts = [];
        }
    },
    init() {
      this.queryList()
    },
    cancelDialog() {
      this.dialogFormVisible = false
      this.queryList()
      this.checkRow = {}
    },

    // 删除

    query() {
      this.page.current = 1
      this.queryList()
    },
    // 导出

    async down() {

     let jkFlag = await  getDownloadAuthority();
     if(!jkFlag.data){
      eventBus.$emit('startJKauth');
      return
     }


      //  this.statType = mode
      // this.date = monthValue
      const { page, formInline, } = this

      if(page.total>1000000){//数据量超过100万条
        this.$message.error('数据量超过100万条,请增加筛选条件后再导出数据！')
        return;
      }



      const {

          contactResultList,
          contactStatus,
          d2LevelDesc,
          d3LevelDesc,
          groupFlag,
          groupName,
          pageSize,
          repairStatus,
          servLevelId,
          seryNumber,
          subName,
          userId

      } = formInline


      const params = Object.assign({}, {
          contactResultList:contactResultList?[contactResultList]:[],
          contactStatus,
          d2LevelDesc,
          d3LevelDesc,
          groupFlag,
          groupName,
          pageSize,
          repairStatus,
          servLevelId,
          seryNumber,
          subName,
          userId
 })


      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          this.$emit('downStart')
          return exportf1(params)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]
          this.download(response.msg)

          // 兼容ie
          // if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          //   window.navigator.msSaveOrOpenBlob(
          //     response,
          //     '10086热线场景式测评满意度日报.xls'
          //   )
          //   return false
          // }
          // const url = URL.createObjectURL(response)
          // console.log('url==>', url)
          // const aLink = document.createElement('a')
          // aLink.href = url
          // aLink.setAttribute('download', '10086热线场景式测评满意度日报.xls')
          // document.body.appendChild(aLink)
          // aLink.click()
          // document.body.removeChild(aLink)
        }).finally(() => {
          this.downLoading = false
          this.$emit('downEnd')
        })
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, formInline } = this
      const {

         contactResultList,
          contactStatus,
          d2LevelDesc,
          d3LevelDesc,
          groupFlag,
          groupName,
          repairStatus,
          servLevelId,
          seryNumber,
          subName,
          userId

      } = formInline

      const timeRange = formInline.timeRange
      const params = Object.assign({}, { currentPage: page.current, pageSize: page.size }, {
          contactResultList:contactResultList?[contactResultList]:[],
          contactStatus,
          d2LevelDesc,
          d3LevelDesc,
          groupFlag,
          groupName,
          repairStatus,
          servLevelId,
          seryNumber,
          subName,
          userId
          })
      this.tableLoading = true
      getf1ReportList(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, current } = data
          // 处理不满意指标
          this.tableDatas = records || []
          this.page.total = total
          this.page.current = current
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    preback() {
      sessionStorage.setItem('menuUrl', `/customergroup`)
      this.$router.push({
        name: 'customergroup'
      })
    },

    resetForm(formName) {
      this.page.current = 1
      this.$refs[formName].resetFields()
      this.queryList()
      // this.csmcustomerlableSeleted = []
      // this.formInline.timeRange = []
      // this.formInline.content = ''
      // this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },

    getTableHeight() {
      return document.body.offsetHeight - 268 + 'px';
    }

  }

}
</script>

<style >
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>

<style lang='scss' scoped>

.msgdialog :deep(.el-form-item__label){
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog :deep(.el-checkbox__label){
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
:deep(.el-dialog__header){
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn){
  top:12px;
  line-height: 20px;

}
:deep(.el-select .el-input.is-focus .el-input__inner){
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus){
  border-color: #ff9900;
}
.reportforms{
        background-color: rgba(242, 242, 242, 1);

        :deep(.el-input-group__append), :deep(.el-input-group__prepend){
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            // padding: 0;

            :deep(.el-tabs__nav-wrap::after){
              height:1px;
              background:#E6E6E6;
            }
            :deep(.el-tabs__active-bar){
              background:#FF9900;
            }
            :deep(.el-tabs__item.is-active){
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            :deep(.el-tabs__item){
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            :deep(.el-form-item.el-form-item--medium){margin-right:20px}
            :deep(.el-form-item__content .el-input-group){
              vertical-align: middle;
            }
            :deep(.el-pagination){text-align: right;}
            .speformitem :deep(.el-form-item__label){
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        :deep(.el-icon-arrow-down:before) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }

  }

    }

 :deep(.el-table--medium .el-table__cell){
   padding:8px 0;
 }
 :deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell){
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  //border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
:deep(.el-form-item.sxgroupCusName.el-form-item--medium .groupCusName){
  width:120px!important;
}

</style>
