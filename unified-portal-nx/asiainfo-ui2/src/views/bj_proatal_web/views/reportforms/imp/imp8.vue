<template>
  <div class="reportforms">
    <div class="layout">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <el-form-item label="选择周">
                <el-select v-model="formInline.week" placeholder="请选择" style="width:300px">
                  <el-option
                    v-for="item in weekoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item>
<el-date-picker :editable="false"
  ref="getStartWeek"
  @change="startWeekChange"
  v-model="formInline.startWeek"
  type="week"
  format="yyyy年-WW周"
  :picker-options="startPickerOptions"
  :placeholder="开始周"
  :clearable="false"
></el-date-picker>
<span>&nbsp;-&nbsp;</span>
<el-date-picker :editable="false"
  ref="getEndWeek"
  @change="endWeekChange"
  v-model="formInline.endWeek"
  type="week"
  format="yyyy年-WW周"
  :picker-options="pickerOptions"
  :placeholder="结束周"
  :clearable="false">
  </el-date-picker>
              </el-form-item> -->

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
                <el-button v-if="canDownReportsData" :loading="downLoading" :size="conditionsize" type="primary" @click="down">导出</el-button>
              </el-form-item>

            </el-form>

            <div class="btnbox">
              <!-- <el-button :size="conditionsize" type="default" @click="dialogFormVisible=true">新建短信模版</el-button> -->
            </div>
          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              :height="getTableHeight()"
              :cell-class-name="cellClassName"
              border
            >
              <el-table-column
                prop="cityName"
                label="地市"
                align="center"
              />
              <el-table-column
                v-for="(item,idx) in columns"
                :key="idx"
                align="center"
                :label="item.label"
                :prop="item.prop"
              >
                <el-table-column
                  v-for="({label,prop, width},cidx) in item.children"
                  :key="`${idx}-${cidx}`"
                  align="center"
                  :label="label"
                  :prop="prop"
                  :width="width?width:'unset'"
                />
              </el-table-column>

            </el-table>
          </div>
        </div>
        <!-- 分页功能 -->
        <!-- <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div> -->

      </div>
    </div>

  </div>
</template>
<script>

import { getStatDateList, exportOdsCycleReportList, getOdsCycleReportList, get91090Page, get91090Export, getDownloadAuthority } from '@/api/reportforms/index'

import columns from '@/views/bj_proatal_web/views/reportforms/report10086.js'
import { eventBus } from '@/main.js'

export default {

  props: { canDownReportsData: { type: Boolean }},
  data() {
    return {
      downLoading: false,
      // 文件导入新建客群
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6 // 如果没有后面的-8.64e6就是不可以选择今天的
        }
      },

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',
      weekoptions: [],
      statType: '1',
      targetId: '2',
      formInline: {
        // timeRange: [],
        // 'acctCycleBegin': '',
        // 'acctCycleEnd': '',
        // 'cityName': '',
        // 'cjName': '',
        'currentPage': 1,
        'pageSize': 10,
        'statisMonth': '',
        'week': '',
        'startWeek': '',
        'endWeek': ''

      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 10,
        total: 0
      },

      tableDatas: [],

      dialogFormVisible: false,
      dialogFormVisible2: false,
      cacheQueryListParams: {
        statType: '1',
        targetId: '2'
      },
      tableLoading: false,

      startWeek: '',
      endWeek: '',
      startPickerOptions: {},
      columns: columns.imp28Columns,

      searchForm: {
        beginWeekIndex: 0, // 开始周   (int)
        endWeekIndex: 0, // 结束周   (int)
        beginDate: '', // 开始时间 (str)
        endDate: '' } // 结束时间 (str)

    }
  },

  watch: {
    dialogFormVisible: {
      deep: true,
      handler: function(v, oldv) {
        if (v == false) {
          this.checkRow = {}
        }
      }
    },
    // 开始时间   2019-01-01 之前的日期禁用 || 两周前的今天，往后的日期禁用
    endWeek(newV, oldV) {
      const _this = this
      const weekDay = this.$moment(new Date()).weekday()
      this.startPickerOptions = {
        disabledDate(time) {
          return (
            time.getTime() < _this.$moment('Tue Jan 01 2019 00:00:00 GMT+0800 (中国标准时间)') ||
            time.getTime() > _this.$moment(new Date()).add(-7 - weekDay, 'd')
          )
        },
        firstDayOfWeek: 1
      }
    },
    // 结束时间   已选中的开始日期之前的日期禁用 || 两周前的今天，往后的日期禁用
    startWeek(newV, oldV) {
      const _this = this
      const weekDay = this.$moment(new Date()).weekday()
      this.pickerOptions = {
        disabledDate(time) {
          return (
            time.getTime() < _this.$moment(newV) ||
            time.getTime() > _this.$moment(new Date()).add(-7 - weekDay, 'd')
          )
        },
        firstDayOfWeek: 1
      }
    }

  },

  created() {
    const { statType, targetId } = this
    getStatDateList({
      statType, // 1-周 2-季度(累计值)
      targetId // 1-移动业务满意度 2-固定宽带满意度
    }).then(res => {
      if (res.code == 200 && Array.isArray(res.data)) {
        this.weekoptions = res.data.map((i, idx) => {
          const obj = {
            label: `开始${i.startTime} 结束${i.endTime}`,
            value: `${i.startTime}&${i.endTime}`
          }
          if (idx == 0) {
            this.formInline.week = `${i.startTime}&${i.endTime}`
          }
          return obj
        })
      } else {
        this.weekoptions = []
      }
      this.init()
    })
  },

  mounted() {

  },
  methods: {
    // 开始周
    startWeekChange() {
      var d = this.disposeWeek(new Date(this.startWeek))
      this.searchForm.beginWeekIndex = this.$refs.getStartWeek.displayValue.slice(6, 8) * 1
      this.searchForm.beginDate =
        d.getFullYear() + '-' +
        (d.getMonth() + 1 < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1)
      // console.log(beginWeekIndex)   ====>   选中的当前周(今年的第几周 int 类型)
      // console.log(beginDate)        ====>   当前选中的周是哪年哪月的(如：2022-06)
    },
    // 结束周
    endWeekChange() {
      var d = this.disposeWeek(new Date(this.endWeek))
      this.searchForm.endWeekIndex = this.$refs.getEndWeek.displayValue.slice(6, 8) * 1
      this.searchForm.endDate =
        d.getFullYear() + '-' +
        (d.getMonth() + 1 < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1)
    },

    // element的选择器是将当日的周数（周几）进行的取值
    // （比如今天周三，选中的那一周是根据周三来定的年月日)
    // 每周一为 年份/月份的起始
    disposeWeek(date) {
      // 这里的判断是因为2019-01-01是在周二，周一为 2018-12-31
      // 但库里没有 19年1月之前的数据所以 这里检测到为19年第一周时，不继续接下来的方法
      if (date.getFullYear() === 2019 && date.getMonth() + 1 === 1) {
        return new Date('2019-01-01')
      }
      var Time = date.getTime()
      var day = date.getDay()
      var oneDayTime = 24 * 60 * 60 * 1000
      var MondayTime = Time - (day - 1) * oneDayTime
      return new Date(MondayTime)
    },

    init() {
      this.queryList()
    },
    cancelDialog() {
      this.dialogFormVisible = false
      this.queryList()
      this.checkRow = {}
    },

    query() {
      this.page.current = 1
      this.queryList()
    },
    // 导出

    async down() {
      const jkFlag = await getDownloadAuthority()
      if (!jkFlag.data) {
        eventBus.$emit('startJKauth')
        return
      }
      //  this.statType = mode
      // this.date = monthValue
      if (Array.isArray(this.tableDatas) && !this.tableDatas.length) {
        this.$message.warning('没有数据,无法导出数据')
        return
      }
      const { cacheQueryListParams } = this
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return exportOdsCycleReportList(cacheQueryListParams)
        })
        .then((response) => {
          this.download(response.msg)
        }).finally(() => {
        this.downLoading = false
      })
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, formInline, statType, targetId } = this
      const {

        week

      } = formInline
      const timeRange = formInline.timeRange
      const params = Object.assign({}, { statType, targetId }
      )
      console.log('week:', week)
      if (week) {
        const arrtemp = week.split('&')
        console.log('arrtemp:', arrtemp)
        params.startTime = arrtemp[0]
        params.endTime = arrtemp[1]
        delete params.week
      }

      delete params.timeRange

      console.log('params:', params)
      this.cacheQueryListParams = params
      this.tableLoading = true
      getOdsCycleReportList(params).then(res => {
        if (res && Array.isArray(res)) {
          res.forEach(i => {
            Object.keys(i).forEach(j => {
              i[j] = (i[j] === 0 || i[j] === '0') ? '0' : i[j] ? i[j] : (i[j] === null || i[j] === 'NULL') ? '-' : i[j]
            })
          })
        }
        this.tableDatas = res || []

        // const { code, data } = res

        // if (code == 200) {
        //   const { records, total, current } = data

        //   // 处理不满意指标
        //   this.tableDatas = records || []
        //   this.page.total = total
        //   this.page.current = current
        // }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    preback() {
      sessionStorage.setItem('menuUrl', `/customergroup`)
      this.$router.push({
        name: 'customergroup'
      })
    },

    resetForm(formName) {
      this.page.current = 1
      this.$refs[formName].resetFields()
      this.formInline.week = this.weekoptions.length ? this.weekoptions[0].value : ''

      this.queryList()
      // this.csmcustomerlableSeleted = []
      // this.formInline.timeRange = []
      // this.formInline.content = ''
      // this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },

    getTableHeight() {
      return document.body.offsetHeight - 268 + 'px'
    },

    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 3) {
        return
      }
      if (columnIndex == 2) {
        if (Number(row[column.property]) >= 0) {
          return 'green-cell'
        } else if (Number(row[column.property]) < 0) {
          return 'red-cell'
        }
      } else {
        if (Number(row[column.property]) < 85) {
          return 'red-cell'
        }
      }
    }

  }

}
</script>

<style >
.xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>

<style lang='scss' scoped>
.red-cell {
  color:red;
}

.green-cell {
  color:green;
}

:deep(.el-tabs--border-card) {
  box-shadow: none;
}
:deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus) {
  border-color: #ff9900;
}
.reportforms{
  background-color: rgba(242, 242, 242, 1);
  // min-height: 100vh;
  :deep(.el-input-group__append), :deep(.el-input-group__prepend) {
    background:#F19733;
    color:#262626;
    &:hover{
      color:#fff;
    }
  }
  .layout{
    // padding: 0 50px;
    padding:0;

    :deep(.el-tabs__nav-wrap::after) {
      height:1px;
      background:#E6E6E6;
    }
    :deep(.el-tabs__active-bar) {
      background:#FF9900;
    }
    :deep(.el-tabs__item.is-active) {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #262626;
    }
    :deep(.el-tabs__item) {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #595959;

    }
    :deep(.el-form-item.el-form-item--medium) {margin-right:20px}
    :deep(.el-form-item__content .el-input-group) {
      vertical-align: middle;
    }
    :deep(.el-pagination) {text-align: right;}
    .speformitem :deep(.el-form-item__label) {
      width:96px;
    }
    .timeRange {

    }

  }
  .flexbox{
    display: flex;
    position: relative;

    .formbox{
      flex:1;

    }
    .btnbox{
      position: absolute;
      right:0;
      bottom: 22px;
    }
  }

  .el-cascader {
    :deep(.el-icon-arrow-down:before ) {
      content: "\E6E1";

    }
    :deep(.el-icon-arrow-down) {
      transform: rotate(180deg);
    }
    :deep(.is-reverse.el-icon-arrow-down) {
      transform: rotate(0deg);
    }

  }

}

:deep(.el-table--medium .el-table__cell) {
  padding:8px 0;
}
:deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid rgba(225,225,225,0.3);
}

.conbox{
  background:#fff;
  //border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}

</style>
