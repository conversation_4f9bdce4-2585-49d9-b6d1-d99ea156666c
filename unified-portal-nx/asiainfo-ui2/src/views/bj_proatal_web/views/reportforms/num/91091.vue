<template>
  <div class="reportforms">

    <div class="layout">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <!-- <el-form-item label="账期" class="timeRange" prop="timeRange">
                <el-date-picker :editable="false"
                  v-model="formInline.timeRange"
                  popper-class="xps"
                  style="width:250px"
                  :size="conditionsize"
                  type="monthrange"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />

              </el-form-item>
              <el-form-item label="地市" prop="cityName">
                <el-input
                  v-model="formInline.cityName"
                  :size="conditionsize"
                  placeholder="输入地市查询"
                />
              </el-form-item>
              <el-form-item label="场景名称" prop="cjName">
                <el-input
                  v-model="formInline.cjName"
                  :size="conditionsize"
                  placeholder="输入场景名称查询"
                />
              </el-form-item> -->

              <el-form-item label="统计月份" prop="statisMonth">
                <el-date-picker
                  v-model="formInline.statisMonth"
                  :editable="false"
                  type="month"
                  placeholder="选择月"
                />
              </el-form-item>

              <!-- <el-form-item label="指标级别" prop="zbLev">
                <el-input
                  v-model="formInline.zbLev"
                  :size="conditionsize"
                  placeholder="输入指标级别查询"
                />
              </el-form-item>

              <el-form-item label="指标类型" prop="zbType">
                <el-input
                  v-model="formInline.zbType"
                  :size="conditionsize"
                  placeholder="输入指标类型查询"
                />
              </el-form-item> -->

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
                <el-button v-if="canDownReportsData" :loading="downLoading" :size="conditionsize" type="primary" @click="down">导出</el-button>
              </el-form-item>

            </el-form>

            <div class="btnbox">
              <!-- <el-button :size="conditionsize" type="default" @click="dialogFormVisible=true">新建短信模版</el-button> -->
            </div>
          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              :height="getTableHeight()"
              border
            >
              <el-table-column
                v-for="item in columnList"
                :key="item.name"
                :prop="item.key"
                :label="item.name"
                align="center"
                :show-overflow-tooltip="true"
                :width="item.width?item.width:'unset'"
                min-width="150"
              >
                <template slot-scope="scope">

                  <span v-if="item.key=='type'">
                    {{ scope.row[item.key]==1?'修复后评价':scope.row[item.key]==0?'主动关怀':'' }}
                  </span>

                  <span v-else-if="scope.row[item.key]===0||scope.row[item.key]===&quot;0&quot;">0</span>
                  <span v-else-if="scope.row[item.key]===&quot;&quot;||scope.row[item.key]===null||scope.row[item.key]===&quot;NULL&quot;">-</span>
                  <span v-else>{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>

  </div>
</template>
<script>


import { get91091Page, get91091Export ,getDownloadAuthority } from '@/api/reportforms/index'

import tool from '@/views/bj_proatal_web/utils/utils'
import { eventBus } from '@/main.js'


export default {

  props: { canDownReportsData: { type: Boolean }},
  data() {
    return {
      downLoading: false,
      // 文件导入新建客群
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6 // 如果没有后面的-8.64e6就是不可以选择今天的
        }
      },

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',
      formInline: {
        // timeRange: [],
        // 'acctCycleBegin': '',
        // 'acctCycleEnd': '',
        // 'cityName': '',
        // 'cjName': '',
        'currentPage': 1,
        'pageSize': 20,
        'statisMonth': ''
        // 'zbLev': '',
        // 'zbType': ''
      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 20,
        total: 0
      },

      tableDatas: [],

      //  "statisMonth": "202208",
      // "dataBatch": "202208",
      // "userId": "9530800785764",
      // "bumanyiChance": ".9374",
      // "bumanyiCause": "3"

      columnList: [
        {
          key: 'dataBatch',
          name: '数据批次'
        },
        {
          key: 'userId',
          name: '用户ID'
        },
        {
          key: 'bumanyiChance',
          name: '不满意概率'
        },
        {
          key: 'bumanyiCause',
          name: '不满意原因'
        },
        {
          key: 'statisMonth',
          name: '统计月份'
        }

      ],

      dialogFormVisible: false,
      dialogFormVisible2: false,
      cacheQueryListParams: null,
      tableLoading: false

    }
  },

  watch: {
    dialogFormVisible: {
      deep: true,
      handler: function(v, oldv) {
        if (v == false) {
          this.checkRow = {}
        }
      }
    }
  },

  mounted() {
    // console.log('this.$store:', this.$store)
    // const code = this.$route.params.code
    // this.customerCode = code

    this.init()
  },
  methods: {
    init() {
      this.queryList()
    },
    cancelDialog() {
      this.dialogFormVisible = false
      this.queryList()
      this.checkRow = {}
    },

    // 删除

    query() {
      this.page.current = 1
      this.queryList()
    },
    // 导出

     async down() {

     let jkFlag = await  getDownloadAuthority();
     if(!jkFlag.data){
      eventBus.$emit('startJKauth');
      return
     }
      //  this.statType = mode
      // this.date = monthValue
      const { cacheQueryListParams } = this
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return get91091Export(cacheQueryListParams)
        })
        .then((response) => {
          this.download(response.msg)
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          // if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          //   window.navigator.msSaveOrOpenBlob(
          //     response,
          //     '91091一经接口-资费不满预测结果.xls'
          //   )
          //   return false
          // }
          // const url = URL.createObjectURL(response)
          // console.log('url==>', url)
          // const aLink = document.createElement('a')
          // aLink.href = url
          // aLink.setAttribute('download', '91091一经接口-资费不满预测结果.xls')
          // document.body.appendChild(aLink)
          // aLink.click()
          // document.body.removeChild(aLink)
        }).finally(() => {
          this.downLoading = false
        })
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, formInline } = this
      const {

        statisMonth

      } = formInline
      const timeRange = formInline.timeRange
      const params = Object.assign({}, { currentPage: page.current, pageSize: page.size }
      )
      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.acctCycleBegin = tool.formatterDate(timeRange[0], 'yyyy年MM月')
        params.acctCycleEnd = tool.formatterDate(timeRange[1], 'yyyy年MM月')
      }
      if (statisMonth) {
        params.statisMonth = tool.formatterDate(statisMonth, 'yyyyMM')
      }

      delete params.timeRange

      console.log('params:', params)
      this.cacheQueryListParams = params
      this.tableLoading = true
      get91091Page(params).then(res => {
        const { code, data } = res

        if (code == 200) {
          const { records, total, current } = data

          // 处理不满意指标
          this.tableDatas = records || []
          this.page.total = total
          this.page.current = current
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    preback() {
      sessionStorage.setItem('menuUrl', `/customergroup`)
      this.$router.push({
        name: 'customergroup'
      })
    },

    resetForm(formName) {
      this.page.current = 1
      this.$refs[formName].resetFields()
      this.queryList()
      // this.csmcustomerlableSeleted = []
      // this.formInline.timeRange = []
      // this.formInline.content = ''
      // this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },

    getTableHeight() {
      return document.body.offsetHeight - 268 + 'px';
    }

  }

}
</script>

<style >
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>

<style lang='scss' scoped>

.msgdialog :deep(.el-form-item__label){
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog :deep(.el-checkbox__label){
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
:deep(.el-dialog__header){
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn){
  top:12px;
  line-height: 20px;

}
:deep(.el-select .el-input.is-focus .el-input__inner){
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus){
  border-color: #ff9900;
}
.reportforms{
        background-color: rgba(242, 242, 242, 1);
        // min-height: 100vh;
        :deep(.el-input-group__append), :deep(.el-input-group__prepend){
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            // padding: 0 50px;

            :deep(.el-tabs__nav-wrap::after){
              height:1px;
              background:#E6E6E6;
            }
            :deep(.el-tabs__active-bar){
              background:#FF9900;
            }
            :deep(.el-tabs__item.is-active){
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            :deep(.el-tabs__item){
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            :deep(.el-form-item.el-form-item--medium){margin-right:20px}
            :deep(.el-form-item__content .el-input-group){
              vertical-align: middle;
            }
            :deep(.el-pagination){text-align: right;}
            .speformitem :deep(.el-form-item__label){
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        :deep(.el-icon-arrow-down:before) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }

  }

    }

 :deep(.el-table--medium .el-table__cell){
   padding:8px 0;
 }
 :deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell){
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  //border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}

</style>
