<template>
  <div class="reportforms">

    <div class="layout">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <el-form-item label="统计日期" class="timeRange" prop="timeRange">
                <el-date-picker
                  v-model="formInline.timeRange"
                  :editable="false"
                  :picker-options="pickerOptions"
                  popper-class="xps"
                  style="width:250px"
                  :size="conditionsize"
                  type="daterange"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />

              </el-form-item>

              <el-form-item label="地市">
                <el-select v-model="formInline.a1" placeholder="请选择" clearable @change="cityChange">
                  <el-option
                    v-for="item in optionsCity"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="区县">
                <el-select v-model="formInline.a2" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in optionsCounty"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
                <el-button v-if="canDownReportsData" :loading="downLoading" :size="conditionsize" type="primary" @click="down">导出</el-button>
              </el-form-item>

            </el-form>

            <div class="btnbox">
              <!-- <el-button :size="conditionsize" type="default" @click="dialogFormVisible=true">新建短信模版</el-button> -->
            </div>
          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              :height="getTableHeight()"
              border
            >
              <el-table-column
                v-for="item in columnList"
                :key="item.key"
                :prop="item.key"
                :label="item.name"
                align="center"
                :show-overflow-tooltip="true"
                :width="item.width?item.width:'unset'"
                min-width="150"
              >
                <template slot-scope="scope">

                  <span v-if="item.key=='type'">
                    {{ scope.row[item.key]==1?'修复后评价':scope.row[item.key]==0?'主动关怀':'' }}
                  </span>

                  <span v-else-if="scope.row[item.key]===0||scope.row[item.key]===&quot;0&quot;">0</span>
                  <span v-else-if="scope.row[item.key]===&quot;&quot;||scope.row[item.key]===null||scope.row[item.key]===&quot;NULL&quot;">-</span>
                  <span v-else>{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>

  </div>
</template>
<script>


import { get178009054Page, get178009054Export ,getDownloadAuthority } from '@/api/reportforms/index'
import { getAllGrides } from '@/api/customer/index'

import tool from '@/views/bj_proatal_web/utils/utils'
import { eventBus } from '@/main.js'

import colums from '../report10086'

export default {

  props: { canDownReportsData: { type: Boolean }},
  data() {
    return {
      downLoading: false,
      // 文件导入新建客群
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6 // 如果没有后面的-8.64e6就是不可以选择今天的
        }
      },

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',
      formInline: {
        timeRange: [],
        'statisDateBegin': '',
        'statisDateEnd': '',
        'a1': '',
        'a2': '',
        'currentPage': 1,
        'pageSize': 20

      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 20,
        total: 0
      },

      tableDatas: [],

      columnList: colums.colums178009054,

      optionsCity: [],
      optionsCounty: [],

      dialogFormVisible: false,
      dialogFormVisible2: false,
      cacheQueryListParams: null,
      tableLoading: false

    }
  },

  watch: {
    dialogFormVisible: {
      deep: true,
      handler: function(v, oldv) {
        if (v == false) {
          this.checkRow = {}
        }
      }
    }
  },
  created() {
    // 获取所有网格
    this.getAllGridesData()
  },
  mounted() {
    // console.log('this.$store:', this.$store)
    // const code = this.$route.params.code
    // this.customerCode = code

    this.init()
  },
  methods: {
    getAllGridesData() {
      getAllGrides().then(res => {
        if (res.code == 200 && Array.isArray(res.data)) {
          res.data.forEach(i => {
            i.label = i.cityName
            i.value = i.cityId
          })
          this.optionsCity = res.data
        }
      })
    },
    cityChange(v) {
      let temp = []
      // this.optionsCounty = []
      this.optionsCity.forEach(i => {
        if (i.label == v) {
          temp = i.children || []
        }
      })
      temp.forEach(i => {
        i.label = i.cityName
        i.value = i.cityId
      })
      this.optionsCounty = temp
      console.log('v:', v)
    },
    // handlerCascaderData(arr, labelkey, valuekey) {
    //   if (Array.isArray(arr) && arr.length) {
    //     arr.forEach(i => {
    //       i.label = i[labelkey]
    //       i.value = i[valuekey]
    //       if (i.children && i.children.length && i.level <= 1) {
    //         this.handlerCascaderData(i.children, labelkey, valuekey)
    //       } else {
    //         delete i.children
    //       }
    //     })
    //   }
    //   return arr
    // },
    init() {
      this.queryList()
    },

    query() {
      this.page.current = 1
      this.queryList()
    },
    // 导出

     async down() {

     let jkFlag = await  getDownloadAuthority();
     if(!jkFlag.data){
      eventBus.$emit('startJKauth');
      return
     }
      //  this.statType = mode
      // this.date = monthValue
      const { cacheQueryListParams } = this
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return get178009054Export(cacheQueryListParams)
        })
        .then((response) => {
          this.download(response.msg)
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          // if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          //   window.navigator.msSaveOrOpenBlob(
          //     response,
          //     '家宽移机场景满意度调研报表-支局.xls'
          //   )
          //   return false
          // }
          // const url = URL.createObjectURL(response)
          // console.log('url==>', url)
          // const aLink = document.createElement('a')
          // aLink.href = url
          // aLink.setAttribute('download', '家宽移机场景满意度调研报表-支局.xls')
          // document.body.appendChild(aLink)
          // aLink.click()
          // document.body.removeChild(aLink)
        }).finally(() => {
          this.downLoading = false
        })
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, formInline } = this
      const {
        a1,
        a2
      } = formInline
      const timeRange = formInline.timeRange
      const params = Object.assign({}, { currentPage: page.current, pageSize: page.size }, { a1, a2 })
      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.statisDateBegin = tool.formatterDate(timeRange[0], 'yyyyMMdd')
        params.statisDateEnd = tool.formatterDate(timeRange[1], 'yyyyMMdd')
      }

      delete params.timeRange
      this.cacheQueryListParams = params
      this.tableLoading = true
      get178009054Page(params).then(res => {
        const { code, data } = res

        if (code == 200) {
          const { records, total, current } = data

          // 处理不满意指标
          this.tableDatas = records || []
          this.page.total = total
          this.page.current = current
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    resetForm(formName) {
      this.page.current = 1
      this.$refs[formName].resetFields()
      this.formInline.a1 = ''
      this.formInline.a2 = ''
      this.queryList()
      // this.csmcustomerlableSeleted = []
      // this.formInline.timeRange = []
      // this.formInline.content = ''
      // this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },

    getTableHeight() {
      return document.body.offsetHeight - 268 + 'px';
    }

  }

}
</script>

<style >
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>

<style lang='scss' scoped>

.msgdialog /deep/.el-form-item__label{
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog /deep/.el-checkbox__label{
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
/deep/.el-dialog__header{
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
/deep/.el-dialog__headerbtn{
  top:12px;
  line-height: 20px;

}
/deep/.el-select .el-input.is-focus .el-input__inner{
  border-color: #ff9900;
}
/deep/.el-textarea__inner:focus{
  border-color: #ff9900;
}
.reportforms{
        background-color: rgba(242, 242, 242, 1);
        // min-height: 100vh;
        /deep/.el-input-group__append, /deep/.el-input-group__prepend{
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            // padding: 0 50px;

            /deep/.el-tabs__nav-wrap::after{
              height:1px;
              background:#E6E6E6;
            }
            /deep/.el-tabs__active-bar{
              background:#FF9900;
            }
            /deep/.el-tabs__item.is-active{
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            /deep/.el-tabs__item{
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            /deep/.el-form-item.el-form-item--medium{margin-right:20px}
            /deep/.el-form-item__content .el-input-group{
              vertical-align: middle;
            }
            /deep/.el-pagination{text-align: right;}
            .speformitem /deep/.el-form-item__label{
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        /deep/.el-icon-arrow-down:before {
          content: "\E6E1";

        }
       /deep/.el-icon-arrow-down{
         transform: rotate(180deg);
       }
       /deep/.is-reverse.el-icon-arrow-down{
         transform: rotate(0deg);
       }

  }

    }

 /deep/.el-table--medium .el-table__cell{
   padding:8px 0;
 }
 /deep/.el-table th.el-table__cell.is-leaf,
/deep/.el-table td.el-table__cell{
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  //border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}

</style>
