<template>
  <div>
    <div class="formbox" style="position:relative">
      <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
        <el-form-item label="修复数据来源渠道" prop="dataSource">
          <el-select v-model="formInline.dataSource" clearable placeholder="请选择" :size="conditionsize">
            <el-option
              v-for="item in data_source"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="网格" prop="gridId">
          <el-cascader
            v-model="formInline.gridId"
            :options="optionsCascader"
            :size="conditionsize"
            clearable
            :props="{
              checkStrictly:true
            }"
            :show-all-levels="false"
          />
        </el-form-item>
        <el-form-item label="是否集团客户" prop="ifGroup">
          <el-select v-model="formInline.ifGroup" clearable placeholder="请选择" :size="conditionsize">
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="客群属性" prop="customersAttributeList">
          <el-select
          style="width:360px"
          v-model="formInline.customersAttributeList"
          multiple
          clearable
          collapse-tags
          placeholder="请选择">
            <el-option
              v-for="item in customersAttributeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接触方式" prop="attributeStyle">
          <el-select v-model="formInline.attributeStyle" placeholder="请选择" clearable :size="conditionsize">
            <!-- <el-option label="短信群发(电渠)" value="短信群发(电渠)" />
            <el-option label="在线ivr" value="在线ivr" />
            <el-option label="在线人工" value="在线人工" />
            <el-option label="网格通" value="网格通" />
            <el-option label="装维app" value="装维app" /> -->
            <el-option v-for="i in attribute_style" :key="i" :label="i" :value="i" />

          </el-select>
        </el-form-item>
        <el-form-item v-if="impActiveName!='满意'" label="得分" prop="score">
          <!-- 自己选择 -->
          <el-select v-model="formInline.score[0]" placeholder="请选择" size="small" clearable @change="scoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select v-model="formInline.score[1]" placeholder="请选择" size="small" clearable @change="scoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>

        <el-form-item label="客户号码" prop="servNumber">
          <el-input
            v-model="formInline.servNumber"
            :size="conditionsize"
            clearable
            placeholder="请输入客户号码搜索"
          >
            <!-- <el-button slot="append" @click="query">搜索</el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item label="集团客户名称" prop="groupCusName">
          <el-input
            v-model="formInline.groupCusName"
            :size="conditionsize"
            style="width:200px"
            clearable
            placeholder="请输入集团客户名称搜索"
          >
            <!-- <el-button slot="append" @click="query">搜索</el-button> -->
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button :size="conditionsize" type="primary" @click="queryList">查询</el-button>
          <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
        </el-form-item>

      </el-form>

    </div>
    <div class="conbox" style="background:#fff;">
      <div>
        <el-table
          v-loading="tableLoading"
          :data="tableDatas"
          style="width: 100%"
          max-height="560"
        >
          <el-table-column
            v-for="item in columnList"
            :key="item.name"
            :prop="item.key"
            :label="item.name"
            :width="item.width?item.width:'unset'"
            align="center"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="操作"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <div class="optionbtnbox">
                <div>
                  <span class="coloryellow" @click="openCheckDetail(scope.row)">查看</span>
                </div>
                <div>

                  <el-popconfirm
                    confirm-button-text="删除"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除本条数据？"
                    @confirm="dele(scope.row)"
                  >
                    <span slot="reference" class="coloryellow">删除</span>
                  </el-popconfirm>

                </div>

              </div>
            </template>
          </el-table-column>

        </el-table>

      </div>

    </div>
    <!-- 分页功能 -->
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        v-if="tableDatas.length"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>

    <el-dialog title="查看" :visible.sync="dialogTableVisible" :append-to-body="true" width="75%" @close="closeDetailDialog">
      <div style="padding-bottom:10px">
        <span class="la">修复时间：</span>
        <span class="vl" style="margin-right:40px">{{ activeRow.repairTime||'-' }}</span>
        <span class="la">电话号码：</span>
        <span class="vl">{{ activeRow.servNumber||'-' }}</span>
      </div>
      <el-table v-loading="detailTableLoading" :data="dialogTableData">
        <el-table-column
          v-for="item in detailColumnList"
          :key="item.name"
          :prop="item.key"
          :label="item.name"
          :width="item.width?item.width:'unset'"
          align="center"
          :show-overflow-tooltip="true"
        />

      </el-table>
      <!-- 详情的分页功能 -->
      <div style="padding:10px 0;background:#fff;text-align:right">
        <el-pagination
          v-if="dialogTableData.length"
          :current-page="detailPage.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="detailPage.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="detailPage.total"
          @size-change="detailSizeChange"
          @current-change="detailPageCurrentChange"
        />
      </div>

    </el-dialog>

  </div>
</template>
<script>
import { getAllGrides, getFieldEnumImp, queryReinsuranceCusInfos, deleteCsmSingle, viewSingleCustomerList, getCustomersAttribute } from '@/api/customer/index'

export default {
  props: {
    impActiveName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      dialogTableData: [],
      dialogTableVisible: false,
      tableDatas: [],
      tableLoading: false,
      conditionsize: 'small',
      //          city  地市
      // district 区县
      // grid 网格
      optionsCascader: [],
      customersAttributeOptions: [],
      formInline: {
        customersAttributeList: [],
        satisfiedType: this.impActiveName,
        ifGroup: '',
        dataSource: '',
        attributeStyle: '',
        groupCusName: '',
        gridId: [],
        servNumber: '',
        score: []

      },
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      detailPage: {
        'currentPage': 1,
        'pageSize': 10,
        'total': 0
      },
      optionsCascaderExpand: [],
      columnList: [
        // { key: 'id', name: 'ID' },
        { key: 'servNumberJia', name: '客户号码', width: 120 },
        // { key: 'userTellPhone', name: '客户告知号码' },
        { key: 'cusAttribute', name: '分数(客户属性)', width: 120 },
        { key: 'attributeStyle', name: '接触方式', width: 120 },
        { key: 'appraiseTime', name: '参评时间', width: 160 },
        // { key: 'repairTime', name: '修复时间' },
        // { key: 'customersAttribute', name: '客群属性' },
        { key: 'city', name: '归属地市', width: 100  },
        { key: 'district', name: '归属区县', width: 100  },
        { key: 'grid', name: '归属网格', width: 100  },
        { key: 'customersAttribute', name: '客群属性', width: 100 },
        // { key: 'ifGroup', name: '是否集团客户' },
        { key: 'groupCusName', name: '集团客户名称', width: 100 },
        // { key: 'cusMangerName', name: '客户经理姓名' },
        { key: 'storageTime', name: '入库时间', width: 160 },
        { key: 'dataSource', name: '修复来源渠道', width: 120 },
        // { key: 'dataSourceData', name: '数据来源日期', width: 120 },
        { key: 'satisfiedType', name: '满意度类型', width: 110 },
        { key: 'ifzb', name: '是否重保', width: 80 }
        // { key: 'statisDate', name: '统计周期' }
      ],
      detailColumnList: [
        { key: 'userTellPhone', name: '客户告知号码' },
        { key: 'cusAttribute', name: '分数(客户属性)' },
        { key: 'attributeStyle', name: '接触方式' },
        { key: 'appraiseTime', name: '参评时间' },
        { key: 'repairTime', name: '修复时间' },
        { key: 'storageTime', name: '入库时间' },
        { key: 'dataSource', name: '修复来源渠道' },
        // { key: 'dataSourceData', name: '数据来源日期' },
        { key: 'satisfiedType', name: '满意度类型' }

      ],
      detailTableLoading: false,
      activeRow: {},
      attribute_style: [],
      customers_attribute: [],
      data_source: [],
      optionsPoints: [
        { label: '0分', value: '0' },
        { label: '1分', value: '1' },
        { label: '2分', value: '2' },
        { label: '3分', value: '3' },
        { label: '4分', value: '4' },
        { label: '5分', value: '5' },
        { label: '6分', value: '6' },
        { label: '7分', value: '7' },
        { label: '8分', value: '8' },
        { label: '9分', value: '9' },
        { label: '10分', value: '10' }
      ]
    }
  },
  watch: {
    impActiveName(v, oldv) {
      this.formInline.score = []
      this.formInline.customersAttributeList = []
      if (v == '不满意') {
        this.optionsPoints = [
          { label: '0分', value: '0' },
          { label: '1分', value: '1' },
          { label: '2分', value: '2' },
          { label: '3分', value: '3' },
          { label: '4分', value: '4' },
          { label: '5分', value: '5' },
          { label: '6分', value: '6' }
        ]
      } else if (v == '中立') {
        this.optionsPoints = [
          { label: '7分', value: '7' },
          { label: '8分', value: '8' },
          { label: '9分', value: '9' }
        ]
      } else {
        this.optionsPoints = []
      }
      this.queryList()
    }
  },
  created() {
    this.getCascaderOptions()
    this.getCustomersAttributeOptions()
    this.getParamsOpts('attribute_style')
    this.getParamsOpts('data_source')
  },

  mounted() {
    this.query()
  },

  methods: {
    // 获取字段的枚举值下拉
    getParamsOpts(a) {
      getFieldEnumImp(a).then(res => {
        const { data } = res
        if (data && Array.isArray(data)) {
          this[a] = data
        }
      })
    },
    closeDetailDialog() {
      this.detailPage = {
        'currentPage': 1,
        'pageSize': 10,
        'total': 0
      }
      this.dialogTableData = []
    },
    // 删除
    dele(row) {
      deleteCsmSingle(row).then(res => {
        const { code, data } = res
        if (code == 200) {
          this.query()
        }
      })
    },
    queryList() {
      this.page.current = 1
      this.query()
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        city: null,
        cityName: null,
        district: null,
        districtName: null,
        grid: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        // 后端反馈只能传中文
        if (level == 1) {
          obj.city = tar.label
          obj.cityName = tar.value
        } else if (level == 2) {
          obj.district = tar.label
          obj.districtName = tar.value
        } else if (level == 3) {
          obj.grid = tar.label
          obj.gridName = tar.value
        }
      }

      return obj
    },
    query() {
      const { impActiveName } = this
      const { page, formInline } = this
      const { current, size } = page
      console.log('current:', current)
      const { gridId = [], score = [] } = formInline
      const gridObj = this.getAllGridPathObj(gridId)
      if (score && score.length == 2) {
        if (score[0] > score[1]) {
          this.$message.error('最低得分必须小于或等于最高得分')
          return
        }
      }
      const params = Object.assign({}, formInline, { pageSize: size, currentPage: current, satisfiedType: impActiveName, startScore: score[0] || '', endScore: score[1] || '' }, { city: gridObj.city || '', district: gridObj.district || '', grid: gridObj.grid || '' })
      delete params.gridId
      delete params.score
      console.log(params)
      this.tableLoading = true
      queryReinsuranceCusInfos(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, size, current } = data || { records: [] }
          records.forEach(i => {
            if (i.servNumber && i.servNumber.length > 10) {
              const strNum = i.servNumber
              i.servNumberJia = strNum.substr(0, 3) + '****' + strNum.substr(7)
            }
          })
          this.tableDatas = records
          this.page = Object.assign(this.page, { total, size, current })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetForm(formName) {
      this.page.total = 0
      this.page.size = 10
      this.page.current = 1
      this.formInline.gridId = []
      this.formInline.customersAttributeList = []
      this.$refs[formName].resetFields()
      this.queryList()
    },
    openCheckDetail(row) {
      this.dialogTableVisible = true
      this.activeRow = row
      this.getDetailTableList()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1

      this.query()
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.query()
    },
    detailSizeChange(v) {
      this.detailPage.pageSize = v
      this.detailPage.currentPage = 1
      this.getDetailTableList()
    },
    detailPageCurrentChange(v) {
      this.detailPage.currentPage = v
      this.getDetailTableList()
    },
    // 获取详情列表
    getDetailTableList() {
      const { activeRow, detailPage } = this
      const params = Object.assign({}, { servNumber: activeRow.servNumber }, detailPage)
      this.detailTableLoading = true
      viewSingleCustomerList(params).then(res => {
        const { code, data } = res
        const { records, total } = data
        if (code == 200) {
          this.dialogTableData = records || []
          this.detailPage.total = total
        }
      }).finally(() => {
        this.detailTableLoading = false
      })
    },
    scoreChange() {
      const { score } = this.formInline
      if (Array.isArray(score) && score.length == 2) {
        if (Number(score[0]) > Number(score[1])) {
          this.$message.error('最低得分必须小于或者等于最高得分')
        }
      }
    },
    // 获取客群属性下拉选项
    getCustomersAttributeOptions() {
      getCustomersAttribute().then(res => {
        if (Array.isArray(res)) {
          this.customersAttributeOptions = res.map(i => {
            return {
              label: i,
              value: i
            }
          })
        } else {
          this.customersAttributeOptions = []
        }
      })
    },
    // 获取所有网格
    getCascaderOptions() {
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityName')// 后端要求传中文
          this.optionsCascader = arr
          this.handlerCascaderDataExpand(arr)
        }
      })
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i && i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    }

  }
}
</script>
<style lang="scss" scoped>
  .el-cascader {
        :deep(.el-icon-arrow-down:before ){
          content: "\E6E1";
        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }

  }
  .optionbtnbox{
  display: flex;
  >div{
    flex:1;
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
.la{
    font-size: 14px;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
    padding-right: 5px;
    font-weight: bold;
}
.vl{
   font-weight: bold;
}

:deep(.el-dialog__header){
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn){
  top:12px;
  line-height: 20px;

}
</style>
