<template>
  <div class="customer-trajectories" v-loading="loading">
    <el-form
      inline
      label-suffix="："
      :size="conditionsize"
      label-width="100px"
      style="text-align: right"
    >
      <el-form-item label="电话">
        <el-input
          v-model="query.phoneNumber"
          placeholder="请输入电话"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="时间粒度">
        <el-radio-group v-model="query.dateType">
          <el-radio :label="1">日</el-radio>
          <el-radio :label="2">月</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="选择时间">
        <el-date-picker
          v-if="query.dateType==2"
          v-model="query.date"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM"
          @change="handleDateChange"
          clearable
        ></el-date-picker>
        <el-date-picker
          v-else-if="query.dateType==1"
          v-model="query.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="customer-detail">
      <div class="customer-intro">
        <img
          class="customer-ava"
          src="../../../../../assets/customer/ava-male.png"
          alt=""
          v-if="customerDetails.gender == 0"
        />
        <img
          class="customer-ava"
          src="../../../../../assets/customer/ava-female.png"
          alt=""
          v-if="customerDetails.gender == 1"
        />
        <div class="customer-name">
          <span>{{ customerDetails.name }}</span>
        </div>
        <div class="base-info">
          <p>基本信息</p>
          <div class="base-line1">
            <span style="margin-right: 30px">{{
              customerDetails.gender == 0 ? "男" : "女"
            }}</span>
            <span style="margin-right: 30px">{{ customerDetails.age }}岁</span>
            <span>{{
              customerDetails.phoneType
                ? phoneTypeList.find(
                    (it) => it.value == customerDetails.phoneType
                  ).label
                : ""
            }}</span>
          </div>
          <div class="base-line2">
            <span style="margin-right: 30px"
              >手机号码：{{ customerDetails.phoneNumber }}</span
            >
            <span>网龄：{{ customerDetails.internetAge }}年</span>
          </div>
          <div class="base-line3">
            <span style="margin-right: 30px"
              >归属地：{{ customerDetails.phoneOfCity }}</span
            >
          </div>
        </div>
        <div class="customer-tag">
          <p>客户标签</p>
          <div class="tag-item" v-for="(item, index) in tagList" :key="index">
            {{ item }}
          </div>
        </div>
      </div>
      <div class="customer-map">
        <p class="map-title">客户旅程地图</p>
        <el-empty description="暂无数据" v-if="!customerMap.length"></el-empty>
        <customerMap :customerMap="customerMap" :key="timeStamp"></customerMap>
      </div>
    </div>
  </div>
</template>

<script>
import { getTrack } from "@/api/customer/index";
import customerMap from "./customerMap.vue";
export default {
  name: "customerTrajectories",
  props: ["conditionsize"],
  components: { customerMap },
  data() {
    return {
      query: {
        phoneNumber: "",
        date: [],
        dateType:1,
      },
      gender: "male",
      customerDetails: {},
      customerMap: [],
      phoneTypeList: [
        { value: 0, label: "全球通" },
        { value: 1, label: "动感地带" },
        { value: 2, label: "神州行" },
      ],
      tagList: [],
      timeStamp: "",
      loading: false,
    };
  },
  methods: {
    getCustomer() {
      let params = {
        phoneNumber: this.query.phoneNumber,
        startTime:
          this.query.date && this.query.date.length ? this.query.date[0] : "",
        endTime:
          this.query.date && this.query.date.length ? this.query.date[1] : "",
      };
      this.loading = true;
      getTrack(params)
        .then((res) => {
          if (res.code == 200) {
            this.customerDetails = JSON.parse(
              JSON.stringify(res.data.csmCustomerMsg)
            );
            this.customerMap = JSON.parse(
              JSON.stringify(res.data.customerLogs)
            );
            this.tagList = this.customerDetails.label.split("&");
            this.timeStamp = new Date().getTime();
          } else {
            this.$message.error("查询失败");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    queryData() {
      this.getCustomer();
    },
    reset() {
      this.query = {
        phoneNumber: "",
        date: [],
        dateType:1,
      };
    },
    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        const startMonth = new Date(dates[0]);
        const endMonth = new Date(dates[1]);

        // 获取结束月份的最后一天
        const lastDay = new Date(
          endMonth.getFullYear(),
          endMonth.getMonth() + 1,
          0,
          23,
          59,
          59
        );
        this.query.date = [
          startMonth.toISOString().split("T")[0],
          lastDay.toISOString().split("T")[0],
        ];
      }
    },
  },
  mounted() {
    this.getCustomer();
  },
};
</script>

<style lang="scss" scoped>
.customer-trajectories {
  .customer-detail {
    width: 100%;
    padding: 10px;
    height: 60vh;
    border: 1px solid #ccc;
    background: #fff;
    display: flex;
    .customer-intro {
      width: 25%;
      border-right: 1px solid #ccc;
      overflow-y: auto;
      .customer-ava {
        width: 100px;
        height: 100px;
        position: relative;
        transform: translateX(-50px);
        left: 50%;
      }
      .customer-name {
        text-align: center;
        font-size: 16px;
        font-weight: 700;
      }
      .base-info {
        padding: 0 20px;
        font-size: 14px;
        p {
          font-size: 16px;
          font-weight: 700;
        }
        .base-line2,
        .base-line3 {
          margin-top: 10px;
        }
      }
      .customer-tag {
        padding: 0 20px;
        font-size: 14px;
        p {
          font-size: 16px;
          font-weight: 700;
        }
        .tag-item {
          width: 45%;
          display: inline-block;
          margin-right: 2.5%;
          height: 30px;
          line-height: 30px;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          background: #f5f5f5;
          border-radius: 2px;
        }
      }
    }
    .customer-map {
      width: 75%;
      padding: 0 20px;
      overflow-y: auto;
      p {
        font-size: 16px;
        font-weight: 700;
      }
    }
  }
}
</style>