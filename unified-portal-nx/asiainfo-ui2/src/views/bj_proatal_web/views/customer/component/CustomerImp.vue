<template>
  <div>
    <div class="formbox" style="position:relative;background:#fff;padding:10px;padding-bottom:32px;">
      <el-form ref="queryForm" :inline="true"  :model="formInline" class="demo-form-inline" label-position="right">
        <el-form-item label="客户号码" prop="phoneNo">
          <el-input
            v-model="formInline.phoneNo"
            :size="conditionsize"
             clearable
            placeholder="请输入手机号进行搜索"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="分数" prop="score">

          <el-select style="width:100px" v-model="formInline.score[0]" placeholder="请选择" size="small" clearable @change="scoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select style="width:100px" v-model="formInline.score[1]" placeholder="请选择" size="small" clearable @change="scoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>
        <!-- 电渠短信,在线ivr外呼,在线人工外呼(固定宽带),在线人工外呼(移动业务),装维app -->
        <el-form-item label="接触方式" prop="attributeStyle">
          <el-select v-model="formInline.attributeStyle" placeholder="请选择" clearable :size="conditionsize">
            <el-option label="电渠短信" value="电渠短信" />
            <el-option label="在线ivr外呼" value="在线ivr外呼" />
            <el-option label="在线人工外呼(固定宽带)" value="在线人工外呼(固定宽带)" />
            <el-option label="在线人工外呼(移动业务)" value="在线人工外呼(移动业务)" />
             <el-option label="装维app" value="装维app" />
            <!-- <el-option label="第三方调研" value="第三方调研" />
            <el-option label="网格通修复" value="网格通修复" />
            <el-option label="装维app修复->上门修复" value="装维app修复->上门修复" />
            <el-option label="装维app修复->电话修复" value="装维app修复->电话修复" /> -->
          </el-select>
        </el-form-item>

        <el-form-item label="满意度类型" prop="satisfiedType">
          <el-select v-model="formInline.satisfiedType" placeholder="请选择" clearable :size="conditionsize">
            <el-option label="中立" value="中立" />
            <el-option label="不满意" value="不满意" />
            <el-option label="满意" value="满意" />
          </el-select>
        </el-form-item>

        <el-form-item label="客户时机" prop="easyVisit">
          <el-select v-model="formInline.easyVisit" placeholder="请选择" clearable :size="conditionsize">
            <el-option label="易受访" value="易受访" />
            <el-option label="不易受访" value="不易受访" />
          </el-select>
        </el-form-item>

        <el-form-item label="预测不满指标" prop="csmcustomerlableSeleted">
          <el-cascader
            v-model="csmcustomerlableSeleted"
            :options="treeLabelData"
            :size="conditionsize"
            :props="cascaderProps"
            collapse-tags
            popper-class="treeLabelDataCascader"
            :show-all-levels="false"
            clearable
          />
        </el-form-item>



         <el-form-item label="显性不满指标" prop="unsatisfiedReason">
        <el-select v-model="formInline.unsatisfiedReason" clearable>
          <el-option label="手机网络不满" value="手机网络不满"></el-option>
          <el-option label="家宽不满" value="家宽不满"></el-option>
          <el-option label="营销资费不满" value="营销资费不满"></el-option>
          <el-option label="电视不满" value="电视不满"></el-option>
          <el-option label="微信公众号、中国移动APP不满" value="微信公众号、中国移动APP不满"></el-option>
          <el-option label="其他不满" value="其他不满"></el-option>

        </el-select>
        </el-form-item>

        <el-form-item label="参评时间" prop="appraiseTime">
          <el-date-picker
          :picker-options="pickOptions"
            v-model="formInline.appraiseTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始参评时间"
            end-placeholder="结束参评时间"
            :size="conditionsize"
            style="width:250px"
          />
        </el-form-item>

        <el-form-item label="归属地市/区县/网格" prop="gridId">
          <el-cascader
            v-model="formInline.gridId"
            :options="optionsCascader"
            :size="conditionsize"
            clearable
            :props="{
              checkStrictly:true
            }"
            :show-all-levels="false"
          />
        </el-form-item>
        <el-form-item label="客群属性" prop="customersAttributeList">
          <el-select
            v-model="formInline.customersAttributeList"
            :size="conditionsize"

            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in customersAttributeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="集团客户名称" prop="groupCusName">
          <el-input
            v-model="formInline.groupCusName"
            :size="conditionsize"
            placeholder="请输入集团客户名称"
          />
        </el-form-item>

        <el-form-item label="修复时间" prop="repairTime">
          <el-date-picker
          :picker-options="pickOptions"
            v-model="formInline.repairTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始修复时间"
            end-placeholder="结束修复时间"
            :size="conditionsize"
            style="width:250px"
          />
        </el-form-item>

        <!-- <el-form-item label="修复得分" prop="repairScore">

          <el-select style="width:100px" v-model="formInline.repairScore[0]" placeholder="请选择" size="small" clearable @change="repairScoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select style="width:100px" v-model="formInline.repairScore[1]" placeholder="请选择" size="small" clearable @change="repairScoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item> -->

         <el-form-item label="修复得分" prop="repairScore">

          <el-select multiple  v-model="formInline.repairScore" placeholder="请选择" size="small" clearable>
            <el-option
              v-for="item in optionsPointsx"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="修复来源渠道" prop="dataSource">
          <el-select v-model="formInline.dataSource" clearable placeholder="请选择" :size="conditionsize">
            <el-option
              v-for="item in data_source"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="升级投诉概率" prop="upgradeComplaintRate">
          <el-select v-model="formInline.upgradeComplaintRate" clearable>
            <el-option label="低概率" value="1"></el-option>
            <el-option label="一般" value="2"></el-option>
            <el-option label="高概率" value="3"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="短信业务" prop="smsBusi">
          <el-select v-model="formInline.smsBusi" clearable>
            <el-option label="短信" value="1"></el-option>
            <el-option label="彩信" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="客户关系" prop="customerRelaion">
          <el-input
            v-model="formInline.customerRelaion"
            :size="conditionsize"
            placeholder="请输入客户关系"
          />
        </el-form-item>

         <el-form-item label="客户标签" prop="labels">
            <el-button @click="openLabelsChoice" size="small"><i class="el-icon-circle-plus-outline"/></el-button>
        </el-form-item>

        <el-form-item>
          <el-button :size="conditionsize" type="primary" @click="queryList">查询</el-button>
          <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
<!--          <el-button :size="conditionsize" type="primary" @click="resetForm('queryForm')">下载</el-button>-->
        </el-form-item>

      </el-form>
      <div v-if="showQueryLabels.length">
       <span style="font-size: 14px;
    color: #606266;font-weight:bold">选择的客户标签有：</span>


       <div>
        <el-tag type="info" style="margin:5px"  closable size="mini" v-for="i in showQueryLabels" :key="i"  @close="handleClose(i)">{{i}}</el-tag>
       </div>


      </div>

        <div class="btnbox">

                <el-button :size="conditionsize" type="default" @click="drawer=true">生成客群</el-button>

        </div>

    </div>
     <div class="conbox" style="background:#fff;position:relative">
      <div>
        <el-table
          v-loading="tableLoading"
          :data="tableDatas"
          style="width: 100%"
          max-height="560"
        >
          <el-table-column
            v-for="item in columnList"
            :key="item.name"
            :prop="item.key"
            :label="item.name"
            :width="item.width?item.width:'unset'"
             :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
            align="center"
            :show-overflow-tooltip="true"
          >
           <template slot-scope="scope">

                    <!-- 不满指标 低于6分显示红  -->
                    <span v-if="item.key=='csmcustomerlableVisibleTxt'">
                      <span v-for="(x,ix) in scope.row[item.key]" :key="ix">
                        <div v-if="x.point>-1">
                          <!-- <span>{{ scope.row[item.key].length }}</span> -->
                          <span :style="{color:0<x.point<7?'':''}">{{ x.txt }}</span>
                          <span v-show="ix!==scope.row[item.key].length-1">{{ '、' }}</span>
                        </div>
                      </span>

                    </span>

                    <span v-else>{{ scope.row[item.key] }}</span>
                  </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <div class="optionbtnbox">
                <div>
                  <span class="coloryellow" @click="clickCheck(scope.row)">查看</span>
                </div>
                <!-- <div>

                  <el-popconfirm
                    confirm-button-text="删除"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除本条数据？"
                    @confirm="dele(scope.row)"
                  >
                    <span slot="reference" class="coloryellow">删除</span>
                  </el-popconfirm>

                </div> -->

              </div>
            </template>
          </el-table-column>

        </el-table>

      </div>

    </div>
    <!-- 分页功能 -->
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        v-if="tableDatas.length"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>

    <el-dialog title="" :show-close="false"  :visible.sync="checkDialogVis" fullscreen :append-to-body="true" class="detaildialog">
      <Check @close="checkDialogVis=false" v-if="checkDialogVis" :check-row="checkRow" :colum-names-map="columNamesMap" :colum-names-point-map="columNamesPointMap" />
    </el-dialog>
     <el-dialog title="请选择客户标签进行查询" :show-close="true" @closed="closed"  :visible.sync="labelsDiaVis" width="60vw" :append-to-body="true" >

        <div style="max-height:65vh;width:100%;min-height:30vh;overflow:auto;position:relative" v-if="labelsDiaVis">

        <el-checkbox-group v-model="formInline.labels"  :size="conditionsize">

            <!-- <el-checkbox style="margin-bottom:15px" v-for="(i,idx) in labelsConst" :key="`${idx}`" :label="i.value"  name="labels2">{{ i.label }}
            </el-checkbox> -->

           <!-- [{className:'性别'},children:[]] -->
           <div style="margin-bottom:10px;" v-for="(j,idx) in labelsConstClass" :key="idx">
              <div style="font-weight:bold;font-size:14px;">{{j.className}}</div>
              <div >
                <div class="liner"></div>
              </div>
              <div style="margin-top:15px;padding-left:25px;">
                 <el-checkbox style="margin-bottom:15px" v-for="(i,idx) in j.children" :key="`-${idx}`" :label="i.value"  name="labels2">{{ i.label }}
            </el-checkbox>
              </div>

           </div>

        </el-checkbox-group>

        </div>
          <span slot="footer" class="dialog-footer">
    <el-button :size="conditionsize" @click="cancelLabels">取 消</el-button>
    <el-button :size="conditionsize" type="primary" @click="confirmLabels">确 定</el-button>
  </span>
    </el-dialog>
      <el-drawer
      :visible.sync="drawer"
      size="60%"
      :append-to-body="true"
    >
      <div slot="title">
        <span style="font-weight:bold;font-size:18px;color:black;">生成客群</span>
      </div>
      <customerAddForm v-if="drawer" :csmcustomerlable-opt="csmcustomerlableOpt" :tree-data="treeLabelData" :options-cascader="optionsCascader" @closedrawer="drawer=false" />
    </el-drawer>
  </div>
</template>
<script>
import { getColumNames, labelLists, getAllGrides, getColumScore, zbPage,delId,getLabels } from '@/api/customer/index'
import tool from '@/views/bj_proatal_web/utils/utils'
import Check from '@/views/bj_proatal_web/views/customer/component/detailInfo.vue'
import customerAddForm from '@/views/bj_proatal_web/views/customer/component/createV2.vue'

export default {
components:{Check,customerAddForm},
  data() {
    return {
      csmcustomerlableOpt:[],
      drawer:false,
      tableLoading: false,
      treeLabelData: [],
      cascaderProps: {
        multiple: true,
        checkStrictly: true
      },
        pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      conditionsize: 'small',
      csmcustomerlableSeleted: [], // 不满指标
      labelsConstClass:[
         {children:[{label:'dsfjs',value:'sfke'}],className:'性别'},
          {children:[{label:'dsfjs3',value:'sfk3e'}],className:'性别2'},
        ], //客户分类标签
      // 客群属性
      customersAttributeOptions: [
        { label: '固定电话', value: '固定电话' },
        { label: '固定宽带', value: '固定宽带' },
        { label: '移动业务', value: '移动业务' }
      ],
      labelsDiaVis:false,
      formInline: {
        repairScore:[],
        unsatisfiedReason:'',
        customersAttributeList: '', // 客群属性
        phoneNo: '',
        score: [], // 分数

        attributeStyle: '', // 接触方式
        satisfiedType: '', // 满意度类型
        appraiseTime: [], // 参评时间
        gridId: [], // 地市区县网格
        groupCusName: '', // 集团客户名称
        repairTime: [], // 修复时间
        dataSource: '',
        labels:[] //客户标签

      },

      showQueryLabels:[],
      // 网格通修复,装维app修复->上门修复，装维app修复->电话修复
      data_source: [

        '网格通修复','装维app修复->上门修复','装维app修复->电话修复'

      ],
      columNamesPointMap: {},
      columNamesMap: {},
      optionsCascader: [],
      optionsCascaderExpand: [],
      optionsPoints: [
        { label: '0分', value: '0' },
        { label: '1分', value: '1' },
        { label: '2分', value: '2' },
        { label: '3分', value: '3' },
        { label: '4分', value: '4' },
        { label: '5分', value: '5' },
        { label: '6分', value: '6' },
        { label: '7分', value: '7' },
        { label: '8分', value: '8' },
        { label: '9分', value: '9' },
        { label: '10分', value: '10' }
      ],
      // 10分. 7-9分 1-6分 其他
      optionsPointsx:[
       { label: '10分', value: '10-10' },
        { label: '7-9分', value: '7-9' },
        { label: '1-6分', value: '1-6' },
        { label: '其他', value: '0-0' },


      ],

      // 客户号码 预测不满指标 显示不满指标 分数 修复得分 接触方式
      // 满意度类型 集团客户名称 客群属性 地市 区县 网格 参评时间 修复时间 修复来源渠道

      columnList:[
        {key:'phoneNoShow',name:'客户号码',width:'130'},
        {key:'csmcustomerlableVisibleTxt',name:'预测不满指标'},
         {key:'unsatisfiedReason',name:'显性不满指标',width:'130'},
        {key:'cusAttribute',name:'分数'},
        {key:'repairScore',name:'修复得分'},
        {key:'attributeStyle',name:'接触方式',width:'130'},
        {key:'satisfiedType',name:'满意度类型'},
        {key:'groupCusName',name:'集团客户名称'},
        {key:'customersAttribute',name:'客群属性'},
        {key:'cityName',name:'地市'},
        {key:'countyName',name:'区县'},
        {key:'gridName',name:'网格'},
        {key:'appraiseTime',name:'参评时间',width:'110'},
        {key:'repairTime',name:'修复时间',width:'110'},
        {key:'dataSource',name:'修复来源渠道'},
      ],
      tableDatas:[],

      page: {
        current: 1,
        size: 10,
        total: 0
      },
      checkDialogVis:false,
      checkRow:null,
      labelsConst:[] //客户标签

    }
  },
  created() {
    // 获取每个指标对应的不满意的值
    getColumScore().then(res => {
      const { code, data } = res
      if (code == 200) {
        this.columNamesPointMap = data
        this.init()
      }
    })
    getLabels().then((res)=>{
        let tempObj={}
        let labelsConstClass = []
        let {code,data} = res;
        data.forEach(i=>{
          i.value = i.labelId;
          i.label = i.labelName||i.label;
          if(!tempObj[i.secondLabelType]){
            tempObj[i.secondLabelType] = [i]
          }else{
            tempObj[i.secondLabelType].push(i)
          }

        })

        let tempObjkeys = Object.keys(tempObj);
        console.log('sssss')
        tempObjkeys.forEach(j=>{

          tempObj[j].sort((a,b)=>{
            if(a['value']>b['value']){
              return 1;
            }else if(a['value']<b['value']){
              return -1;
            }else {
              return 0;
            }
          })

          console.log(tempObj[j])

          let x={
            className:j,
            children:tempObj[j]
          }
          labelsConstClass.push(x)
        })


        this.labelsConstClass = labelsConstClass||[]

        this.labelsConst = data||[]

    })

  },

  methods: {
    handleClose(tag) {
      console.log('tag==>',tag);
        let {labels} = this.formInline;
        let {labelsConst} = this;
        this.showQueryLabels.splice(this.showQueryLabels.indexOf(tag), 1);
        let target = null;
        labelsConst.forEach((i,idx)=>{
          console.log('i=>',i)
          if(i.label==tag){
            target = i.value;
          }
        })

        labels.splice(labels.indexOf(target), 1);
      },
    closed(){
      if(!this.showQueryLabels.length){
        this.formInline.labels = []
        this.showQueryLabels = []
      }
    },
    confirmLabels(){
      let {labelsConst} = this;
      let {labels}=this.formInline;
      let showQueryLabels = []
      if(labels.length){
        labels.forEach(i=>{
          labelsConst.forEach(j=>{
            if(i==j.value){
              showQueryLabels.push(j.label)
            }
          })
        })
      }
    this.showQueryLabels = showQueryLabels;
    this.labelsDiaVis = false
    },
      sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1

      this.query()
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.query()
    },
    cancelLabels(){
        this.labelsDiaVis = false;
        this.showQueryLabels = []
        this.formInline.labels=[]
    },
    openLabelsChoice(){
        this.labelsDiaVis = true;
    },
    queryList() {
      this.page.current = 1
      this.query()
    },
    resetForm(formName) {
      this.page.total = 0
      this.page.size = 10
      this.page.current = 1
      this.csmcustomerlableSeleted = []
      this.formInline.gridId = []
      this.formInline.labels=[]
      this.showQueryLabels = []
      this.formInline.repairScore = []
      this.formInline.customersAttributeList = ''
      this.$refs[formName].resetFields()
      this.queryList()
    },
      clickCheck(row) {
      this.checkDialogVis = true
      // row.phoneNo='15825339698'
      this.checkRow = row
      console.log('row:', row)
    },
    query() {
      let columFiles = []
       let params = {}
      const { page, formInline, csmcustomerlableSeleted } = this
      const { current, size } = page
      const { appraiseTime, repairTime, gridId , score = [],repairScore=[] } = formInline

      const gridObj = this.getAllGridPathObj(gridId)


      console.log('gridObj=>', gridObj)
      if (score && score.length == 2) {

        if (Number(score[0]) > Number(score[1])) {
          this.$message.error('最低得分必须小于或等于最高得分')
          return
        }
        params.beginCusAttribute = score[0]||''
         params.endCusAttribute = score[1]||''



      }
      //   if (repairScore && repairScore.length == 2) {
      //   if (Number(repairScore[0]) > Number(repairScore[1])) {
      //     this.$message.error('最低修复得分必须小于或等于最高修复得分')
      //     return
      //   }
      //   params.beginRepairScore = repairScore[0]||'';
      //   params.endRepairScore = repairScore[1]||'';
      // }


      if (csmcustomerlableSeleted.length) {
        columFiles = csmcustomerlableSeleted.map(j => {
          const len = j.length
          return j[len - 1]
        })
      }
       params = Object.assign({},params, formInline,{pagination:{pageSize: size, currentPage: current}} ,{

      },
        { cityId: gridObj.cityName || '', countyId: gridObj.districtName || ''})
      params.gridId =  gridObj.gridName || ''

     if (appraiseTime && appraiseTime.length && Array.isArray(appraiseTime)) {
        params.beginAppraiseTime = tool.formatterDate(appraiseTime[0], 'yyyy-MM-dd')
        params.endAppraiseTime = tool.formatterDate(appraiseTime[1], 'yyyy-MM-dd')
        params.beginAppraiseTime = `${params.beginAppraiseTime} 00:00:00`
         params.endAppraiseTime = `${params.endAppraiseTime} 23:59:59`
      }

      if (repairTime && repairTime.length && Array.isArray(repairTime)) {
        params.beginRepairTime = tool.formatterDate(repairTime[0], 'yyyy-MM-dd')
        params.endRepairTime = tool.formatterDate(repairTime[1], 'yyyy-MM-dd')
        params.beginRepairTime = `${params.beginRepairTime} 00:00:00`
         params.endRepairTime = `${params.endRepairTime} 23:59:59`
      }

      params.columFiles = columFiles || []

      console.log('params=>', params)

      delete params.score
      // delete params.repairScore
      delete params.repairTime
      delete params.appraiseTime
      this.tableLoading = true
        zbPage(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total } = data
          // 处理不满意指标
          const { columNamesMap = {}, columNamesPointMap = {}} = this
          records.forEach(i => {
            i.csmcustomerlableVisibleTxt = []
            for (const ke in i) {
              if (columNamesMap.hasOwnProperty(ke) && columNamesPointMap.hasOwnProperty(ke) && i[ke] && i[ke] < columNamesPointMap[ke] && i[ke] != -1) {
                let strobj = { txt: '', point: '' }
                strobj = {
                  txt: `${columNamesMap[ke]}(${i[ke]}) `,
                  point: i[ke] }
                console.log(strobj)
                i.csmcustomerlableVisibleTxt.push(strobj)
              }
            }
            let strNum = i.phoneNo;
            if(strNum)  i.phoneNoShow = strNum.substr(0, 3) + '****' + strNum.substr(7)

          })

          this.tableDatas = records || []
          this.page.total = total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        city: null,
        cityName: null,
        district: null,
        districtName: null,
        grid: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        // 后端反馈只能传中文
        if (level == 1) {
          obj.city = tar.label
          obj.cityName = tar.value
        } else if (level == 2) {
          obj.district = tar.label
          obj.districtName = tar.value
        } else if (level == 3) {
          obj.grid = tar.label
          obj.gridName = tar.value
        }
      }

      return obj
    },
    init() {
      const p0 = labelLists()// 获取所有标签tree
      const p1 = getColumNames()// 获取与标签对应的map

      Promise.all([p0, p1]).then(values => {
        const res0 = values[0]
        const res1 = values[1]

        let { code, data } = res0
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }

        if (res1.code == 200) {
          this.columNamesMap = res1.data
        }
      }).finally(()=>{
         this.queryList();
      })
      // 获取所有网格1
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr
          this.handlerCascaderDataExpand(arr)
        }
      })

    },
    scoreChange() {
      const { score } = this.formInline

      if (Array.isArray(score) && score.length == 2) {
        if (Number(score[0]) > Number(score[1])) {
          this.$message.error('最低得分必须小于或者等于最高得分')
        }
      }
    },
     dele(row) {

        delId(row.id).then(res => {
        console.log(row)
        const { code, data } = res
        if (code == 200) {
          this.query()
        }
      })
    },
    repairScoreChange() {
      const { repairScore } = this.formInline
      if (Array.isArray(repairScore) && repairScore.length == 2) {
        if (Number(repairScore[0]) > Number(repairScore[1])) {
          this.$message.error('最低修复得分必须小于或者等于最高修复得分')
        }
      }
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i && i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    }
  }
}
</script>
<style lang="scss" scoped>
  .el-cascader {
        :deep(.el-icon-arrow-down:before ){
          content: "\E6E1";
        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }

  }
 .optionbtnbox{
  display: flex;
  >div{
    flex:1;
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
.la{
    font-size: 14px;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
    padding-right: 5px;
    font-weight: bold;
}
.vl{
   font-weight: bold;
}

.liner{
    width:150px;
    height:4px;
    background:linear-gradient(90deg,rgba(253,151,33,0.9),rgba(253,151,33,0.6) , rgba(253,151,33,0.2),rgba(253,151,33,0));
}

:deep(.el-dialog__header){
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn){
  top:12px;
  line-height: 20px;

}
 .btnbox{
          position: absolute;
          right:20px;
          bottom: 22px;
        }
.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
</style>

