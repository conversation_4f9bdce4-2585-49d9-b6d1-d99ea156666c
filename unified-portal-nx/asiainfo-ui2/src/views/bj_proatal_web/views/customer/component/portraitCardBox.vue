<template>
  <div :class="{cardbox:true,green:theme=='green',blue:theme=='blue'}" :style="{width:widthconf,height:heightconf}">
    <div class="title">
      {{ title }}
    </div>
    <div>
      <slot name="cardcontent" />
    </div>

  </div>
</template>

<script>
export default {
  props: {
    theme: {
      type: String,
      default: () => 'green'
    },
    title: {
      type: String,
      default: () => ''
    },
    widthconf: {
      type: String,
      default: () => '360px'
    },
    heightconf: {
      type: String,
      default: () => '180px'
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.cardbox{
    min-width:360px;
    min-height:180px;
    padding:10px 15px 5px 15px;
    font-size: 14px;
    position: relative;
    &.green{
        background:url('../../../../../assets/images/green.png') no-repeat;
        background-size: cover;
        .title{
            background:rgb(33,177,138)
        }

    }
    &.blue{
          background:url('../../../../../assets/images/blue.png') no-repeat;
        background-size: cover;
        .title{
            background:rgb(21,120,227)
        }
    }
}
.title{
    text-align: center;
    color:white;
    font-weight: bold;
    line-height: 20px;
    font-size: 14px;
}
</style>

