<template>
  <div v-loading="submitLoading" class="customerAddForm">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="140px" class="demo-ruleForm">
      <el-form-item label="客群名称:" prop="customerName" >
        <el-input v-model="ruleForm.customerName" style="width:80%" size="mini" />
      </el-form-item>
      <el-form-item label="归属地市/区县/网格:" prop="gridId">
        <el-cascader
          v-model="ruleForm.gridId"
          style="width:80%"
          :options="optionsCascader"
          size="mini"
          clearable
          :props="{
            checkStrictly:true
          }"
          @change="handleChangeCascader"
        />
      </el-form-item>

      <!-- <el-form-item label="预测不满指标:" prop="csmcustomerlableSeleted" >
        <el-cascader
          v-model="ruleForm.csmcustomerlableSeleted"
          style="width:80%"
          :options="treeData"
          :props="cascaderProps"
          popper-class="treeLabelDataCascader"
          :show-all-levels="false"
          clearable
        />
      </el-form-item> -->

    <el-form-item label="满意度类型:" prop="satisfiedType">
        <el-select v-model="ruleForm.satisfiedType" clearable style="width:80%" placeholder="请选择" size="small">
          <el-option label="满意用户" value="满意" />
          <el-option label="中立用户" value="中立" />
          <el-option label="不满客户" value="不满意" />
        </el-select>
      </el-form-item>


        <el-form-item label="显性不满指标" prop="unsatisfiedReason">
          <el-select v-model="ruleForm.unsatisfiedReason" clearable style="width:80%">
            <el-option label="手机网络不满" value="手机网络不满"></el-option>
            <el-option label="家宽不满" value="家宽不满"></el-option>
            <el-option label="营销资费不满" value="营销资费不满"></el-option>
            <el-option label="电视不满" value="电视不满"></el-option>
            <el-option label="微信公众号、中国移动APP不满" value="微信公众号、中国移动APP不满"></el-option>
            <el-option label="其他不满" value="其他不满"></el-option>

          </el-select>
        </el-form-item>

        <el-form-item label="客户时机" prop="easyVisit">
          <el-select v-model="ruleForm.easyVisit" placeholder="请选择" clearable :size="conditionsize" style="width:80%">
            <el-option label="易受访" value="易受访" />
            <el-option label="不易受访" value="不易受访" />
          </el-select>
        </el-form-item>

      <el-divider />
      <div class="subt">客户属性</div>

      <el-form-item  label="客户号码:" prop="phoneNo">
        <el-input v-model="ruleForm.phoneNo" size="mini" style="width:200px;"></el-input>
      </el-form-item>

        <el-form-item label="接触方式:" prop="attributeStyle">
        <el-select v-model="ruleForm.attributeStyle" clearable style="width:80%" placeholder="请选择" size="small">
         <el-option label="电渠短信" value="电渠短信" />
            <el-option label="在线ivr外呼" value="在线ivr外呼" />
            <el-option label="在线人工外呼(固定宽带)" value="在线人工外呼(固定宽带)" />
            <el-option label="在线人工外呼(移动业务)" value="在线人工外呼(移动业务)" />
             <el-option label="装维app" value="装维app" />
        </el-select>
      </el-form-item>

      <div class="morebox">
        <el-form-item label="分数:" prop="satisfaction">
          <el-select  v-model="ruleForm.satisfaction[0]" placeholder="请选择" size="small" @change="satisfactionLowChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select  v-model="ruleForm.satisfaction[1]" placeholder="请选择" size="small" @change="satisfactionLowChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>

      </div>


        <el-form-item label="参评时间" prop="appraiseTime">
          <el-date-picker
            v-model="ruleForm.appraiseTime"
            :picker-options="pickOptions"
            type="daterange"
            range-separator="至"
            start-placeholder="开始参评时间"
            end-placeholder="结束参评时间"
            :size="conditionsize"
            style="width:250px"
          />
        </el-form-item>



      <el-divider />
       <!-- <el-radio-group ref="moretxt1" id="moretxt1"  size="mini" :style="{height:moretxt1status?'unset':moretxt1DyHeight,overflow:moretxt1status?'visible':'hidden'}">
            <el-radio v-for="(i,idx) in moretxt1Arr" :key="`${idx}moretxt1`" border :label="i.value">{{ i.label }}</el-radio>
          </el-radio-group> -->
      <!-- <div class="subt">客户标签</div>
       <div class="morebox">
        <el-form-item label="公共标签:" >
          <el-checkbox-group ref="moretxt1" id="moretxt1" v-model="ruleForm.labels1" :style="{height:moretxt1status?'unset':moretxt1DyHeight,overflow:moretxt1status?'visible':'hidden'}" size="mini" @change="checkboxChange">
            <el-checkbox v-for="(i,idx) in moretxt1Arr" :key="`${idx}moretxt1`" :label="i.value" border name="labels1">{{ i.label }}
            </el-checkbox>
          </el-checkbox-group>

        </el-form-item>
        <div v-show="moretxt1vis" class="moretxt" @click="moretxt1status=!moretxt1status">{{ moretxt1status?'收起':'展开' }}>>></div>
      </div>

      <div class="morebox">
        <el-form-item label="模型标签:" >
          <el-checkbox-group ref="moretxt2" id="moretxt2" v-model="ruleForm.labels2" :style="{height:moretxt2status?'unset':moretxt2DyHeight,overflow:moretxt2status?'visible':'hidden'}" size="mini" @change="checkboxChange2">
            <el-checkbox v-for="(i,idx) in moretxt2Arr" :key="`${idx}moretxt2`" :label="i.value" border name="labels2">{{ i.label }}
            </el-checkbox>
          </el-checkbox-group>

        </el-form-item>
        <div v-show="moretxt2vis" class="moretxt" @click="moretxt2status=!moretxt2status">{{ moretxt2status?'收起':'展开' }}>>></div>
      </div> -->




     <el-form-item label="客户标签" >
          <el-button @click="openLabelsChoice" size="small"><i class="el-icon-circle-plus-outline"/></el-button>
      </el-form-item>

        <el-form-item label="选择的客户标签有：" v-if="showQueryLabels.length">
            <div v-if="showQueryLabels.length">

       <span>
        <el-tag type="info" closable @close="handleClose(i)" style="margin:5px" size="mini" v-for="i in showQueryLabels" :key="i">{{i}}</el-tag>
       </span>
      </div>
      </el-form-item>





      <el-form-item label="集团客户名称" prop="groupCusName">
          <el-input
             style="width:80%"
            v-model="ruleForm.groupCusName"
            :size="conditionsize"
            placeholder="请输入集团客户名称"
          />
        </el-form-item>



      <el-divider />
       <el-form-item label="客群属性" prop="customersAttributeList">
          <el-select
            v-model="ruleForm.customersAttributeList"
            style="width:260px"
            :size="conditionsize"

            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in customersAttributeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

          <el-form-item label="修复时间" prop="repairTime">
          <el-date-picker
            v-model="ruleForm.repairTime"
            :picker-options="pickOptions"
            type="daterange"
            range-separator="至"
            start-placeholder="开始修复时间"
            end-placeholder="结束修复时间"
            :size="conditionsize"
            style="width:250px"
          />
        </el-form-item>

          <!-- <el-form-item label="修复得分" prop="repairScore">

          <el-select v-model="ruleForm.repairScore[0]" placeholder="请选择" size="small" clearable @change="repairScoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select v-model="ruleForm.repairScore[1]" placeholder="请选择" size="small" clearable @change="repairScoreChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item> -->

        <el-form-item label="修复得分" prop="repairScore">

          <el-select multiple  v-model="ruleForm.repairScore" placeholder="请选择" size="small" clearable>
            <el-option
              v-for="item in optionsPointsx"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="修复来源渠道" prop="dataSource">
          <el-select v-model="ruleForm.dataSource" clearable placeholder="请选择" :size="conditionsize">
            <el-option
              v-for="item in data_source"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="升级投诉概率" prop="upgradeComplaintRate">
          <el-select v-model="ruleForm.upgradeComplaintRate" clearable>
            <el-option label="低概率" value="1"></el-option>
            <el-option label="一般" value="2"></el-option>
            <el-option label="高概率" value="3"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="短信业务" prop="smsBusi">
          <el-select v-model="ruleForm.smsBusi" clearable>
            <el-option label="短信" value="1"></el-option>
            <el-option label="彩信" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="客户关系" prop="customerRelaion">
          <el-input
            v-model="ruleForm.customerRelaion"
            :size="conditionsize"
            placeholder="请输入客户关系"
            style="width:80%"
          />
        </el-form-item>


      <el-form-item style="text-align:right">
        <el-button size="mini" type="primary" @click="submitForm('ruleForm')">生成</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
        <el-button size="mini" @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
    <el-dialog title="请选择客户标签" :show-close="true" @closed="closed" :visible.sync="labelsDiaVis" width="60vw" :append-to-body="true" >
        <div style="max-height:65vh;width:100%;min-height:30vh;overflow:auto" v-if="labelsDiaVis">
          <el-checkbox-group v-model="ruleForm.labels"  size="small">
            <!-- <el-checkbox style="margin-bottom:15px;" v-for="(i,idx) in labelsConst" :key="`${idx}${i.value}`" :label="i.value"  name="labels2">{{ i.label }}
            </el-checkbox> -->

              <div style="margin-bottom:10px;" v-for="(j,idx) in labelsConstClass" :key="idx">
              <div style="font-weight:bold;font-size:14px;">{{j.className}}</div>
              <div >
                <div class="liner"></div>
              </div>
              <div style="margin-top:15px;padding-left:25px;">
                 <el-checkbox style="margin-bottom:15px" v-for="(i,idx) in j.children" :key="`-${idx}`" :label="i.value"  name="labels2">{{ i.label }}
            </el-checkbox>
              </div>

           </div>





        </el-checkbox-group>
        </div>
          <span slot="footer" class="dialog-footer">
    <el-button size="small" @click="cancelLabels">取 消</el-button>
    <el-button size="small" type="primary" @click="confirmLabels">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
import { zbcreateCustomer,getLabels } from '@/api/customer/index'
import tool from '@/views/bj_proatal_web/utils/utils'

export default {
  props: {
    // csmcustomerlableOpt: {
    //   type: Array,
    //   required: true
    // },
    optionsCascader: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    treeData: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }

  },
  data() {
    return {
        pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      labelsConst:[],
      conditionsize:'small',
        customersAttributeOptions: [
        { label: '固定电话', value: '固定电话' },
        { label: '固定宽带', value: '固定宽带' },
        { label: '移动业务', value: '移动业务' }
      ],
      canSet:false,
      optionsCascaderExpand: [],
      submitLoading: false,
      cascaderProps: {
        multiple: true,
        checkStrictly: true
      },
      conditionSize: 'small',
      data_source: [

        '网格通修复','装维app修复->上门修复','装维app修复->电话修复'

      ],
         optionsPointsx:[
       { label: '10分', value: '10-10' },
        { label: '7-9分', value: '7-9' },
        { label: '1-6分', value: '1-6' },
        { label: '其他', value: '0-0' },


      ],
      labelsConstClass:[],

      ruleForm: {
        // exponentialLevel: '-1',
        csmcustomerlableSeleted: [],
        labels1:[],
        labels2:[],
        unsatisfiedReason:'',
        labels:[],
        // isComplaint: '-1',
        // repairStatus: ['-1'],
        satisfaction: [],
        gridId: [],
        customerName: '',
        satisfiedType:'',//满意度类型
        attributeStyle:'',
        type:'',//客群类别
        phoneNo:'',//客户号码
        appraiseTime:'',//参评时间
        customersAttributeList:'',//客群属性
        groupCusName:'',
        repairTime:[],
        repairScore:[],
        dataSource:'',


      },
      rules: {
        customerName: [
          { required: true, message: '请输入客群名称', trigger: 'blur' }
        ],

        // gridId: [
        //   { required: false, message: '请选择网格', trigger: 'change' }
        // ],

        csmcustomerlableSeleted: [
          { type: 'array', required: false, message: '请选择指标名称', trigger: 'change' }
        ],

        satisfaction: [{ required: false, message: '请选择得分', trigger: 'change' }],



      },

      labelsDiaVis:false,
      moretxt1DyHeight: 'unset',
      moretxt1status: 0, // 1 展开 0 隐藏 2不出现更多
      moretxt1vis: false,
      moretxt1Arr: [],
      moretxt2DyHeight: 'unset',
      moretxt2status: 0, // 1 展开 0 隐藏 2不出现更多
      moretxt2vis: false,
      moretxt2Arr: [],
      showQueryLabels:[],
      // 得分
      optionsPoints: [
        { label: '0分', value: '0' },
        { label: '1分', value: '1' },
        { label: '2分', value: '2' },
        { label: '3分', value: '3' },
        { label: '4分', value: '4' },
        { label: '5分', value: '5' },
        { label: '6分', value: '6' },
        { label: '7分', value: '7' },
        { label: '8分', value: '8' },
        { label: '9分', value: '9' },
        { label: '10分', value: '10' }
      ]

    }
  },

  watch: {
    'ruleForm.csmcustomerlableSeleted': {
      deep: true,
      handler(v, oldv) {

      }
    },
    // csmcustomerlableOpt: {
    //   deep: true,
    //   handler(v, oldv) {
    //     const tar = v.filter(i => i.value == '-1')
    //     if (!tar.length) {
    //       v.unshift({ label: '不限', value: '-1' })
    //     }
    //     this.moretxt1Arr = v
    //     console.log('moretxt1Arr=>',v);
    //     this.getClientHeight('moretxt1')
    //   }
    // }

  },
  created(){
    let moretxt1Arr = [{ id:'-1',labelType:"1",labelId:'-1' ,label:'不限',value:'-1'}]
    let moretxt2Arr = [{ id:'-1',labelType:"1",labelId:'-1' ,label:'不限',value:'-1'}]
    this.submitLoading = true;
    getLabels().then(res=>{
      let {code,data} = res;
      // if(code==200){
      //   data.forEach(i=>{
      //     i.value = i.labelId;
      //     i.label = i.labelName||i.label;
      //     if(i.labelType=='1'||false){
      //       moretxt1Arr.push(i)
      //     }else if(i.labelType == '2'||true){
      //       moretxt2Arr.push(i)
      //     }
      //   })
      //   this.moretxt1Arr = moretxt1Arr;
      //   this.getClientHeight('moretxt1')
      //   this.moretxt2Arr = moretxt2Arr
      //   this.getClientHeight('moretxt2')
      // }

       if(code==200){
        let tempObj={}
        let labelsConstClass = []
        let {code,data} = res;
        data.forEach(i=>{
          i.value = i.labelId;
          i.label = i.labelName||i.label;
          if(!tempObj[i.secondLabelType]){
            tempObj[i.secondLabelType] = [i]
          }else{
            tempObj[i.secondLabelType].push(i)
          }
        })

        let tempObjkeys = Object.keys(tempObj);
        tempObjkeys.forEach(j=>{

          tempObj[j].sort((a,b)=>{
            if(a['value']>b['value']){
              return 1;
            }else if(a['value']<b['value']){
              return -1;
            }else {
              return 0;
            }
          })

          let x={
            className:j,
            children:tempObj[j]
          }
          labelsConstClass.push(x)
        })
        this.labelsConstClass = labelsConstClass||[]

        this.labelsConst = data||[]
      }


    }).finally(()=>{this.submitLoading=false})
  },
  mounted() {
    console.log('this.optionsCascader:', this.optionsCascader)
    this.handlerCascaderDataExpand(this.optionsCascader)
    //  this.moretxt1Arr = this.csmcustomerlableOpt||[]
    //   this.getClientHeight('moretxt1')



  },
  methods: {
     handleClose(tag) {
      console.log('tag==>',tag);
        let {labels} = this.ruleForm;
        let {labelsConst} = this;
        this.showQueryLabels.splice(this.showQueryLabels.indexOf(tag), 1);
        let target = null;
        labelsConst.forEach((i,idx)=>{
          console.log('i=>',i)
          if(i.label==tag){
            target = i.value;
          }
        })

        labels.splice(labels.indexOf(target), 1);
      },
    closed(){
      if(!this.showQueryLabels.length){
        this.ruleForm.labels = []
        this.showQueryLabels = []
      }
    },
     cancelLabels(){
        this.labelsDiaVis = false;
        this.showQueryLabels = [];
        this.ruleForm.labels=[]
    },
      confirmLabels(){
      let {labelsConst} = this;
      let {labels}=this.ruleForm;
      let showQueryLabels = []
      if(labels.length){
        labels.forEach(i=>{
          labelsConst.forEach(j=>{
            if(i==j.value){
              showQueryLabels.push(j.label)
            }
          })
        })
      }
    this.showQueryLabels = showQueryLabels;
    this.labelsDiaVis = false
    },
    openLabelsChoice(){
        this.labelsDiaVis = true;
    },
      repairScoreChange() {
      const { repairScore } = this.ruleForm
      if (Array.isArray(repairScore) && repairScore.length == 2) {
        if (Number(repairScore[0]) > Number(repairScore[1])) {
          this.$message.error('最低修复得分必须小于或者等于最高修复得分')
        }
      }
    },

    checkboxChange(v) {
      if (v[v.length - 1] != '-1') {
        this.ruleForm.labels1 = v.filter(i => i != '-1')
      } else {
        this.ruleForm.labels1 = ['-1']
      }
    },
    checkboxChange2(v){
      if (v[v.length - 1] != '-1') {
        this.ruleForm.labels2 = v.filter(i => i != '-1')
      } else {
        this.ruleForm.labels2 = ['-1']
      }
    },



    cancel() {
      this.$emit('closedrawer')
    },
    // 计算高度元素高度
    getClientHeight(reflabel) {
      const self = this
      this.$nextTick(() => {
        const elHeight = document.getElementById(reflabel).offsetHeight
        console.log('elHeight=>',elHeight)
        if (elHeight > 42) {
          this[`${reflabel}vis`] = true
          this[`${reflabel}DyHeight`] = '42px'
        }
      })
    },
    // 处理级联数据 展开平铺
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    satisfactionLowChange() {
      const { satisfaction } = this.ruleForm
      console.log('satisfaction:', satisfaction)
      if (Array.isArray(satisfaction) && satisfaction.length == 2) {
        if (Number(satisfaction[0]) > Number(satisfaction[1])) {
          this.$message.error('最低得分必须小于最高得分')
        }
      }
    },

    handleChangeCascader() {

    },
    submitForm(formName) {
       if(!this.ruleForm.customerName){
            this.$message.error('请输入客群名称')
            return
          }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let columFiles = []
          let params = {}
          const { customerName,csmcustomerlableSeleted,labels1,labels2,labels,satisfiedType,
          satisfaction,type,phoneNo,attributeStyle,appraiseTime,groupCusName,repairTime,
          dataSource,repairScore,customersAttributeList,unsatisfiedReason} = this.ruleForm



          // 处理网格
          const gridObj = this.getAllGridPathObj(this.ruleForm.gridId)// 网格处理好了
          console.log('gridObj:', gridObj)
          // 处理指标名称
          if (csmcustomerlableSeleted.length) {
            columFiles = csmcustomerlableSeleted.map(j => {
              const len = j.length
              return j[len - 1]
            })

          }

          // 处理标签
          // let labels = [...labels1.filter(k=>k!='-1'),...labels2.filter(x=>x!='-1')];
          // if(labels.length) params.labels=labels;

          // 处理得分
          console.log('satisfaction=>',satisfaction)
          if (Array.isArray(satisfaction) && satisfaction.length == 2) {
            if (Number(satisfaction[0]) > Number(satisfaction[1])) {
              this.$message.error('最小得分必须小于最大得分')
              return
            }
            params.beginCusAttribute = satisfaction[0]
            params.endCusAttribute = satisfaction[1]
          }

            // 处理修复得分
          // if (Array.isArray(repairScore) && repairScore.length == 2) {
          //   if (Number(repairScore[0]) > Number(repairScore[1])) {
          //     this.$message.error('最小得分必须小于最大得分')
          //     return
          //   }
          //   params.beginRepairScore = repairScore[0]
          //   params.endRepairScore = repairScore[1]
          // }

          //
            // 参评时间
           if (appraiseTime && appraiseTime.length && Array.isArray(appraiseTime)) {
        params.beginAppraiseTime = tool.formatterDate(appraiseTime[0], 'yyyy-MM-dd')
        params.endAppraiseTime = tool.formatterDate(appraiseTime[1], 'yyyy-MM-dd')
        params.beginAppraiseTime = `${params.beginAppraiseTime} 00:00:00`
         params.endAppraiseTime = `${params.endAppraiseTime} 23:59:59`
      }

      if (repairTime && repairTime.length && Array.isArray(repairTime)) {
        params.beginRepairTime = tool.formatterDate(repairTime[0], 'yyyy-MM-dd')
        params.endRepairTime = tool.formatterDate(repairTime[1], 'yyyy-MM-dd')
        params.beginRepairTime = `${params.beginRepairTime} 00:00:00`
         params.endRepairTime = `${params.endRepairTime} 23:59:59`
      }

          const userInfo = JSON.parse(localStorage.getItem('userInfo'))
          params = Object.assign(
            {},
            params,
             { cityId: gridObj.cityId || '', countyId: gridObj.countyId || '', gridId: gridObj.gridId || '' },
            {  customerName ,satisfiedType,phoneNo,attributeStyle,
            customersAttributeList,unsatisfiedReason,repairScore,
            groupCusName,dataSource,labels},
            {
              createUserName: userInfo.name,
              createUserId: userInfo.loginName
            }
          )
          delete params['-1']
          Object.keys(params).forEach(key => {
            if (params[key] == -1 && key != 'customerName') {
              delete params[key]
            }
          })
          params.columFiles = columFiles || []
          console.log('params:', params)
          this.submitLoading = true
          zbcreateCustomer(params).then(res => {
            console.log('res=>',res)
            if (res.code == 200) {
              this.$message.success('创建客群成功')
              this.$emit('closedrawer')
            }

          }).finally(() => {
            this.submitLoading = false
          }).catch(err=>{
            console.log(err)
             this.$message.warning(err.msg)

          })
        } else {
          return false
        }
      })
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        cityId: null,
        cityName: null,
        countyId: null,
        countyName: null,
        gridId: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        if (level == 1) {
          obj.cityId = tar.value
          obj.cityName = tar.label
        } else if (level == 2) {
          obj.countyId = tar.value
          obj.countyName = tar.label
        } else if (level == 3) {
          obj.gridId = tar.value
          obj.gridName = tar.label
        }
      }

      return obj
    },
    resetForm(formName) {
      this.showQueryLabels = []
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.customerAddForm{
    padding:0 20px;
}
.el-cascader {
        :deep(.el-icon-arrow-down:before) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }
       :deep(.el-input .el-input__inner){
           line-height: 32px;
           height: 32px;
       }

  }


:deep(.el-radio.is-bordered.is-disabled){
background:#F5F7FA;
}
//  :deep(.el-checkbox.is-bordered.is-checked{
//     background:#FF9900;
//     border-color: #FF9900;

//   }
//   :deep(.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__label{

// font-size: 14px;

//   }
//   :deep(.el-checkbox.is-bordered.el-checkbox--mini{
//    margin-right: 0px;
//     padding: 3px 8px;
//     margin-top: 6px;
//     height: 24px;
//     line-height: 18px;
//   }

//   :deep(.el-checkbox__input.is-checked + .el-checkbox__label{
//     color:#262626;

//   }
//   :deep(.el-checkbox__inner{display:none}
//   :deep(.el-checkbox__label{
//     padding:0;
//     font-size: 14px;

// font-weight: 400;

// color: #595959;

// letter-spacing: -0.27px;
//   }

  :deep(.el-form-item__label){

    font-size: 14px;
    font-weight: 400;

    text-align: right;
    color: #262626;

    letter-spacing: -0.27px;
  }
:deep(.el-form-item.el-form-item--medium){
  margin-bottom: 22px;
}

:deep(.el-radio--mini.is-bordered){
      margin-right: 0px;
    padding: 3px 8px;
    margin-top: 6px;
    height: 24px;
    line-height: 18px;
    margin-bottom: 10px;

  }
  :deep(.el-radio--mini.is-bordered .el-radio__label){
    font-size:14px;
    color:#262626;
    font-weight: 400;
    padding-left: 0;
    text-align: center;

  }
  :deep(.el-radio__input){display:none}
  :deep(.el-radio.is-bordered.is-checked){
     background:#FF9900;
    border-color: #FF9900;
  }

  .subt{
      margin-bottom: 15px;
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #262626;
      line-height: 21px;
      letter-spacing: -0.27px;
  }
  .morebox{
      position: relative;
      padding-right:50px;
      margin-bottom: 22px;

  }
  .moretxt{
      position: absolute;
      right:0;
      top:0;
      color:#595959;
      font-size: 12px;
      line-height: 42px;
      &:hover{
          color:#FF9900;
          cursor:pointer;
      }
  }
  .liner{
    width:150px;
    height:4px;
    background:linear-gradient(90deg,rgba(253,151,33,0.9),rgba(253,151,33,0.6) , rgba(253,151,33,0.2),rgba(253,151,33,0));
}

</style>
