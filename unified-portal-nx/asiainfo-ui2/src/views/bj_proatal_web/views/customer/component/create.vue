<template>
  <div v-loading="submitLoading" class="customerAddForm">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm">
      <el-form-item label="客群名称:" prop="customerName" >
        <el-input v-model="ruleForm.customerName" style="width:80%" size="mini" />
      </el-form-item>
      <!-- <el-form-item label="网格:" prop="gridId">
        <el-cascader
          v-model="ruleForm.gridId"
          style="width:80%"
          :options="optionsCascader"
          size="mini"
          clearable
          :props="{
            checkStrictly:true
          }"
          @change="handleChangeCascader"
        />
      </el-form-item> -->

      <el-form-item label="指标名称:" prop="csmcustomerlableSeleted" >
        <el-cascader
          v-model="ruleForm.csmcustomerlableSeleted"
          style="width:80%"
          :options="treeData"
          :props="cascaderProps"
          popper-class="treeLabelDataCascader"
          :show-all-levels="false"
          clearable
        />
      </el-form-item>

    <el-form-item label="满意度类型:" prop="satisfiedType">
        <el-select v-model="ruleForm.satisfiedType" clearable @change="satisfiedTypeChange" style="width:80%" placeholder="请选择" size="small">
          <el-option label="满意用户" value="满意" />
          <el-option label="中立用户" value="中立" />
          <el-option label="不满客户" value="不满意" />
        </el-select>
      </el-form-item>



      <el-divider />
      <div class="subt">客户属性</div>


      <div class="morebox">
        <el-form-item label="得分:" prop="satisfaction">
          <el-select v-show="ruleForm.satisfiedType!=='满意'" :disabled="canSet" v-model="ruleForm.satisfaction[0]" placeholder="请选择" size="small" @change="satisfactionLowChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span v-show="ruleForm.satisfiedType!=='满意'" style="padding:0 10px;">至</span>
          <el-select :disabled="canSet" v-model="ruleForm.satisfaction[1]" placeholder="请选择" size="small" @change="satisfactionLowChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>
        <div v-show="moretxt2vis" class="moretxt" @click="moretxt2status=!moretxt2status">{{ moretxt2status?'收起':'展开' }}>>></div>
      </div>
       <el-form-item  label="客户按键:" prop="customerButton">
        <el-radio-group :disabled="canSet" v-model="ruleForm.customerButton" size="mini">
          <el-radio border  label="-1">不限</el-radio>
          <el-radio border  label="1">1</el-radio>
          <el-radio border  label="2">2</el-radio>
          <el-radio border  label="3">3</el-radio>
        </el-radio-group>
      </el-form-item>
       <el-form-item label="是否是集团用户:" prop="ifGroup">
        <el-radio-group v-model="ruleForm.ifGroup" size="mini" >
          <el-radio border label="-1">不限</el-radio>
          <el-radio border label="1">是</el-radio>
          <el-radio border label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-divider />
      <div class="subt">客户标签</div>
       <div class="morebox">
        <el-form-item label="公共标签:" >
          <el-checkbox-group ref="moretxt1" id="moretxt1" v-model="ruleForm.labels1" :style="{height:moretxt1status?'unset':moretxt1DyHeight,overflow:moretxt1status?'visible':'hidden'}" size="mini" @change="checkboxChange">
            <el-checkbox v-for="(i,idx) in moretxt1Arr" :key="`${idx}moretxt1`" :label="i.value" border name="labels1">{{ i.label }}
            </el-checkbox>
          </el-checkbox-group>
             <!-- <el-radio-group ref="moretxt1" id="moretxt1"  size="mini" :style="{height:moretxt1status?'unset':moretxt1DyHeight,overflow:moretxt1status?'visible':'hidden'}">
            <el-radio v-for="(i,idx) in moretxt1Arr" :key="`${idx}moretxt1`" border :label="i.value">{{ i.label }}</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <div v-show="moretxt1vis" class="moretxt" @click="moretxt1status=!moretxt1status">{{ moretxt1status?'收起':'展开' }}>>></div>
      </div>

      <div class="morebox">
        <el-form-item label="模型标签:" >
          <el-checkbox-group ref="moretxt2" id="moretxt2" v-model="ruleForm.labels2" :style="{height:moretxt2status?'unset':moretxt2DyHeight,overflow:moretxt2status?'visible':'hidden'}" size="mini" @change="checkboxChange2">
            <el-checkbox v-for="(i,idx) in moretxt2Arr" :key="`${idx}moretxt2`" :label="i.value" border name="labels2">{{ i.label }}
            </el-checkbox>
          </el-checkbox-group>
             <!-- <el-radio-group ref="moretxt2" id="moretxt2"  size="mini" :style="{height:moretxt2status?'unset':moretxt2DyHeight,overflow:moretxt2status?'visible':'hidden'}">
            <el-radio v-for="(i,idx) in moretxt2Arr" :key="`${idx}moretxt2`" border :label="i.value">{{ i.label }}</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <div v-show="moretxt2vis" class="moretxt" @click="moretxt2status=!moretxt2status">{{ moretxt2status?'收起':'展开' }}>>></div>
      </div>




      <!-- <el-divider />
      <div class="subt">客群属性</div>
       <el-form-item label="客群类别:" prop="type">
        <el-radio-group v-model="ruleForm.type" size="mini">
          <el-radio border label="0">关怀客群</el-radio>
          <el-radio border label="1">修复客群</el-radio>
        </el-radio-group>
      </el-form-item> -->


      <el-form-item style="text-align:right">
        <el-button size="mini" type="primary" @click="submitForm('ruleForm')">生成</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
        <el-button size="mini" @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { zbcreateCustomer,getLabels } from '@/api/customer/index'
export default {
  props: {
    // csmcustomerlableOpt: {
    //   type: Array,
    //   required: true
    // },
    optionsCascader: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    treeData: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }

  },
  data() {
    return {
      canSet:false,
      optionsCascaderExpand: [],
      submitLoading: false,
      cascaderProps: {
        multiple: true,
        checkStrictly: true
      },
      conditionSize: 'small',

      ruleForm: {
        // exponentialLevel: '-1',
        csmcustomerlableSeleted: [],
        labels1:[],
        labels2:[],
        // isComplaint: '-1',
        // repairStatus: ['-1'],
        satisfaction: ['0', '10'],
        gridId: [],
        customerName: '',
        satisfiedType:'',//满意度类型
        customerButton:'',//客户按键
        ifGroup:'',//是否为集团用户
        type:'',//客群类别

      },
      rules: {
        customerName: [
          { required: true, message: '请输入客群名称', trigger: 'blur' }
        ],
        customerButton: [
          { required: true, message: '请选择客户按键', trigger: 'change' }
        ],
        // gridId: [
        //   { required: false, message: '请选择网格', trigger: 'change' }
        // ],
        // 指数级别
        // exponentialLevel: [
        //   { required: true, message: '请选择指数级别', trigger: 'change' }
        // ],
        csmcustomerlableSeleted: [
          { type: 'array', required: true, message: '请选择指标名称', trigger: 'change' }
        ],

        satisfaction: [{ required: true, message: '请选择得分', trigger: 'change' }],

        // isComplaint: [
        //   { required: true, message: '请选择是否投诉', trigger: 'change' }
        // ],
        // repairStatus: [
        //   { required: true, message: '请选择修复状态', trigger: 'change' }
        // ]

      },

      moretxt1DyHeight: 'unset',
      moretxt1status: 0, // 1 展开 0 隐藏 2不出现更多
      moretxt1vis: false,
      moretxt1Arr: [],
      moretxt2DyHeight: 'unset',
      moretxt2status: 0, // 1 展开 0 隐藏 2不出现更多
      moretxt2vis: false,
      moretxt2Arr: [],
      // 得分
      optionsPoints: [
        { label: '0分', value: '0' },
        { label: '1分', value: '1' },
        { label: '2分', value: '2' },
        { label: '3分', value: '3' },
        { label: '4分', value: '4' },
        { label: '5分', value: '5' },
        { label: '6分', value: '6' },
        { label: '7分', value: '7' },
        { label: '8分', value: '8' },
        { label: '9分', value: '9' },
        { label: '10分', value: '10' }
      ]

    }
  },

  watch: {
    'ruleForm.csmcustomerlableSeleted': {
      deep: true,
      handler(v, oldv) {

      }
    },
    // csmcustomerlableOpt: {
    //   deep: true,
    //   handler(v, oldv) {
    //     const tar = v.filter(i => i.value == '-1')
    //     if (!tar.length) {
    //       v.unshift({ label: '不限', value: '-1' })
    //     }
    //     this.moretxt1Arr = v
    //     console.log('moretxt1Arr=>',v);
    //     this.getClientHeight('moretxt1')
    //   }
    // }

  },
  created(){
    let moretxt1Arr = [{ id:'-1',labelType:"1",labelId:'-1' ,label:'不限',value:'-1'}]
    let moretxt2Arr = [{ id:'-1',labelType:"1",labelId:'-1' ,label:'不限',value:'-1'}]
    this.submitLoading = true;
    getLabels().then(res=>{
      let {code,data} = res;
      if(code==200){
        data.forEach(i=>{
          i.value = i.labelId;
          i.label = i.labelName||i.label;
          if(i.labelType=='1'||false){
            moretxt1Arr.push(i)
          }else if(i.labelType == '2'||true){
            moretxt2Arr.push(i)
          }
        })


        this.moretxt1Arr = moretxt1Arr;
        this.getClientHeight('moretxt1')
        this.moretxt2Arr = moretxt2Arr
        this.getClientHeight('moretxt2')

      }
    }).finally(()=>{this.submitLoading=false})
  },
  mounted() {
    console.log('this.optionsCascader:', this.optionsCascader)
    this.handlerCascaderDataExpand(this.optionsCascader)
    //  this.moretxt1Arr = this.csmcustomerlableOpt||[]
    //   this.getClientHeight('moretxt1')



  },
  methods: {
    satisfiedTypeChange(v){
      // 当满意度类型做了选择后，满意（得分10分、按键1）、中立（得分7-9分、按键2）、不满意（得分1-6分、按键3
      if(v){
        this.canSet = true
        if(v==='满意'){
           this.ruleForm.satisfaction[0] = '10'
          this.ruleForm.satisfaction[1] = '10'
          this.ruleForm.customerButton = '1';
        }else if(v=='中立'){
           this.ruleForm.satisfaction[0] = '7'
          this.ruleForm.satisfaction[1] = '9'
          this.ruleForm.customerButton = '2';
        }else if(v=='不满意'){
          this.ruleForm.satisfaction[0] = '1'
          this.ruleForm.satisfaction[1] = '6'
          this.ruleForm.customerButton = '3';
        }
      }else{
        this.canSet = false
      }
    },
    checkboxChange(v) {
      if (v[v.length - 1] != '-1') {
        this.ruleForm.labels1 = v.filter(i => i != '-1')
      } else {
        this.ruleForm.labels1 = ['-1']
      }
    },
    checkboxChange2(v){
      if (v[v.length - 1] != '-1') {
        this.ruleForm.labels2 = v.filter(i => i != '-1')
      } else {
        this.ruleForm.labels2 = ['-1']
      }
    },

    cancel() {
      this.$emit('closedrawer')
    },
    // 计算高度元素高度
    getClientHeight(reflabel) {
      const self = this
      this.$nextTick(() => {
        const elHeight = document.getElementById(reflabel).offsetHeight
        console.log('elHeight=>',elHeight)
        if (elHeight > 42) {
          this[`${reflabel}vis`] = true
          this[`${reflabel}DyHeight`] = '42px'
        }
      })
    },
    // 处理级联数据 展开平铺
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    satisfactionLowChange() {
      const { satisfaction } = this.ruleForm
      console.log('satisfaction:', satisfaction)
      if (Array.isArray(satisfaction) && satisfaction.length == 2) {
        if (Number(satisfaction[0]) > Number(satisfaction[1])) {
          this.$message.error('最低得分必须小于最高得分')
        }
      }
    },

    handleChangeCascader() {

    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let columFiles = []
          let params = {}
          const { customerName,csmcustomerlableSeleted,labels1,labels2,satisfiedType, satisfaction,customerButton,type,ifGroup } = this.ruleForm
          console.log('csmcustomerlableSeleted=>',csmcustomerlableSeleted)
          // 处理网格
          // const gridObj = this.getAllGridPathObj(this.ruleForm.gridId)// 网格处理好了
          // console.log('gridObj:', gridObj)
          // 处理指标名称
          if (csmcustomerlableSeleted.length) {
            columFiles = csmcustomerlableSeleted.map(j => {
              const len = j.length
              return j[len - 1]
            })
            // csmcustomerlableSeleted.forEach(i => {
            //   params[i] = '1'
            // })
          }
          // 处理标签
          let labels = [...labels1.filter(k=>k!='-1'),...labels2.filter(x=>x!='-1')];
          if(labels.length) params.labels=labels;

          // 处理得分
          if (Array.isArray(satisfaction) && satisfaction.length == 2) {
            // const temparr = satisfaction.split(',')
            // params.beginSatisfaction = temparr[0]
            // params.endSatisfaction = temparr[1]

            if (Number(satisfaction[0]) > Number(satisfaction[1])) {
              this.$message.error('最小得分必须小于最大得分')
              return
            }

            params.beginSatisfaction = satisfaction[0]
            params.endSatisfaction = satisfaction[1]
          } else {
            this.$message.error('请选择得分')
            return
          }

          // 处理修复状态
          // let repairStatustxt = ''
          // if (Array.isArray(repairStatus) && repairStatus.length) {
          //   // 选择了不限 repairStatustxt = ''
          //   if (repairStatus.indexOf('-1') == -1) { // 没有选择不限
          //     repairStatustxt = repairStatus.join(',')
          //   }
          // } else {
          //   this.$message.error('请选择修复状态')
          //   return
          // }

          const userInfo = JSON.parse(localStorage.getItem('userInfo'))
          params = Object.assign(
            {},
            params,
            { type,ifGroup:ifGroup=='-1'?undefined:ifGroup, customerName ,satisfiedType,customerButton:customerButton=='-1'?undefined:customerButton},
            {
              createUserName: userInfo.name,
              createUserId: userInfo.loginName
            }
          )

          delete params['-1']
          Object.keys(params).forEach(key => {
            if (params[key] == -1 && key != 'customerName') {
              delete params[key]
            }
          })
          params.columFiles = columFiles || []
          console.log('params:', params)
          this.submitLoading = true
          zbcreateCustomer(params).then(res => {
            console.log('res=>',res)
            if (res.code == 200) {
              this.$message.success('创建客群成功')
              this.$emit('closedrawer')
            }

          }).finally(() => {
            this.submitLoading = false
          }).catch(err=>{

            this.$message.warning(err.msg)

          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        cityId: null,
        cityName: null,
        countyId: null,
        countyName: null,
        gridId: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        if (level == 1) {
          obj.cityId = tar.value
          obj.cityName = tar.label
        } else if (level == 2) {
          obj.countyId = tar.value
          obj.countyName = tar.label
        } else if (level == 3) {
          obj.gridId = tar.value
          obj.gridName = tar.label
        }
      }

      return obj
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.customerAddForm{
    padding:0 20px;
}
.el-cascader {
        :deep(.el-icon-arrow-down:before) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down){
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down){
         transform: rotate(0deg);
       }
       :deep(.el-input .el-input__inner){
           line-height: 32px;
           height: 32px;
       }

  }

  :deep(.el-checkbox.is-bordered.is-checked){
    background:#FF9900;
    border-color: #FF9900;

  }
  :deep(.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__label){

font-size: 14px;

  }
:deep(.el-radio.is-bordered.is-disabled){
background:#F5F7FA;
}
  :deep(.el-checkbox.is-bordered.el-checkbox--mini){
   margin-right: 0px;
    padding: 3px 8px;
    margin-top: 6px;
    height: 24px;
    line-height: 18px;
  }

  :deep(.el-checkbox__input.is-checked + .el-checkbox__label){
    color:#262626;

  }
  :deep(.el-checkbox__inner){display:none}
  :deep(.el-checkbox__label){
    padding:0;
    font-size: 14px;

font-weight: 400;

color: #595959;

letter-spacing: -0.27px;
  }

  :deep(.el-form-item__label){

    font-size: 14px;
    font-weight: 400;

    text-align: right;
    color: #262626;

    letter-spacing: -0.27px;
  }
:deep(.el-form-item.el-form-item--medium){
  margin-bottom: 22px;
}

:deep(.el-radio--mini.is-bordered){
      margin-right: 0px;
    padding: 3px 8px;
    margin-top: 6px;
    height: 24px;
    line-height: 18px;
    margin-bottom: 10px;

  }
  :deep(.el-radio--mini.is-bordered .el-radio__label){
    font-size:14px;
    color:#262626;
    font-weight: 400;
    padding-left: 0;
    text-align: center;

  }
  :deep(.el-radio__input){display:none}
  :deep(.el-radio.is-bordered.is-checked){
     background:#FF9900;
    border-color: #FF9900;
  }

  .subt{
      margin-bottom: 15px;
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #262626;
      line-height: 21px;
      letter-spacing: -0.27px;
  }
  .morebox{
      position: relative;
      padding-right:50px;
      margin-bottom: 22px;

  }
  .moretxt{
      position: absolute;
      right:0;
      top:0;
      color:#595959;
      font-size: 12px;
      line-height: 42px;
      &:hover{
          color:#FF9900;
          cursor:pointer;
      }
  }

</style>
