<template>
  <div v-loading="loading">
    <div class="con">
      <div class="imgbackgroundbox">
        <img style="width:350px;position:relative;top:-0px;" src="../../../../../assets/images/customer/8.png" alt="">
      </div>
      <div class="topcon">
        <div class="topL it">
          <!-- 基础信息 -->
          <portraitCardBox theme="green" title="客户基础信息">
            <div slot="cardcontent">
              <div class="flex">
                <div class="labelLeft">姓名:</div>
                <div :class="{value:true,name:info.userName?true:false}">{{ info.userName||'--' }}</div>
                <div class="label" style="width:20%">性别:</div>
                <div class="value" style="width:40%">{{ info.sex || '--' }}</div>
              </div>
              <div class="flex">
                <div class="labelLeft">年龄:</div>
                <div class="value">{{ info.age ||'--' }}</div>
                <div class="label" style="width:20%">在网时长:</div>
                <div class="value" style="width:40%;">{{ info.monthsInnet||'--' }}<span v-if="info.monthsInnet">月</span></div>
              </div>
              <div class="flex">
                <div class="labelLeft">归属地:</div>
                <div class="value">{{ info.regionName ||'--' }}</div>
                <div class="label" style="width:20%;">星级:</div>
                <div class="value" style="width:40%;display:flex;align-items:center;justify-content:flex-start">

                  <el-rate v-if="info.creditLevel" v-model="info.creditLevel" style="font-size:10px;" :max="Number(info.creditLevel)" disabled />
                  <span v-else> {{ '--' }}</span>

                </div>
              </div>
            </div>
          </portraitCardBox>
        </div>
        <div class="topR it">
          <!-- 近期投诉情况 -->
          <portraitCardBox theme="blue" title="近期投诉情况">
            <div slot="cardcontent">

              <div v-if="info.instrs" class="sqc">
                <div class="sqcit1">
                  <img src="../../../../../assets/images/customer/22.svg" alt="">
                </div>
                <div class="sqcit2">
                  <el-tooltip placement="top" popper-class="instrstooltip">
                    <div slot="content">{{ info.instrs }}</div>
                    <div class="ellipsis">
                      {{ info.instrs }}
                    </div>
                  </el-tooltip>
                  <div style="text-align:right;font-size:14px;font-weight:400;color:#8c8c8c;margin-top:10px;"><span>最近投诉日期:</span><span>{{ info.lastCompDate }}</span></div>
                </div>
              </div>
              <div v-else style="height:140px">
                <Blank2 />
              </div>

            </div>
          </portraitCardBox>
        </div>
      </div>
      <div class="centercon">
        <div class="it">
          <!-- 消费特征 -->
          <portraitCardBox theme="green" title="消费特征" widthconf="360px" heightconf="180px">
            <div slot="cardcontent">
              <div class="pay">
                <div>
                  <div>用户月均消费</div>
                  <div class="paysecond">
                    <div :class="{em:info.arpu?true:false}">
                      <span style="font-size:18px">{{ info.arpu||'--' }}</span>
                    </div>
                    <div class="imgb" style="padding-top:10px;">
                      <img style="width:100%" src="../../../../../assets/images/customer/20.svg" alt="">
                    </div>
                  </div>

                </div>
                <div>
                  <div>
                    用户当前套餐名称
                  </div>
                  <div class="paysecond">
                    <div :class="{em:info.tcName?true:false}">
                      <el-tooltip placement="top">
                        <div slot="content">{{ info.tcName }}</div>
                        <span class="ellipsis">{{ info.tcName||'--' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="imgb" style="padding-top:10px;text-align:right">
                      <img style="width:85%" src="../../../../../assets/images/customer/21.svg" alt="">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </portraitCardBox>
        </div>
        <div class="it">
          <!-- 近期工单情况 -->
          <portraitCardBox theme="blue" title="近期工单情况">
            <div slot="cardcontent">
              <div v-if="workOrders.length" style="width:100%;height:148px;overflow-y:auto">
                <div v-for="(i,idx) in workOrders" :key="idx" class="workitem">
                  <div class="t"><span class="point" />{{ i.title }}</div>
                  <div style="padding-left:16px;"><span style="margin-right:20px">{{ i.contentDate }}</span><span>{{ i.content }}</span></div>
                </div>
              </div>
              <div v-else style="width:100%;height:148px;">
                <Blank2 />
              </div>
            </div>
          </portraitCardBox>
        </div>
      </div>
      <div class="bottomcon">
        <div class="it">
          <!-- 产品使用情况 -->
          <portraitCardBox theme="green" title="产品使用情况">
            <div slot="cardcontent">
              <div v-if="info.cntHaltMon6||info.cntPackoutGprsMon3||info.cntPackoutVoiceMon3||info.isVoiceHandle||info.isKdTs ||info.isZnzw ||info.isTvOrder" style="width:100%;height:148px;overflow-y:auto">
                <div v-if="info.cntHaltMon6" class="useitem">
                  <div>近6个月停机次数</div>
                  <div class="useitemem">{{ info.cntHaltMon6 }}</div>
                </div>
                <div v-if="info.cntPackoutGprsMon3" class="useitem">
                  <div>近三个月流量超套次数</div>
                  <div class="useitemem">{{ info.cntPackoutGprsMon3 }}</div>
                </div>
                <div v-if="info.cntPackoutVoiceMon3" class="useitem">
                  <div>近三个月语音超套次数</div>
                  <div class="useitemem">{{ info.cntPackoutVoiceMon3 }}</div>
                </div>
                <div v-if="info.isVoiceHandle" class="useitem">
                  <div>是否订购语音遥控器</div>
                  <div class="useitemem">{{ info.isVoiceHandle }}</div>
                </div>
                <div v-if="info.isKdTs" class="useitem">
                  <div>是否订购宽带提速包</div>
                  <div class="useitemem">{{ info.isKdTs }}</div>
                </div>
                <div v-if="info.isZnzw" class="useitem">
                  <div>是否订购智能组网产品</div>
                  <div class="useitemem">{{ info.isZnzw }}</div>
                </div>
                <div v-if="info.isTvOrder" class="useitem">
                  <div>是否订购电视增值业务</div>
                  <div class="useitemem">{{ info.isTvOrder }}</div>
                </div>

              </div>
              <div v-else style="width:100%;height:148px;">
                <Blank2 />
              </div>
            </div>
          </portraitCardBox>
        </div>
        <div class="it">
          <!-- 近期调研结果 -->
          <portraitCardBox theme="blue" title="近期调研结果">
            <div slot="cardcontent">
              <div v-if="survey.length" style="width:100%;height:148px;overflow-y:auto;">
                <div v-for="(i,idx) in survey" :key="idx" class="workitem">
                  <div class="t"><span class="point" />{{ i.title }}</div>
                  <div style="padding-left:16px;"><span style="margin-right:20px">{{ i.contentDate }}</span><span>{{ i.content }}</span></div>
                </div>
              </div>
              <div v-else style="width:100%;height:148px;">
                <Blank2 />
              </div>

            </div>
          </portraitCardBox>
        </div>
      </div>

    </div>

    <!-- <div class="flexForm">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="0px" class="demo-ruleForm">
        <el-form-item label="" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入" class="input-with-select">
            <div slot="append" @click="search">
              <el-button type="primary">搜索</el-button>
            </div>
          </el-input>
        </el-form-item>
      </el-form>
    </div> -->

  </div>
</template>

<script>
import { getCustomerBaseDetailByPhoneNum, listCustomerDateDetailByPhoneNum } from '@/api/customer/index'
import portraitCardBox from './portraitCardBox.vue'

export default {
  components: { portraitCardBox },
  props: {
    checkRow: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      name: '家乐',
      ruleForm: {
        name: ''
      },
      rules: {},
      // 近期工单情况
      workOrders: [],
      // 近期调研情况
      survey: [],
      info: {}

    }
  },
  created() {
    this.query()
  },

  methods: {
    // 根据电话号码查询信息
    query() {
      this.loading = true
      const p = { phoneNum: this.checkRow.phoneNo }

      Promise.all([getCustomerBaseDetailByPhoneNum(p), listCustomerDateDetailByPhoneNum(p)]).then(resarr => {
        const res = resarr[0]
        const res1 = resarr[1]
        const { code, data } = res
        if (code == 200 && data) {
          // data.creditLevel = 10
          this.info = data
        }
        if (res1.code == 200 && res1.data) {
          this.workOrders = res1.data['近期工单情况']
          this.survey = res1.data['近期调研结果']
        }
      }).finally(() => {
        this.loading = false
      })

      // getCustomerBaseDetailByPhoneNum(p).then(res => {
      //   const { code, data } = res
      //   if (code == 200 && data) {
      //     this.info = data
      //   }
      // }).finally(() => {
      //   this.loading = false
      // })

      // listCustomerDateDetailByPhoneNum(p).then(res => {
      //   const { code, data } = res
      //   if (code == 200 && data) {
      //     this.workOrders = data['近期工单情况']
      //     this.survey = data['近期调研结果']
      //   }
      // })
    }
  }
}
</script>
<style >
.instrstooltip.el-tooltip__popper{
  max-width:500px!important;
  width:500px;
}
</style>
<style scoped lang="scss">

.ellipsis{
	     overflow: hidden;  /** 隐藏超出的内容 **/
       word-break: break-all;
       text-overflow: ellipsis; /** 多行 **/
       display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
       -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
       -webkit-line-clamp: 3; /** 显示的行数 **/
}

.point{
  width:10px;
  height:10px;
  background:rgb(21,120,227);
  display: inline-block;
  border-radius: 50%;
  margin-right:5px;
}
.workitem{
  margin-top:10px;
  padding-left: 10px;
  .t{
    font-weight: bold;
    font-size: 14px;
  }
}

.sqc{
  display: flex;
  padding-top:20px;
  .sqcit1{
    width:30%;
    img{
      width:80%;
    }
  }
  .sqcit2{
    flex:1;
    font-size: 14px;
    font-weight: bold;
  }
}
.pay{
  display: flex;
  justify-content: space-between;
  height:150px;
  font-size: 14px;
  >div{
    border:1px solid #e6e6e6;
    width: 48%;
    margin-top:10px;
    padding:10px;
    margin-bottom: 10px;
  }
}
.paysecond{
  display: flex;
  justify-content: space-between;
  .em{
    flex:1
  }
  .imgb{
    width:50px;
  }
}
.con{position: relative;}
.imgbackgroundbox{
  position: absolute;
  left:50%;
  transform: translateX(-50%);
  top:210px;
}
.topcon{
  display: flex;
  justify-content: center;
  height:200px;
  margin-bottom: 30px;
  .it{
    margin:20px 35px;
  }
}
.centercon{
  display: flex;
  justify-content: center;
  height:200px;
  .it{
    margin:0px 200px;
  }
}
.bottomcon{
  display: flex;
  justify-content: center;
  height:200px;
  .it{
    margin:0px 200px;
  }
}
.flex{
  display: flex;
  line-height:42px;
  width:100%;

  .label{
    width:20%;
    color:rgb(140, 140, 140);
    text-align: right;
  }
  .labelLeft{
    width:20%;
    text-align: right;
    color:rgb(140, 140, 140);

  }
  .value{
    width:30%;
    text-align: center;
  }
}
.name{
  font-size: 18px;
  color:rgb(33,177,138);
  font-weight: bold;

}
// 近期工单受理情况
.workOrder{
  line-height:20px;
  margin:10px 10px 18px 10px;
}
.em{
  font-weight: bold;
  font-size: 14px;
  color:black;
  margin-top:20px;
}
.useitem{
  border-left: 3px solid rgb(33,177,138);
  margin-top:15px;
  padding-left: 15px;
  font-size: 14px;
}
.useitemem{
  font-weight: bold;
  font-size: 16px;
  color:black;
}
</style>
