<template>
  <div style="position:relative;">

    <div style="position:relative;">
      <el-button style="position:absolute;right:0;top:-20px" type="default" size="small" @click="$emit('close')">返回</el-button>
      <el-radio-group v-model="pageType" size="medium">
        <el-radio-button label="repair">客户修复信息</el-radio-button>
        <el-radio-button label="portrait">客户画像</el-radio-button>
        <el-radio-button label="locus">客户轨迹</el-radio-button>
      </el-radio-group>
    </div>
    <keep-alive>
      <component :is="pageType" style="min-height:200px; margin-top:30px" v-bind="$attrs" :check-row="checkRow" :customer-data="customerData" />
    </keep-alive>

    <!-- <div style="text-align:center">
      <el-button size="small" type="default" @click="$emit('close')">关闭</el-button>
    </div> -->
  </div>
</template>
<script>
import repair from './repair.vue'
import portrait from './portrait.vue'
import locus from './locus.vue'
import { listCustomerDateDetailByPhoneNum } from '@/api/customer/index'

export default {
  components: { repair, portrait, locus },
  props: {
    visible: {
      type: Boolean,
      default: () => false
    },
    checkRow: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      pageType: 'repair',
      customerData: {
        '正常客户行为': [],
        '重点关注行为': []
      },
      IconTitle: {
        '退订信息': 'tdxx',
        '投诉': 'ts',
        '评测': 'pc',
        '用户订购': 'dg',
        '用户退订': 'tdxx',
        '用户套餐变更': 'yhxf'

      }
    }
  },
  mounted() {
    console.log(this.checkRow)
    this.queryData()
  },
  methods: {
    // 查询
    queryData() {
      const p = { phoneNum: this.checkRow.phoneNo }
      // 15825339698
      // 上线之前删除
      listCustomerDateDetailByPhoneNum(p).then(res => {
        const { code, data } = res
        if (code == 200 && data) {
          data['重点关注行为'] && data['重点关注行为'].forEach(i => {
            if (i.contentDate) {
              const arr = i.contentDate.split(' ')
              i.contentDate1 = arr[0]
              i.contentDate2 = arr[1]
              i.titltType = this.IconTitle[i.title]
            }
          })
          data['正常客户行为'] && data['正常客户行为'].forEach(i => {
            if (i.contentDate) {
              const arr = i.contentDate.split(' ')
              i.contentDate1 = arr[0]
              i.contentDate2 = arr[1]
              i.titltType = this.IconTitle[i.title]
            }
          })

          this.customerData = data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
