<template>
  <div style="background:white;padding:15px 10px;">
    <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">

      <el-form-item label="类型" prop="type">
        <el-select v-model="formInline.type" placeholder="请选择" :size="conditionsize" style="width:100px;">
          <el-option label="显性" value="1" />
          <el-option label="潜在" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="手机号码" prop="phoneNo">
        <el-input
          v-model="formInline.phoneNo"
          :size="conditionsize"
          placeholder="请输入手机号进行搜索"
        />
      </el-form-item>
      <el-form-item label="选择时间段" class="timeRange" prop="timeRange">
        <el-date-picker
          v-model="formInline.timeRange"
          style="width:250px"
          :size="conditionsize"
          type="daterange"
          popper-class="xps"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item>
        <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
        <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
      </el-form-item>

    </el-form>
    <div class="conbox">
      <div>
        <el-table
          v-loading="tableLoading"
          :data="tableDatas"
          style="width: 100%"
          max-height="560"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column
            type="selection"
            width="55"
          /> -->
          <el-table-column
            v-for="item in columnList"
            :key="item.name"
            :prop="item.key"
            :label="item.name"
            :width="item.width?item.width:'unset'"
            align="center"
            :show-overflow-tooltip="true"
            :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
          >
            <template slot-scope="scope">
              <span v-if="item.key=='type'">
                {{ scope.row[item.key]==1?'显性':scope.row[item.key]==0?'潜在':'' }}
              </span>
              <span v-else-if="item.key=='isComplaint'">
                {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
              </span>

              <span v-else-if="item.key=='isSend'">
                {{ scope.row[item.key]=='0'?'已发送':scope.row[item.key]=='1'?'未发送':'' }}
              </span>
              <span v-else-if="item.key=='repairStatus'">
                {{ scope.row[item.key]==3?'待修复':scope.row[item.key]==2?'已修复':scope.row[item.key]==1?'已接触未修复':scope.row[item.key]==0?'下发未接触成功':'' }}
              </span>
              <!-- 不满指标 低于6分显示红  -->
              <span v-else-if="item.key=='csmcustomerlableVisibleTxt'">
                <span v-for="(x,ix) in scope.row[item.key]" :key="ix">
                  <div v-if="x.point>-1">
                    <!-- <span>{{ scope.row[item.key].length }}</span> -->
                    <span :style="{color:0<x.point<7?'':''}">{{ x.txt }}</span>
                    <span v-show="ix!==scope.row[item.key].length-1">{{ '、' }}</span>
                  </div>
                </span>

              </span>

              <span v-else>{{ scope.row[item.key] }}</span>
            </template>

          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="200"
          >
            <template slot-scope="scope">
              <div class="optionbtnbox">
                <div>
                  <span class="coloryellow" @click="openCheckDetail(scope.row)">查看</span>
                </div>
                <div>
                  <span v-if="scope.row.notifyStatus=='0'&&scope.row.isSend==1" class="coloryellow" @click="sendMsg(scope.row)">短信回访</span>
                  <span v-if="scope.row.notifyStatus=='1'" class="coloryellow" style="color:#8C8C8C;cursor:not-allowed">短信回访</span>
                </div>
                <div>

                  <el-popconfirm
                    confirm-button-text="删除"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除本条数据？"
                    @confirm="dele(scope.row)"
                  >
                    <span slot="reference" class="coloryellow">删除</span>
                  </el-popconfirm>

                </div>
              </div>
            </template>
          </el-table-column>

        </el-table>

      </div>

    </div>
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      conditionsize: 'small',
      tableLoading: false,
      tableDatas: [],
      formInline: {
        type: '',
        phone: '',
        timeRange: []
      },
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      columnList: [
        {
          key: 'userId',
          name: '用户ID'
        },
        {
          key: 'csmcustomerlableVisibleTxt',
          name: '不满指标'
        },
        {
          key: 'phoneNo',
          name: '手机号码',
          width: '120px'
        },

        {
          key: 'type',
          name: '类型'
        },

        {
          key: 'isComplaint',
          name: '是否投诉'
        },
        {
          key: 'isSend',
          name: '是否下发短信'
        },
        {
          key: 'lastContactTime',
          name: '最近接触时间',
          width: '150px'
        },
        {
          key: 'repairStatus',
          name: '修复状态'
        }

      ]
    }
  },
  methods: {
    query() {},
    queryList() {},
    handleSelectionChange() {},
    resetForm(formName) {
      this.formInline.timeRange = []
      this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.el-cascader {
  :deep(.el-icon-arrow-down:before ) {
    content: "\E6E1";
  }

  :deep(.el-icon-arrow-down) {
    transform: rotate(180deg);
  }

  :deep(.is-reverse.el-icon-arrow-down) {
    transform: rotate(0deg);
  }
}
.conbox{
    background:#fff;border:1px solid rgba(225,225,225,0.8);border-bottom:none;
}
</style>
