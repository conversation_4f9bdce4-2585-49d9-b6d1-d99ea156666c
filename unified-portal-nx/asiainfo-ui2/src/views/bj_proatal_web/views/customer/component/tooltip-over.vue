
<template>
  <div class="text-tooltip">
    <el-tooltip
      class="item"
      effect="dark"
      :disabled="isShowTooltip"
      :content="content"
      popper-class="custom"
      placement="top"
    >
      <p
        class="over-flow"
        :class="className"
        @mouseover="onMouseOver(refName)"
      >
        <span :ref="refName" style="color:white;line-height:12px;">{{ content }}</span>
      </p>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'textTooltip',
  props: {
    // 显示的文字内容
    content: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 外层框的样式，在传入的这个类名中设置文字显示的宽度
    className: {
      type: String,
      default: () => {
        return ''
      }
    },
    // 为页面文字标识（如在同一页面中调用多次组件，此参数不可重复）
    refName: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    return {
      isShowTooltip: true
    }
  },
  methods: {
    /**
     * @description: 判断是否哦开启 tooltip
     * @param {*} str
     * @return {*}
     */
    onMouseOver (str) {
      const parentWidth = this.$refs[str].parentNode.offsetWidth
      const contentWidth = this.$refs[str].offsetWidth
      // 判断是否开启tooltip功能
      if (contentWidth > parentWidth) {
        this.isShowTooltip = false
      } else {
        this.isShowTooltip = true
      }
    }
  }
}
</script>
 <style type="text/css">
 .custom.el-tooltip__popper{
  width: 300px !important;;
 }
 </style>
<style lang="scss" scoped>
.text-tooltip {
  .over-flow {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 23px;
    margin: 0;
  }
}
.tooltip-block {
  width: 100%;
  display: block;
}
:deep(.el-tooltip__popper){
  width:300px;
}
</style>
