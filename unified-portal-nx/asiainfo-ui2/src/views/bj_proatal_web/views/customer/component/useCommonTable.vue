<template>
  <div>
    <CommonTable
      ref="testTableList"
      style="padding-top:10px;"
      :conditions="conditions"
      :columns="tableColumns"
      show-overflow-tooltip
      :max-height="560"
      highlight-current-row
      row-class-name="row-class-name"
      :pagination="true"
      @row-click="testRowClick"
      @queryData="(params)=>queryData(params,'testTableList')"
    >

      <!-- <div slot="actions" slot-scope="scope" >
        <el-button type="primary" size="small" @click="checkDetail">查看</el-button>
      </div> -->
      <div slot="customerId" slot-scope="scope" style="text-align:center">
        <span style="color:red">{{ scope.data.row['customerId'] }}</span>
      </div>
      <div slot="phoneNo" slot-scope="scope" style="text-align:center">
        <span>{{ scope.data.row['phoneNo']?scope.data.row['phoneNo'].substring(0,3)+" *****" + scope.data.row['phoneNo'].substring(8):'' }}</span>
      </div>
      <div slot="type" slot-scope="scope" style="text-align:center">
        <span>{{ scope.data.row['type']=='1'?'显性':scope.data.row['type']=='0'?'潜在':'' }}</span>
      </div>
      <div slot="userStatus" slot-scope="scope" style="text-align:center">

        <span :style="{color:'white',borderRadius:'4px',display:'inline-block',padding:'2px 5px',background:scope.data.row['userStatus']=='已评测'?'pink':'gray'}">{{ scope.data.row['userStatus'] }}</span>

      </div>
       <div slot="isPush" slot-scope="scope" style="text-align:center">
        <!-- 1-未推送 2-已推送未返回 3-推送结果已返回 -->
        <span> {{ scope.data.row['isPush']==3?'推送结果已返回':scope.data.row['isPush']==2?'已推送未返回':scope.data.row['isPush']==1?'未推送':'' }}</span>
      </div>
      <div slot="repairStatus" slot-scope="scope" style="text-align:center">
        <span> {{ scope.data.row['repairStatus']==3?'待修复':scope.data.row['repairStatus']==2?'已修复':scope.data.row['repairStatus']==1?'已接触未修复':scope.data.row['repairStatus']==0?'下发未接触成功':'' }}</span>
      </div>
       <div slot="isComplaint" slot-scope="scope" style="text-align:center">
        <span> {{ scope.data.row['isComplaint']=='Y'?'是':scope.data.row['isComplaint']=='N'?'否':'' }}</span>
      </div>
      <div slot="isSend" slot-scope="scope" style="text-align:center">
        <span >{{ scope.data.row['isSend']=='0'?'已发送':scope.data.row['isSend']=='1'?'未发送':'' }}</span>
      </div>

        <!-- 不满指标 低于6分显示红  -->
        <div slot="csmcustomerlableVisibleTxt" slot-scope="scope">
               <span v-for="(x,ix) in scope.data.row['csmcustomerlableVisibleTxt']" :key="ix">
                        <div v-if="x.point>-1">
                          <!-- <span>{{ scope.row[item.key].length }}</span> -->
                          <span :style="{color:0<x.point<7?'':''}">{{ x.txt }}</span>
                          <span v-show="ix!==scope.data.row['csmcustomerlableVisibleTxt'].length-1">{{ '、' }}</span>
                    </div>
              </span>
      </div>
      <div slot="actions" slot-scope="scope">

        <div class="optionbtnbox">
          <div>
            <span class="coloryellow" @click="clickCheck(scope.data.row)">查看</span>
          </div>
          <!-- <div>
            <span v-if="scope.data.notifyStatus=='0'&&scope.data.isSend==1" class="coloryellow" @click="sendMsg(scope.data)">短信回访</span>
            <span v-if="scope.data.notifyStatus=='1'" class="coloryellow" style="color:#8C8C8C;cursor:not-allowed">短信回访</span>
          </div> -->
          <!-- <div>

            <el-popconfirm
              confirm-button-text="删除"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定删除本条数据？"
              @confirm="dele(scope.data.row)"
            >
              <span slot="reference" class="coloryellow">删除</span>
            </el-popconfirm>

          </div> -->
        </div>
      </div>

      <div slot="additionBtn" style="position:absolute;right:0;top:-48px">

         <el-button type="default" size="small" @click="drawer=true">生成客群</el-button>
      </div>
    </CommonTable>
    <el-dialog title="" :visible.sync="checkDialogVis" fullscreen :append-to-body="true" class="detaildialog">
      <Check @close="checkDialogVis=false" v-if="checkDialogVis" :check-row="checkRow" :colum-names-map="columNamesMap" :colum-names-point-map="columNamesPointMap" />
    </el-dialog>
    <el-drawer
      :visible.sync="drawer"
      size="50%"
      :append-to-body="true"
    >
      <div slot="title">
        <span style="font-weight:bold;font-size:18px;color:black;">生成客群</span>

      </div>
      <customerAddForm v-if="drawer" :csmcustomerlable-opt="csmcustomerlableOpt" :tree-data="treeLabelData" :options-cascader="optionsCascader" @closedrawer="drawer=false" />
    </el-drawer>

  </div>

</template>

<script>
import CommonTable from '@/views/bj_proatal_web/nx-components/commonTable/index.vue'
import Check from '@/views/bj_proatal_web/views/customer/component/detailInfo.vue'
import customerAddForm from '@/views/bj_proatal_web/views/customer/component/create.vue'
import { csmcustomerbaseinfo, getColumNames, labelLists, getAllGrides,getColumScore,delId,zbPage } from '@/api/customer/index'
// import { csmcustomerlable, getColumScore, getColumNames, csmcustomerbaseinfo, delId, getByPhoneNo, getAllGrides, labelLists } from '@/api/customer/index'
import { getLabels } from '@/api/customer/index'

export default {
  components: {
    CommonTable,
    Check,
    customerAddForm
  },
  data() {
    return {
      // 客群生成start******* */
      optionsCascaderExpand: [],
      drawer: false,
      csmcustomerlableOpt: [

      ],
      treeLabelData: [],
      optionsCascader: [],
      // 客群生成 over******* */

      checkRow: {},
      columNamesMap: {},
      columNamesPointMap: {},
      checkDialogVis: false,
      // 条件配置
      conditions: [

        {
          type: 'select',
          key: 'type',
          label: '类型',
          width: '120px',
          options: [{ label: '显性', value: '1' }, { label: '潜在', value: '0' }]
        },
        {
          type: 'select',
          key: 'isComplaint',
          label: '是否投诉',
          options: [{ label: '是', value: 'Y' }, { label: '否', value: 'N' }]
        },
         {
          type: 'select',
          key: 'satisfiedType',
          label: '满意度类型',
          options: [
            { label: '满意', value: '满意' },
            { label: '中立', value: '中立' },
            { label: '不满', value: '不满' }
          ]
        },

          {
          type: 'cascader', // 输入框
          key: 'csmcustomerlableSeleted', // 字段名 根据客户群名称来搜索
          label: '不满指标',
          props:{multiple: true,checkStrictly: true },
          options:[]
        },

        // {
        //   type: 'select',
        //   key: 'customerPurpose',
        //   label: '客户群用途',
        //   options: [{ type: '用后即评', value: '用后即评' }] // {type:'',value:''}
        // //   getOpts: () => {}
        // },

        {
          type: 'daterange', // 'date' 日期范围 日期
          key: 'createTime',
          label: '选择时间段',
          width: '250px',
          parmsName: ['beginContactTime', 'endContactTime'],
          pickerOptions:{
             disabledDate(time) {
            return time.getTime() > Date.now();
          }
          }
        },
        {
          type: 'input', // 输入框
          key: 'phoneNo', // 字段名 根据客户群名称来搜索
          label: '手机号码',
          placeholder:'请输入手机号码'
        },

        // {
        //   type: 'cascader', // 输入框
        //   key: 'x', // 字段名 根据客户群名称来搜索
        //   label: '级联',
        //   options: [
        //     {
        //       value: 'zhinan',
        //       label: '指南',
        //       children: [
        //         {
        //           value: 'zhinan1',
        //           label: '指南1',
        //           children: [
        //             {
        //               value: 'zhinan2',
        //               label: '指南2'
        //             }
        //           ]
        //         }
        //       ]
        //     },
        //     {
        //       value: 'zhinan3',
        //       label: 'p3',
        //       children: [
        //         {
        //           value: 'zhinan4',
        //           label: '指南5',
        //           children: [
        //             {
        //               value: 'zhinan6',
        //               label: '指南6'
        //             }
        //           ]
        //         }
        //       ]
        //     }
        //   ]
        // }
        //  {
        //   type:'date',// 'date' 日期范围 日期
        //   key:'time',
        //   label:'时间',
        // },
        // {
        //   type: 'select',
        //   key: 'customerPurpose',
        //   label: '客户群用途',
        //   options: [{ type: '用后即评', value: '用后即评' }] // {type:'',value:''}
        // //   getOpts: () => {}
        // }
          {
          type: 'select',
          key: 'labels',
          label: '客户标签',
          multiple:true,
          width:'400px',
          // options: [] // {type:'',value:''}
          getOpts:  getLabels
        },
      ],
      tableColumns: {
        userId: {
          label: '用户ID',
          width: 150,
          sortable: false,
          showOverflowTooltip: true
        },
        satisfiedType:{
          label:'满意度类型'
        },
        csmcustomerlableVisibleTxt: {
          label:'不满指标',
          width:250
        },
        phoneNo:{
          label:'电话号码'
        },
        isComplaint:{
          label:'是否投诉'
        },
        type: {
          label:'类型'
        },



        // isSend: {
        //   label:'是否下发短信',
        //   width:200
        //   },
        lastContactTime: {
          label: '最近接触时间',
          width:200
        },

        // isSlot 配置可以省 操作列通过增加一列进来 不用单独插槽 动态改变列的配置 表格是否有问题测试 格式调整
        // repairStatus: {
        //   label: '修复状态'
        // },
        actions: { label: '操作',width:150 }
      }

    }
  },
  created(){
     // 获取每个指标对应的不满意的值
    getColumScore().then(res => {
      const { code, data } = res
      if (code == 200) {
        this.columNamesPointMap = data
        this.init()
      }
    })
  },
  mounted() {

  },
  methods: {
    init() {
      const p0 = labelLists()// 获取所有标签tree
      const p1 = getColumNames()// 获取与标签对应的map

      Promise.all([p0, p1]).then(values => {
        const res0 = values[0]
        const res1 = values[1]

        let { code, data } = res0
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
            this.conditions[3].options = data;
            this.$store.commit('UPDATE_TREE_LABEL_DATA', data)
          }
        }

        if (res1.code == 200) {
          this.columNamesMap = res1.data
          this.$store.commit('UPDATE_COLUMNAMESMAP', res1.data)
        }
      })

      // 获取所有网格1
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr
          this.handlerCascaderDataExpand(arr)
        }
      })
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    // 获取不满意指标map
    getColumNames() {
      getColumNames().then(res => {
        const { code, data } = res
        if (code == 200) this.columNamesMap = data
      })
    },
    // 获取所有标签
    labelLists() {
      labelLists().then(res => {
        let { code, data } = res
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }
      })
    },

    sendMsg() {},
    dele(row) {
        delId(row.id).then(res => {
        console.log(row)
        const { code, data } = res
        if (code == 200) {
          this.queryData()
        }
      })
    },
    testRowClick() {
      // this.$refs.testTableList.getList({ a: 1 })
      // console.log('rowClick....')
    },
    testSortChange() {
      // console.log('sortChange...')
    },
    checkDetail() {
      console.log('check-btn is click...')
    },
    clickCheck(row) {
      this.checkDialogVis = true
      // row.phoneNo='15825339698'
      this.checkRow = row
      console.log('row:', row)
    },
    queryData(p, refKey) {
      let columFiles = []
       if (p&&p.csmcustomerlableSeleted&&p.csmcustomerlableSeleted.length) {
        columFiles = p.csmcustomerlableSeleted.map(j => {
          const len = j.length
          return j[len - 1]
        })
       delete p.csmcustomerlableSeleted
      }
       if(p.beginContactTime) {
        p.beginContactTime = `${p.beginContactTime} 00:00:00`
       }
       if(p.endContactTime){
        p.endContactTime = `${p.endContactTime} 23:59:59`
       }

       p.columFiles = columFiles || []




      zbPage(p).then(res => {
        const { code, data } = res
        const { records, total, size, current } = data

         // 处理不满意指标
          const { columNamesMap = {}, columNamesPointMap = {}} = this
          console.log('columNamesMap=>',columNamesMap)
          console.log('columNamesPointMap=>',columNamesPointMap)

          records.forEach(i => {
            i.csmcustomerlableVisibleTxt = []
            for (const ke in i) {
              if (columNamesMap.hasOwnProperty(ke) && columNamesPointMap.hasOwnProperty(ke) && i[ke] && i[ke] < columNamesPointMap[ke] && i[ke] != -1) {
                let strobj = { txt: '', point: '' }
                strobj = {
                  txt: `${columNamesMap[ke]}(${i[ke]}) `,
                  point: i[ke] }

                i.csmcustomerlableVisibleTxt.push(strobj)
              }
            }
          })

        console.log('records=>',records)
        this.$refs[refKey].tableData = records
        this.$refs[refKey].form.pagination.totalCount = total
        this.$refs[refKey].form.pagination.currentPage = current || 1
        this.$refs[refKey].form.pagination.pageSize = size || 10
      }).finally(() => {
        if(this.$refs[refKey]){
          this.$refs[refKey].tableLoading = false
       this.$refs[refKey].btnLoading = false
        }

      })
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    }

  }
}
</script>

<style lang="scss" scoped>

.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
:deep(.el-dialog__body){
  padding-top:0;
}
</style>
