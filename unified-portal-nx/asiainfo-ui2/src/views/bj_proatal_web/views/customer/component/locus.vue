<template>
  <div v-loading="loading">
    <!-- <div class="flexForm">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="0px" class="demo-ruleForm">
        <el-form-item label="" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入" class="input-with-select">
            <div slot="append" @click="search">
              <el-button type="primary">搜索</el-button>
            </div>
          </el-input>
        </el-form-item>
      </el-form>
    </div> -->
    <div class="flexCen">
      <div class="flexCenItem">
        <div class="imgbox">
          <img src="../../../../../assets/images/customer/cai.svg" style="width:60%" alt="">
        </div>
        <div>
          <div class="la">客户满意度指数总分</div>
          <div class="score">
            <span v-if="topInfoObj.satisfactionScore!=-1">
              {{ topInfoObj.satisfactionScore||'0' }}
            </span>
            <span v-if="topInfoObj.satisfactionScore==-1" style="font-size:16px">
              未参与评测
            </span>
          </div>
        </div>
      </div>
      <div class="flexCenItem">
        <div class="imgbox">
          <img src="../../../../../assets/images/customer/cai.svg" style="width:60%" alt="">
        </div>
        <div>
          <div class="la">参与调研次数</div>
          <!-- <div style="margin-top:30px;">
            <el-tag v-if="topInfoObj.notDisturbLabel" size="mini" :color="tagColor">
              <span style="color:white">{{ topInfoObj.notDisturbLabel||'--' }}</span></el-tag>
          </div> -->
          <!-- <div style="margin-top:30px;">
            <el-tag v-if="checkRow.a091" size="mini" :color="tagColor">
              <span style="color:white">{{ checkRow.a091||'--' }}</span></el-tag>
          </div> -->

          <div class="score">
            <span>
              {{ checkRow.a091||'0' }}
            </span>
          </div>

        </div>

      </div>
      <div class="flexCenItem">
        <div class="imgbox">
          <img src="../../../../../assets/images/customer/cai.svg" style="width:60%" alt="">
        </div>
        <div>
          <div class="la">近三个月投诉次数</div>
          <div class="score">
            {{ topInfoObj.complaintCountMon3||'0' }}
          </div>

        </div>
      </div>
      <div class="flexCenItem">
        <div class="imgbox">
          <img src="../../../../../assets/images/customer/cai.svg" style="width:60%" alt="">
        </div>
        <div>
          <div class="la">近期不满意原因</div>
          <div v-if="topInfoObj.dissatisfactionCause" style="margin-top:30px;">
            <el-tag size="mini" :color="tagColor">
              <!-- <span style="color:white">{{ topInfoObj.dissatisfactionCause||'--' }}</span> -->
              <!-- <tooltip-over
                  :content="topInfoObj.dissatisfactionCause||'--'"
                  class="tooltip-block"
                  refName="tooltipCampaignName"
                ></tooltip-over> -->

              <tooltip-over
                :content="topInfoObj.dissatisfactionCause||'--'"
                class="tooltip-block"
                ref-name="tooltipCampaignName"
              />

            </el-tag>
          </div>
          <div v-else class="score">
            <span>
              --
            </span>
          </div>
        </div>
      </div>
    </div>
    <div style="height:50vh;margin-top:25px;display:flex;justify-content:center">
      <div class="ibox">
        <div class="t">重点关注行为</div>
        <div v-if="customerData['重点关注行为'].length" style="height:40vh;overflow-y:auto;padding-left:20px;padding-top:20px">
          <el-steps direction="vertical" :active="customerData['重点关注行为'].length">
            <el-step v-for="(i,idx) in customerData['重点关注行为']" :key="idx" style="min-height:100px;padding-left:110px;position:relative;">
              <div slot="title" style="position:absolute;width:100px;left:0;top:0;">
                <div style="text-align:right;font-size:14px;padding-right:10px;">{{ i.contentDate1 }}</div>
                <div style="text-align:right;font-size:12px;padding-right:10px;">{{ i.contentDate2 }}</div>
              </div>
              <div slot="icon">
                <div style="height:40px;width:40px;border-radius:50%;display:inline-block;text-align:center;background:#E7361F;padding-top:5px;">
                  <img
                    style="width:70%"
                    :src="zdgzxwIcons[i.titltType]"
                    alt=""
                  >
                </div>
              </div>
              <div slot="description">
                <div style="font-weight:bold;padding-left:10px">{{ i.title }}</div>
                <div style="padding-left:10px">{{ i.content }}</div>
              </div>
            </el-step>

          </el-steps>
        </div>
        <div v-else style="height:41vh">
          <Blank2 />
        </div>
      </div>
      <div class="ibox">
        <div class="t right">正常客户行为</div>
        <div v-if="customerData['正常客户行为'].length" style="height:40vh;overflow-y:auto;padding-left:20px;padding-top:20px">
          <el-steps direction="vertical">
            <el-step v-for="(i,idx) in customerData['正常客户行为']" :key="idx" style="min-height:95px;padding-left:110px;position:relative;">
              <div slot="title" style="position:absolute;width:100px;left:0;top:0;">
                <div style="text-align:right;font-size:14px;padding-right:10px;">{{ i.contentDate1 }}</div>
                <div style="text-align:right;font-size:12px;padding-right:10px;">{{ i.contentDate2 }}</div>
              </div>
              <div slot="icon">
                <div style="height:40px;width:40px;border-radius:50%;display:inline-block;text-align:center;background:rgb(33,177,138);padding-top:5px;">
                  <img
                    style="width:70%"
                    :src="zdgzxwIcons[i.titltType]"
                    alt=""
                  >
                </div>
              </div>
              <div slot="description">
                <div style="font-weight:bold;padding-left:10px">{{ i.title }}</div>
                <div style="padding-left:10px">{{ i.content }}</div>
              </div>
            </el-step>
          </el-steps>
        </div>
        <div v-else style="height:41vh">
          <Blank2 />
        </div>

      </div>

    </div>

  </div>
</template>

<script>
import { getCustomerDateInfoByPhoneNum } from '@/api/customer/index'
import Blank from '../../../components/common/Blank.vue'
import tooltipOver from './tooltip-over'
export default {
  components: { Blank, tooltipOver },
  props: {
    customerData: {
      type: Object,
      default: () => {
        return {
          '正常客户行为': [],
          '重点关注行为': []
        }
      }
    },
    checkRow: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        name: ''
      },
      rules: {},
      tagColor: '#E7361F',
      zdgzxwIcons: {
        ts: require('../../../../../assets/images/customer/ts.svg'),
        pc: require('../../../../../assets/images/customer/pc.svg'),
        tdxx: require('../../../../../assets/images/customer/tdxx.svg'),
        zx: require('../../../../../assets/images/customer/zx.svg'),
        dg: require('../../../../../assets/images/customer/dg.svg'),
        yhxf: require('../../../../../assets/images/customer/yhxf.svg')
      },
      topInfoObj: {
        'satisfactionScore': '',
        'notDisturbLabel': '',
        'complaintCountMon3': '',
        'dissatisfactionCause': ''
      }

    }
  },
  created() {
    this.queryData()
  },
  methods: {
    search() {
      console.log(this.ruleForm.name)
    },
    // 查询数据
    queryData() {
      const p = { phoneNum: this.checkRow.phoneNo }
      this.loading = true
      getCustomerDateInfoByPhoneNum(p).then(res => {
        const { code, data } = res
        if (code == 200) {
          this.topInfoObj = data
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.tooltip-block{
  display: inline-block;
  max-width:100px;
}
.tooltipCampaignName{
  width:100px;
}
.flexForm{
  display: flex;
  justify-content: center;
}
.demo-ruleForm{
  width:35%;
}
.ibox{
  width:30%;background:red;margin-right:4%;background:rgb(239,239,239);
  border-radius: 10px;
  border:1px solid rgba(231,231,231,0.8);
  .t{
    border-left:5px solid #E7361F;
    line-height:14px;
    margin-left:20px;
    margin-top:30px;
    padding-left:10px;
    font-weight: bold;
    margin-bottom: 20px;
    &.right{
      border-left:5px solid rgb(23,181,145);
    }
  }
}
.flexCen{
  display: flex;
  padding: 20px 6%;
  justify-content: space-around;
  .flexCenItem{
    width:260px;
    height:130px;
    padding:15px 10px 15px 10px;
    border:1px solid rgba(233,233,233,0.8);
    box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);
    // box-shadow: 0px 0px 2px 2px rgb(233,233,233);
    // flex:1;
    display:flex;
    .imgbox{
      width:70px;
      height:70px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin-right: 10px;
      background:#E7361F;

    }
    .la{
      font-weight: bold;
      font-size: 14px;
      color:rgb(32,43,47);
      line-height: 24px;
    }
    .score{
      font-size: 40px;
      line-height: 60px;
      color:rgb(123,123,123);
    }
  }
}
:deep(.el-input-group__append), :deep(.el-input-group__prepend){
  background:rgb(24,121,254);
  color:white;
}
:deep(.el-step__description.is-wait){
  color:black;
}
:deep(.el-step__title.is-wait){
  color:black;
}
</style>
