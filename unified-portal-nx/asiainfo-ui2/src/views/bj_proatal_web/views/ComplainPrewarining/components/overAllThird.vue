<template>
  <div class="content">
    <div class="service-header">
      <NumberTitle num="03" text="投诉工单情况" />
      <div class="header-search">
        <div class="block">
          <!-- <div style="margin-right: 10px">
            <span class="labelcss">选择地市：</span>
            <el-cascader
              v-model="idLists"
              class="el-cascader"
              placeholder="请选择地市"
              :options="optionsCascader2"
              :size="conditionsize"
              :props="props"
              @change="gridIdChange"
            />
          </div> -->
          <AreaPicker
            size="small"
            :ext-options="mapPermission.mapLevel == 1 ? [
              {name:'全省按部门',id:'999999',data:{ name:'全省按部门',id: '999999',level:1}}
            ] : []"
            @change="gridIdChange"
          />
          <NewDatePicker :dateTypes="['date','month','daycumulative']" :tableName="'portal_indicator_appeal_monitor_data'" @change="dateChange"></NewDatePicker>
        </div>
      </div>
    </div>
    <div class="service-content">
      <!-- 顶部轮播菜单 -->
      <div class="swiper-top">
        <swiper ref="mySwiper" :options="swiperOptions">
          <swiper-slide
            v-for="item in slildOptions"
            :key="item.target_name"
            :class="
              item.target_name == dataItem.target_name ? 'chosed-slide' : ''
            "
            @click="changeTarget(item)"
          >
            <div class="slide-title">
              <i
                v-if="item.target_name == dataItem.target_name"
                class="el-icon-caret-right"
              />
              <span class="score">{{ item.target_name }}</span>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="slider-button">
        <div class="swiper-button-prev el-icon-caret-left" />
        <!--左箭头。如果放置在swiper外面，需要自定义样式。-->
        <div class="swiper-button-next el-icon-caret-right" />
        <!--右箭头。如果放置在swiper外面，需要自定义样式。-->
      </div>

      <div v-if="dataItem.id" class="Chart">
        <div class="leftChart">
          <LineBarChart
            v-if="echartsLeave === 0"
            :all-data="chartLeftData"
            :legend-data="['投诉总量', '万投比']"
            :keymap="{
              xData: 'statdate',
              seriesData: ['score', 'eoms'],
            }"
            :is-double-line="false"
            @barclick="leftBarclick"
          />
          <LineBarChart
            v-if="echartsLeave >= 1"
            :all-data="chartLeftData2"
            :legend-data="['投诉量', '环比', '同比']"
            :keymap="{
              xData: 'businessname',
              seriesData: ['score', 'momrate', 'yearbasis'],
            }"
            @barclick="leftBarclick2"
          />
        </div>

        <el-button
          v-show="showExportBtn"

          class="leftBtn"
          size="small"
          type="primary"
          @click="exportOne"
        >导出</el-button>
        <img
          v-if="echartsLeave > 0"
          class="goBack"
          src="../../../assets/img/expCourse/return.png"
          @click="goBack"
        >
        <div class="rightChart">
          <LineBarChartR
            :is-show-tip="isShowTip"
            :all-data="chartRightData"
            :data-list="dataList"
            :legend-data="['投诉量', '环比']"
            :keymap="{
              xData: 'appartname',
              seriesData: ['score', 'momrate'],
            }"
            :is-double-line="false"
          />
        </div>
        <el-button
          v-show="showExportBtn"
          class="rightBtn"
          size="small"
          type="primary"
          @click="exportTwo"
        >导出</el-button>
      </div>
    </div>
  </div>
</template>
<script>
// import { swiper, swiperSlide } from "vue-awesome-swiper";
import NumberTitle from '../../../components/common/numberTitle.vue'
import 'vue-awesome-swiper/node_modules/swiper/dist/css/swiper.css'
import LineBarChart from './linebarChartsT.vue'
import LineBarChartR from './linebarChartsRTipshow2.vue'
import {
  getComplainByTime,
  getComplainBySection,
  getComplainByBusiness
} from '@/api/complaint-analysis/api'
import { AreaByUser } from '@/api/mobilePhoneTariff'
import { esportComplaint } from '@/api/complaint-analysis/api'
import tool from '@/views/bj_proatal_web/utils/utils'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker.vue'

export default {
  components: {
    NumberTitle,
    LineBarChart,
    LineBarChartR,
    AreaPicker,
    NewDatePicker
  },
  inject: ['showExportBtn', 'mapPermission'],

  data() {
    const self = this
    const year = new Date().getFullYear()
    let month = Number(new Date().getMonth()) + 1
    if (month < 10) {
      month = '0' + month
    }
    return {
      echartsLeave: 0,
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      isShowTip: true,
      appartRoute: '',
      businessName: '',
      businessRoute: '',
      appartLevel: '0',
      selectType: '2',
      gridId: '64000',
      statType: '3',
      conditionsize: 'small',
      idLists: ['640000'],
      defaultDateTypeList: ['日', '月', '日累计'],
      defaultDate: String(year) + String(month),
      optionsCascader2: [
        { cityName: '全省按部门', level: 1, name: '全省按部门', id: '999999' }
      ],
      slildOptions: [
        { target_name: '投诉建单量', id: 'a0002' },
        { target_name: '投诉非建单量', id: 'a0003' },
        { target_name: '投诉归档量', id: 'a0004' },
        { target_name: '未归档工单量', id: 'a0005' },
        { target_name: '超时未处理工单量', id: 'a0006' }
      ],
      // 菜单的swiper配置
      dataItem: { target_name: '投诉建单量', id: 'a0002' },
      swiperOptions: {
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        slidesPerView: 5, // 控制swipe显示的个数
        spaceBetween: 30, // slide之间的距离
        on: {
          click: function() {
            self.changeTarget(this.clickedIndex)
          }
        }
      },
      dataList: {
        targetId: 'a0002',
        opTime: '',
        cityId: '',
        selectType: '',
        statType: ''
      },

      activeName: '',
      fixActiveName: '',
      tabList: [
        { name: '不满客户数' },
        { name: '接触成功率' },
        { name: '修复成功率' },
        { name: '满意度得分' }
      ],
      barclickTime: '',
      chartLeftData: [],
      chartLeftData2: [],
      chartRightData: [],
      targetId: 'a0002',
      cityId: this.mapPermission.cityId
    }
  },
  created() {},
  mounted() {
    this.initTwo()
    this.dataList = {
      targetId: 'a0002',
      opTime: this.formatDate(this.defaultDate),
      cityId: this.cityId,
      selectType: this.selectType,
      statType: this.statType
    }
  },
  methods: {
    exportOne() {
      // 左侧导出
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))
      let params = ''
      if (this.echartsLeave == 0) {
        // 初始时左侧导出
        params = `?cityId=${this.cityId}&opTime=${this.defaultDate}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=${this.targetId}&chartNum=1&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      } else {
        // 下钻时左侧导出
        params = `?cityId=${this.cityId}&opTime=${this.barclickTime}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=${this.targetId}&chartNum=2&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      }
      esportComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            this.dataItem.target_name + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', this.dataItem.target_name + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportTwo() {
      // 右侧导出
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))
      const params = `?cityId=${this.cityId}&opTime=${this.barclickTime}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=${this.targetId}&chartNum=3&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      console.log(params, '888')
      esportComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            this.dataItem.target_name + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', this.dataItem.target_name + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    goBack() {
      this.echartsLeave = this.echartsLeave - 1
      if (this.echartsLeave == 1) {
        this.businessRoute = sessionStorage.getItem('businessRoute')
        this.businessName = this.businessRoute
        this.leftBarclick()
      } else if (this.echartsLeave >= 2) {
        this.businessRoute = this.businessRoute.substring(
          0,
          this.businessRoute.lastIndexOf('-')
        )
        if (this.echartsLeave > 2) {
          this.businessName = this.businessRoute.substring(
            this.businessRoute.lastIndexOf('-') + 1
          )
        } else {
          this.businessName = this.businessRoute
        }
        this.leftBarclick2()
      } else {
        this.getLeftEchartData()
      }

      this.getRightEchartData()
    },
    gridIdChange(value) {
      const val = Array.isArray(value) ? value[value.length - 1] : value

      if (
        this.targetId == 'a0003' ||
        this.targetId == 'a0006' ||
        this.targetId == 'a0005'
      ) {
        if (val == '999999') {
          this.cityId = '640000'
        } else {
          this.cityId = val
        }
        this.selectType = '2'
      } else {
        if (val == '999999') {
          this.cityId = '640000'
          this.selectType = '1'
          this.appartLevel = '1'
        } else {
          this.cityId = val
          this.selectType = '2'
          this.appartLevel = '0'
        }
      }
      this.getLeftEchartData()
      this.getRightEchartData()
      this.dataList.cityId = this.cityId
      this.dataList.selectType = this.selectType
    },
    dateChange(params) {
      const [ dateType, v ] = params
      this.statType =
        dateType == '月'
          ? '3'
          : dateType == '日'
            ? '1'
            : dateType == '日累计'
              ? '5'
              : ''
      this.defaultDate = v
      if (this.echartsLeave == 1) {
        this.leftBarclick()
      } else if (this.echartsLeave > 1) {
        this.leftBarclick2()
      } else {
        this.getLeftEchartData()
      }
      this.getRightEchartData()
      this.dataList.opTime = this.formatDate(this.defaultDate)
      this.dataList.statType = this.statType
    },
    formatDate(val) {
      if (this.statType == '3' && val.indexOf('-', 0) == '-1') {
        val = val.slice(0, 4) + '-' + val.slice(4)
      }
      val = new Date(val)
      const year = val.getFullYear()
      const month =
        val.getMonth() + 1 < 10 ? `0${val.getMonth() + 1}` : val.getMonth() + 1
      const date = val.getDate() < 10 ? `0${val.getDate()}` : val.getDate()
      if (this.statType == '3') {
        return `${year}${month}`
      } else {
        return `${year}${month}${date}`
      }
    },
    async initTwo() {
      // 初始化获取所有区域
      const { data, code } = await AreaByUser({
        restSerialNo: '6MujCDCs'
      })
      if (code == 200) {
        this.optionsCascader2 = this.optionsCascader2.concat(data[0])
        this.getLeftEchartData()
        this.getRightEchartData()
      }
    },
    async leftBarclick(item) {
      this.echartsLeave = 1
      if (item) {
        this.businessRoute = item.xValue
        this.businessName = item.xValue
        this.barclickTime = item.xValue
        sessionStorage.setItem('businessRoute', this.businessRoute)
      }

      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: this.targetId,
        businessLevel: this.echartsLeave.toString(),
        businessRoute: this.businessRoute
      }
      const { data } = await getComplainByBusiness(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData2 = data.data
        this.getRightEchartData()
      }
    },
    // 点击柱状图
    async leftBarclick2(item) {
      if (item) {
        this.echartsLeave++
        this.businessName = item.xValue
        if (item.xValue && this.echartsLeave > 2) {
          this.businessRoute = this.businessRoute + '-' + item.xValue
        } else if (item.xValue && this.echartsLeave == 2) {
          this.businessRoute = item.xValue
        }
      }
      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: this.targetId,
        businessLevel: this.echartsLeave.toString(),
        businessRoute: this.businessRoute
      }
      const { data } = await getComplainByBusiness(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData2 = data.data
        this.getRightEchartData()
      }
    },
    // 切换滑动块
    changeTarget(index) {
      const i = this.slildOptions[index]
      this.dataItem = i
      this.targetId = i.id
      if (
        this.targetId == 'a0003' ||
        this.targetId == 'a0006' ||
        this.targetId == 'a0005'
      ) {
        this.selectType = '2' // 地市
        if (this.optionsCascader2.length == 2) {
          // 隐藏全省按部门
          this.optionsCascader2.shift()
        }
        if (this.idLists == '999999') {
          this.idLists = ['640000']
        }
      } else {
        const arr = [
          {
            cityName: '全省按部门',
            level: 1,
            name: '全省按部门',
            id: '999999'
          }
        ]
        if (this.optionsCascader2 && this.optionsCascader2.length == 1) {
          this.optionsCascader2 = arr.concat(this.optionsCascader2)
        }
      }
      // if (
      //   this.targetId == "a0003" ||
      //   this.targetId == "a0006" ||
      //   this.targetId == "a0005"
      // ) {
      //   this.selectType = "2";
      // } else if (this.idLists == "999999") {
      //   this.selectType = "1";
      // }
      this.getLeftEchartData()
      this.getRightEchartData()
      this.dataList.targetId = i.id
    },
    // 查询接口-左边图例
    async getLeftEchartData() {
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))

      const params = {
        statType: this.statType,
        opTime: this.defaultDate,
        cityId: this.cityId,
        targetId: this.targetId,
        monthNum: monthNum
      }
      const { data } = await getComplainByTime(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData = data.data
      }
    },
    // 查询接口-右边图例
    async getRightEchartData() {
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      let level = ''
      if (this.echartsLeave > 0) {
        level = (this.echartsLeave - 1).toString()
      } else {
        level = '0'
        this.barclickTime = this.defaultDate
      }
      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: this.targetId,
        businessLevel: level,
        businessRoute: this.businessRoute,
        appartLevel: this.appartLevel,
        businessName: this.businessName,
        appartRoute: this.appartRoute,
        selectType: this.selectType
      }

      const { data } = await getComplainBySection(params)
      if (data) {
        this.chartRightData = data.data
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  position: relative;
  padding: 0px 40px 0px 40px;
  background: #f0f0f0;
}
.header-search {
  position: absolute;
  right: 30px;
  top: 30px;
  .block {
    display: flex;
  }
}
.service-header {
  padding: 24px 48px 0;
  box-sizing: border-box;
  width: 100%;
  background: #f0f0f0;
  display: flex;
  // justify-content: space-between;
  align-items: center;

  .header {
    width: 100%;
  }
  .time {
    display: flex;
    padding-top: 30px;
    span {
      display: inline-block;
      width: 80px;
      line-height: 32px;
    }
  }
}
.service-content {
  background: #fff;
}
.swiper-top {
  margin-bottom: 24px;
  // width: 90%;
  height: 102px;
  margin-left: 5%;
  margin-right: 5%;
  .swiper-container {
    // width: 100%;
    // height: 100%;
    .swiper-slide {
      width: 100%;
      height: 100%;
      background-color: white;
      padding: 20px 20px 40px;
      box-sizing: border-box;
      cursor: pointer;
      background-size: cover;
      background-repeat: no-repeat;
      border: 1px solid #f0f0f0;
      margin-top: 10px;
      border-radius: 4px;
      .slide-title {
        display: flex;
        padding-bottom: 5px;
        color: rgb(107, 107, 107);
        font-size: 15px;

        .el-icon-caret-right {
          font-size: 30px;
          position: absolute;
          left: 0;
          top: 0;
        }
        .score {
          position: absolute;
          top: 5px;
          left: 30px;
        }
      }
    }
  }
  .chosed-slide {
    box-shadow: inset -2px -2px 1px rgb(246, 225, 193);
  }
}
.slider-button {
  position: relative;
}
.swiper-button-prev,
.swiper-container-rtl .swiper-button-next,
.swiper-button-prevbar,
.swiper-container-rtl .swiper-button-nextbar {
  background-image: url();
  position: absolute;
  top: -85px;
  left: 0px;
  right: auto;
  z-index: 99;
}
.swiper-button-next,
.swiper-container-rtl .swiper-button-prev,
.swiper-button-nextbar,
.swiper-container-rtl .swiper-button-prevbar {
  background-image: url();
  position: absolute;
  top: -85px;
  right: 20px;
  left: auto;
  z-index: 99;
}
.el-icon-caret-left,
.el-icon-caret-right {
  font-size: 45px;
  color: rgb(255, 153, 0);
}
.swiper-button-disabled {
  color: rgb(248, 213, 159);
}

.el-cascader {
  :deep(.el-icon-arrow-down:before ) {
    content: "\E6E1";
  }
  :deep(.el-icon-arrow-down ) {
    transform: rotate(180deg);
  }
  :deep(.is-reverse.el-icon-arrow-down ) {
    transform: rotate(0deg);
  }
}
.Chart {
  width: 100%;
  height: 450px;
  display: flex;
  position: relative;
  justify-content: space-between;
  .leftChart,
  .rightChart {
    width: 45%;
    height: 450px;
  }
  button {
    position: absolute;
  }
  .rightBtn {
    right: 15px;
    top: 10px;
  }
  .leftBtn {
    left: 42%;
    top: 10px;
  }
  .goBack {
    cursor: pointer;
    position: absolute;
    left: 39%;
    top: 13px;
    z-index: 12;
  }
}
</style>
