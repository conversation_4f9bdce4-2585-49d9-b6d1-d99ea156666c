<template>
  <div class="hunsty" style="position: relative">
    <div :id="t" class="hunsty" :style="{ opacity: allData.length ? 1 : 0 }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: allData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
  </div>
</template>

<script>
import tool from "@/views/bj_proatal_web/utils/utils";
export default {
  name: "GLineChart",
  props: {
    // x轴 seriesData 对应的
    keymap: {
      type: Object,
      default: () => {
        return {
          xData: "appartName",
          seriesData: ["score", "yoyrate", "momrate"]
        };
      }
    },
    allData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    option: {
      type: Object,
      default: () => {
        return {};
      }
    },
    legendData: {
      type: Array,
      default: () => {
        return [];
      }
    },

    // 控制echart图显示多少
    barlineControl: {
      type: Array,
      default: () => {
        return ["bar", "line", "line"];
      }
    },
    // 是否是两条线
    isDoubleLine: {
      type: Boolean,
      default: true
    },
    // 横坐标出现多少个数据时 文字斜放
    minRotateXaxis: {
      type: String,
      require: false,
      default: () => "12"
    },
    // 设置dataZoomStyle里面的end属性，不为0显示dataZoom
    dataZoomEnd: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      t: "",
      chart: null,
      valueList: [],
      showChart: true,
      dataZoomStyle: {
        show: true,
        type: "slider",
        end: 40,
        handleSize: 0, // 滑动条的 左右2个滑动条的大小
        height: 8, // 组件高度
        left: 0, // 左边的距离
        right: 0, // 右边的距离
        bottom: 10, // 右边的距离
        handleColor: "#eee", // h滑动图标的颜色
        handleStyle: {
          borderColor: "#eee",
          borderWidth: "1",
          shadowBlur: 2,
          background: "#eee",
          shadowColor: "#eee"
        },
        backgroundColor: "#eee", // 两边未选中的滑动条区域的颜色
        showDataShadow: false, // 是否显示数据阴影 默认auto
        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        handleIcon:
          "M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",
        // filterMode: "filter",
        moveHandleStyle: { color: "#eee" }
      },
      units: {
        环比: "%",
        万投比: "‱"
      }
    };
  },
  computed: {
    xData() {
      const temp = this.allData.map(i => i[this.keymap["xData"]]);
      return temp;
    },
    seriesData() {
      const temp = [];
      this.keymap["seriesData"].forEach((i, idx) => {
        temp[idx] = this.allData.map(x => x[i]);
      });

      return temp;
    }
  },
  watch: {
    allData(v, oldv) {
      this.renderChart();
    }
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0);
  },
  mounted() {
    var _this = this;
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener("resize", function() {
      _this.chart.resize();
    });
  },
  methods: {
    initChart() {
      const dom = document.getElementById(this.t);
      this.chart = this.$echarts.init(dom);
      this.renderChart();
      const _self = this;
      this.chart.on("click", params => {
        // 逻辑处理放在组件外面
        _self.$emit("barclick", { xValue: params.name });
      });
    },
    renderChart() {
      const _self = this;
      const option = {
        grid: {
          top: "20%",
          left: "10%",
          right: "10%",
          bottom: "15%",
          containLabel: false
        },

        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255,255,255,1)",
          textStyle: {
            color: "#262626",
            align: "left"
          },
          confine: true,
          extraCssText: "box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)",
          formatter: function(datas) {
            if (datas && datas.length) {
              datas.forEach(i => {
                i.value = tool.dealFloatNum(i.value);
              });
            }

            let emostype = "";
            if (!_self.isDoubleLine) {
              try {
                const flagIndx = datas[0].dataIndex;
                const flagObj = _self.allData[flagIndx];

                emostype = Number(flagObj.eomstype);
              } catch (error) {}
            }
            console.log("emostype:", emostype);
            const html =
              "<span style='color:#777;line-height:30px;'>" +
              datas[0].name +
              "</span><br>" +
              datas[0]?.marker +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[0].seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              datas[0]?.value +
              (datas[0].seriesName.indexOf("满意度") != -1
                ? ""
                : String(datas[0].seriesName).indexOf("时长") != -1
                ? " 分钟"
                : datas[0].seriesName.includes("万投比")
                ? " ‱"
                : datas[0].seriesName === "投诉量"
                ? ""
                : " %") +
              "</span><br>" +
              (datas[1]?.marker || "") +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[1]?.seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              datas[1]?.value +
              (datas[1]?.value == "-" ? "" : "%") +
              "</span><br>" +
              (datas[2]?.marker || "") +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[2]?.seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              datas[2]?.value +
              (datas[2]?.value == "-" ? "" : "%") +
              "</span><br>";
            const onlyLineHtml = `
                <span style='color:#777;line-height:30px;'>${
                  datas[0].name
                }</span><br>${
              datas[0]?.marker
            }<span style='color:#222;padding:0 8px 0 8px;'>${
              datas[0].seriesName
            }
                </span><span style='font-weight:bold;line-height:30px;'>${datas[0]
                  ?.value +
                  (datas[0]?.seriesName.includes("万投比")
                    ? "‱"
                    : datas[0]?.seriesName.includes("占比")
                    ? "%"
                    : "")}</span><br>
                ${datas[1]?.marker ||
                  ""}<span style='color:#222;padding:0 8px 0 8px;'>${
              datas[1]?.seriesName || ""
            }
                </span><span style='font-weight:bold;line-height:30px;'>${
                  datas[1]?.value
                    ? datas[1]?.value
                    : datas[1]?.value === 0
                    ? datas[1]?.value
                    : ""
                }
                ${
                  datas[1]?.seriesName.includes("环比")
                    ? datas[1]?.value == "-"
                      ? ""
                      : "%"
                    : datas[1]?.seriesName.includes("万投比")
                    ? `‱<i  class="${
                        emostype < 1
                          ? "el-icon-caret-top"
                          : "el-icon-caret-bottom"
                      }"
                      style="color:${ emostype < 1 ? "red" : "green" };
                       display:${emostype === 1 || emostype === 0 ? 'inline' : 'none'}; font-size:20px"></i>`
                    : ""
                }</span>`;
            return _self.isDoubleLine ? html : onlyLineHtml;
          }
        },

        legend: {
          data: this.legendData,   selectedMode: false, // 是否允许点击
          top: "2%",
          textStyle: {
            color: "#747474"
          }
        },
        xAxis: {
          type: "category",
          // data: ['2022-02', '2022-03'],
          data: this.xData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            rotate: this.xData.length > Number(this.minRotateXaxis) ? 25 : 0,
            textStyle: {
              color: "#393939"
            }
          }
        },
        yAxis: [
          {
            // max: 100,
            type: "value",
            name: this.legendData[0],
            nameTextStyle: {
              color: "#393939",
              padding: [0, 0, 10, -40]
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#eeeeee"
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#393939"
              },
              formatter:
                this.legendData[0] && this.legendData[0].indexOf("万投比") != -1
                  ? "{value} ‱"
                  : this.legendData[0] &&
                    this.legendData[0].indexOf("占比") != -1
                  ? "{value}%"
                  : "{value}"
            }
          },
          {
            // max: 2,
            type: "value",
            name: _self.isDoubleLine ? "" : this.legendData[1],
            nameTextStyle: {
              color: "#393939",
              padding: [0, 0, 10, 35] // 四个数字分别为上右下左与原位置距离
            },
            position: "right",
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#393939"
              },
              // formatter: '{value}' + this.units[this.legendData[1]] || ''
              formatter:
                this.legendData[1] && this.legendData[1].indexOf("万投") != -1
                  ? "{value} ‱"
                  : "{value} %"
            }
          }
        ],
        dataZoom: this.dataZoomEnd
          ? [{ ...this.dataZoomStyle, end: this.dataZoomEnd }]
          : [],
        series: [
          {
            name: this.legendData[0],
            type: "bar",
            barWidth: 15,
            label: {
              show: true,
              position: "top",
              formatter: function(value) {
                return _self.legendData[0] == "占比"
                  ? value.value + "%"
                  : value.value;
              }
            },
            itemStyle: {
              normal: {
                color: "#0682FF",
                borderRadius: [1, 1, 0, 0]
              }
            },
            data: this.seriesData[0]
          },
          {
            name: this.legendData[1],
            type: "line",
            yAxisIndex: 1,
            showAllSymbol: true, // 小圆点
            symbol: "circle",
            symbolSize: 1,
            // smooth: true, // 平滑曲线
            itemStyle: {
              color: "rgb(246, 175, 56)",
              borderWidth: "2",
              borderColor: "rgb(246, 175, 56)"
            },
            lineStyle: {
              // color: '#7097F6' // 线颜色
              color: "rgb(246, 175, 56)" // 线颜色
            },
            data: this.seriesData[1]
          },
          _self.isDoubleLine
            ? {
                name: this.legendData[2],
                type: "line",
                yAxisIndex: 1,
                showAllSymbol: true, // 小圆点
                symbol: "circle",
                symbolSize: 1,
                // smooth: true, // 平滑曲线
                itemStyle: {
                  color: "#4CD1C3",
                  borderWidth: "2",
                  borderColor: "#4CD1C3"
                },
                lineStyle: {
                  color: "#4CD1C3" // 线颜色
                },
                data: this.seriesData[2]
              }
            : null
        ]
      };
      console.log("option", option);
      this.chart.setOption(option, true);
      this.chart.hideLoading();
      // if (this.allData.length == 0 || !this.allData) {
      //   this.chart.showLoading({
      //     text: '暂无数据',
      //     color: 'rgba(255, 255, 255, 0)',
      //     fontSize: 20,
      //     textColor: '#8a8e91',
      //     maskColor: 'rgba(255, 255, 255, 1)'
      //   })
      // } else {
      //   this.chart.hideLoading()
      // }
    }
  }
};
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
