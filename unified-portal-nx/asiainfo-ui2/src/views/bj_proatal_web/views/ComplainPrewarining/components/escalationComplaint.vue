<template>
  <div class="escalationComplaint">
    <div class="firstPart">
      <NumberTitle num="01" text="工信部申诉" />
      <div class="header-search">
        <div class="block">
          <!-- <div style="margin-right: 10px">
            <span class="labelcss">选择地市：</span>
            <el-cascader
              v-model="oneIdLists"
              class="el-cascader"
              placeholder="请选择地市"
              :options="optionsCascader2"
              :size="conditionsize"
              :props="props"
              @change="gridIdChange"
            />
          </div> -->
          <AreaPicker size="small" @change="gridIdChange" />
          <NewDatePicker :dateTypes="['date','month','daycumulative']" :tableName="'portal_indicator_appeal_data_info'" @change="dateOneChange"></NewDatePicker>
        </div>
      </div>
      <div class="content">
        <div class="content-top">
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>工信部申诉总量</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpOne"
              >导出</el-button>
            </div>
            <div ref="leftChart1" :style="{ width: '100%', height: '350px' }" />
          </div>
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>工信部百万申诉率</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpTwo"
              >导出</el-button>
            </div>
            <div ref="leftChart2" :style="{ width: '100%', height: '350px' }" />
          </div>
        </div>
        <div class="content-top">
          <div class="leftPie">
            <div slot="header" class="header-title">
              <span>部门申诉情况</span>
              <img
                v-if="level2 == 2"
                class="goBack"
                src="../../../assets/img/expCourse/return.png"
                @click="goBack1"
              >
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary "
                @click="exportUpThree"
              >导出</el-button>
            </div>
            <div
              ref="bottomChart"
              :style="{ width: '100%', height: '350px' }"
            />
          </div>
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>工信部申诉焦点问题</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpFour"
              >导出</el-button>
            </div>
            <el-table :data="tableData" stripe border @row-click="clickRowOne">
              <el-table-column
                type="index"
                label="排名"
                width="80"
                align="center"
              />
              <el-table-column prop="targetalias" label="热点业务名称" />
              <el-table-column prop="score" label="投诉量" width="120" />
              <el-table-column prop="partrate" label="投诉占比" width="120">
                <template slot-scope="scope">
                  <a
                    v-if="scope.row.partrate >= 0"
                  >{{ Math.ceil(scope.row.partrate * 100) }}%</a>
                  <a v-else>-</a>
                </template>
              </el-table-column>
              <el-table-column prop="momrate" label="投诉环比" width="120">
                <template slot-scope="scope">
                  <a
                    v-if="scope.row.momrate >= 0"
                  >{{ Math.ceil(scope.row.momrate * 100) }}%</a>
                  <a v-else>-</a>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="secondPart">
      <NumberTitle num="02" text="10080升级投诉" />
      <div class="header-search">
        <div class="block">
          <!-- <div style="margin-right: 10px">
            <span class="labelcss">选择地市：</span>
            <el-cascader
              v-model="twoIdLists"
              class="el-cascader"
              placeholder="请选择地市"
              :options="optionsCascader2"
              :size="conditionsize"
              :props="props"
              @change="gridChange"
            />
          </div> -->
          <AreaPicker size="small" @change="gridChange" />
          <NewDatePicker :dateTypes="['date','month','daycumulative']" :tableName="'portal_indicator_appeal_data_info'" @change="dateTwoChange"></NewDatePicker>
        </div>
      </div>
      <div class="content">
        <div class="content-top">
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>10080升级投诉总量</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpFive"
              >导出</el-button>
            </div>
            <div
              ref="secondChart1"
              :style="{ width: '100%', height: '350px' }"
            />
          </div>
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>10080的工信部百万申诉率</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small "
                type="primary"
                @click="exportUpSix"
              >导出</el-button>
            </div>
            <div
              ref="secondChart2"
              :style="{ width: '100%', height: '350px' }"
            />
          </div>
        </div>
        <div class="content-top">
          <div class="leftPie">
            <div slot="header" class="header-title">
              <span>部门申诉情况</span>
              <img
                v-if="level1 == 2"
                class="goBack"
                src="../../../assets/img/expCourse/return.png"
                @click="goBack2"
              >
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpSeven"
              >导出</el-button>
            </div>
            <div
              ref="secondChart3"
              :style="{ width: '100%', height: '350px' }"
            />
          </div>
          <div class="leftPie">
            <div slot="header" class="clearfix header-title">
              <span>10080的工信部申诉焦点问题</span>
              <el-button
                v-show="showExportBtn"
                class="leftBtn"
                size="small"
                type="primary"
                @click="exportUpEight"
              >导出</el-button>
            </div>
            <el-table :data="tableData2" stripe border @row-click="clickRowTwo">
              <el-table-column
                type="index"
                label="排名"
                width="80"
                align="center"
              />
              <el-table-column prop="targetalias" label="热点业务名称" />
              <el-table-column prop="score" label="投诉量" width="120" />
              <el-table-column prop="partrate" label="投诉占比" width="120">
                <template slot-scope="scope">
                  <a
                    v-if="scope.row.partrate >= 0"
                  >{{ Math.ceil(scope.row.partrate * 100) }}%</a>
                  <a v-else>-</a>
                </template>
              </el-table-column>
              <el-table-column prop="momrate" label="投诉环比" width="120">
                <template slot-scope="scope">
                  <a
                    v-if="scope.row.momrate >= 0"
                  >{{ Math.ceil(scope.row.momrate * 100) }}%</a>
                  <a v-else>-</a>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="地市投诉排名"
      :visible.sync="selfInspectionDialog"
      append-to-body
      :close-on-click-modal="true"
      width="370px"
      :modal="false"
    >
      <el-table :data="orgs" style="width: 100%">
        <el-table-column prop="appartname" align="center" label="地市" />
        <el-table-column prop="score" align="center" label="投诉量" />
        <el-table-column prop="partrate" align="center" label="投诉占比" />
        <el-table-column prop="momrate" align="center" label="投诉环比" />
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import NumberTitle from '../../../components/common/numberTitle'
import { exportUpgradeComplaint } from '@/api/complaint-analysis/api'
import { AreaByUser } from '@/api/mobilePhoneTariff'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker'

import {
  getMIITData,
  getMoveData,
  getDrillDownData
} from '@/api/complaint-topic'
import { getRegionRank } from '@/api/teleservice/api'
export default {
  name: 'EscalationComplaint',
  components: {
    NumberTitle,
    AreaPicker,
    NewDatePicker
  },
  inject: ['showExportBtn', 'mapPermission'],
  data() {
    const year = new Date().getFullYear()
    let month = Number(new Date().getMonth()) + 1
    if (month < 10) {
      month = '0' + month
    } else {
      month = '' + month
    }
    return {
      dataZoom: [
        // 给x轴设置滚动条
        {
          start: 0, // 默认为0
          end: 100 - 1500 / 31, // 默认为100
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          handleSize: 0, // 滑动条的 左右2个滑动条的大小
          height: 8, // 组件高度
          left: 0, // 左边的距离
          right: 0, // 右边的距离
          bottom: 26, // 右边的距离
          handleColor: '#eee', // h滑动图标的颜色
          handleStyle: {
            borderColor: '#eee',
            borderWidth: '1',
            shadowBlur: 2,
            background: '#eee',
            shadowColor: '#eee'
          },
          backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
          showDataShadow: false, // 是否显示数据阴影 默认auto
          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
          handleIcon:
            'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
          // filterMode: "filter",
          moveHandleStyle: {
            color: '#eee'
          }
        }
      ],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      MIITstatType: '3',
      moveStatType: '3',
      tableData: [],
      leftChart1: null,
      selfInspectionDialog: false,
      orgs: [],
      leftChart2: null,
      bottomChart: null,
      tableData2: [],
      chart1: null,
      chart2: null,
      chart3: null,
      chart4: null,
      chart5: null,
      chart6: null,
      level1: 1,
      level2: 1,
      defaultDateTwo: String(year) + String(month),
      defaultDateOne: String(year) + String(month),
      defaultDateTypeList: ['日', '月', '日累计'],
      optionsCascader2: [],
      twoIdLists: ['640000'],
      oneIdLists: ['640000'],
      gridIdOne: this.mapPermission.cityId,
      gridIdTwo: this.mapPermission.cityId,
      conditionsize: 'small'
    }
  },
  created() {},
  mounted() {
    this.init()
    this.initCharts()
  },
  methods: {
    exportUpOne() {
      // 导出
      if (
        this.MIITstatType == '3' &&
        this.defaultDateOne.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateOne =
          this.defaultDateOne.slice(0, 4) + '-' + this.defaultDateOne.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateOne.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateOne}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.MIITstatType}&targetId=301&chartNum=7&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '工信部申诉总量' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '工信部申诉总量' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpTwo() {
      // 导出
      if (
        this.MIITstatType == '3' &&
        this.defaultDateOne.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateOne =
          this.defaultDateOne.slice(0, 4) + '-' + this.defaultDateOne.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateOne.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateOne}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.MIITstatType}&targetId=302&chartNum=8&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '工信部百万申诉率' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '工信部百万申诉率' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpThree() {
      // 导出
      if (
        this.MIITstatType == '3' &&
        this.defaultDateOne.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateOne =
          this.defaultDateOne.slice(0, 4) + '-' + this.defaultDateOne.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateOne.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateOne}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.MIITstatType}&targetId=303&chartNum=9&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(response, '部门申诉情况' + '.xls')
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '部门申诉情况' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpFour() {
      // 导出
      if (
        this.MIITstatType == '3' &&
        this.defaultDateOne.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateOne =
          this.defaultDateOne.slice(0, 4) + '-' + this.defaultDateOne.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateOne.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateOne}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.MIITstatType}&targetId=304&chartNum=10&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '工信部申诉焦点问题' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '工信部申诉焦点问题' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpFive() {
      // 导出
      if (
        this.moveStatType == '3' &&
        this.defaultDateTwo.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateTwo =
          this.defaultDateTwo.slice(0, 4) + '-' + this.defaultDateTwo.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateTwo.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateTwo}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.moveStatType}&targetId=501&chartNum=11&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '10080升级投诉总量' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '10080升级投诉总量' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpSix() {
      // 导出
      if (
        this.moveStatType == '3' &&
        this.defaultDateTwo.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateTwo =
          this.defaultDateTwo.slice(0, 4) + '-' + this.defaultDateTwo.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateTwo.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateTwo}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.moveStatType}&targetId=502&chartNum=12&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '10080的工信部百万申诉率' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '10080的工信部百万申诉率' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpSeven() {
      // 导出
      if (
        this.moveStatType == '3' &&
        this.defaultDateTwo.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateTwo =
          this.defaultDateTwo.slice(0, 4) + '-' + this.defaultDateTwo.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateTwo.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateTwo}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.moveStatType}&targetId=503&chartNum=13&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(response, '部门申诉情况' + '.xls')
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '部门申诉情况' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportUpEight() {
      // 导出
      if (
        this.moveStatType == '3' &&
        this.defaultDateTwo.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateTwo =
          this.defaultDateTwo.slice(0, 4) + '-' + this.defaultDateTwo.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateTwo.slice(6, 7))
      const params = `?cityId=${this.gridIdOne}&opTime=${this.defaultDateTwo}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.moveStatType}&targetId=504&chartNum=14&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      exportUpgradeComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '10080的工信部申诉焦点问题' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '10080的工信部申诉焦点问题' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    goBack1() {
      this.level2 = 1
      this.getMIITDataList()
    },
    goBack2() {
      this.level1 = 1
      this.getMoveDataList()
    },
    clickRowOne(row) {
      // 工信部申诉表格行点击
      getRegionRank({
        restSerialNo: 'aSfZjj0L',
        opTime: this.defaultDateOne,
        cityId: this.gridIdOne,
        //  targetAlias: row.targetalias,
        targetId: row.targetid
      }).then((res) => {
        this.orgs = res.data.data.map((el, index) => {
          let partrate = ''
          let momrate = ''
          const momratetype = el.momratetype == '1' ? '-' : ''
          if (el.partrate != '-') {
            partrate = el.partrate * 100
          } else {
            partrate = el.partrate
          }
          if (el.momrate != '-') {
            momrate = momratetype + el.momrate * 100
          } else {
            momrate = el.momrate
          }
          return {
            ...el,
            indexs: index + 1,
            partrate: partrate + '%',
            momrate: momrate + '%'
          }
        })
        this.selfInspectionDialog = true
      })
    },
    clickRowTwo(row) {
      // 10086升级投诉表格行点击
      getRegionRank({
        restSerialNo: 'aSfZjj0L',
        opTime: this.defaultDateTwo,
        cityId: this.gridIdTwo,
        //  targetAlias: row.targetalias,
        targetId: row.targetid
      }).then((res) => {
        this.orgs = res.data.data.map((el, index) => {
          let partrate = ''
          let momrate = ''
          const momratetype = el.momratetype == '1' ? '-' : ''
          if (el.partrate != '-') {
            partrate = el.partrate * 100
          } else {
            partrate = el.partrate
          }
          if (el.momrate != '-') {
            momrate = momratetype + el.momrate * 100
          } else {
            momrate = el.momrate
          }
          return {
            ...el,
            indexs: index + 1,
            partrate: partrate + '%',
            momrate: momrate + '%'
          }
        })
        this.selfInspectionDialog = true
      })
    },
    getMoveDataList() {
      if (
        this.moveStatType == '3' &&
        this.defaultDateTwo.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateTwo =
          this.defaultDateTwo.slice(0, 4) + '-' + this.defaultDateTwo.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateTwo.slice(6, 7))
      const params = {
        statType: this.moveStatType,
        opTime: this.defaultDateTwo,
        cityId: this.gridIdTwo,
        monthNum: monthNum
      }
      getMoveData(params).then((res) => {
        if (res.code == 200) {
          const data = res.data.data
          Object.keys(data).forEach((k) => {
            if (k == 'first') {
              this.chart1 = this.initCharts(
                data[k],
                this.$refs.secondChart1,
                1
              )
            } else if (k == 'second') {
              this.chart2 = this.initCharts(
                data[k],
                this.$refs.secondChart2,
                2
              )
            } else if (k == 'third') {
              this.chart3 = this.initCharts(
                data[k],
                this.$refs.secondChart3,
                3
              )
            } else {
              this.tableData2 = data[k]
            }
          })
        }
      })
    },
    getMIITDataList() {
      if (
        this.MIITstatType == '3' &&
        this.defaultDateOne.indexOf('-', 0) == '-1'
      ) {
        this.defaultDateOne =
          this.defaultDateOne.slice(0, 4) + '-' + this.defaultDateOne.slice(4)
      }
      const monthNum = parseFloat(this.defaultDateOne.slice(6, 7))
      const params = {
        statType: this.MIITstatType,
        opTime: this.defaultDateOne,
        cityId: this.gridIdOne,
        monthNum: monthNum
      }
      getMIITData(params).then((res) => {
        if (res.code == 200) {
          const data = res.data.data
          Object.keys(data).forEach((k) => {
            if (k == 'first') {
              this.chart4 = this.initCharts(data[k], this.$refs.leftChart1, 4)
            } else if (k == 'second') {
              this.chart5 = this.initCharts(data[k], this.$refs.leftChart2, 5)
            } else if (k == 'third') {
              this.chart5 = this.initCharts(data[k], this.$refs.bottomChart, 6)
            } else {
              this.tableData = data[k]
            }
          })
        }
      })
    },
    getAixsLable(nameList, num) {
      let axisLabel = {
        rotate: nameList.length > 8 || (nameList[0] && nameList[0].length > 10) ? 20 : 0 // 文字旋转
      }
      if ((num == 3 && this.level1 == 2) || (num == 6 && this.level2 == 2)) {
        axisLabel = {
          interval: 0,
          formatter: function(params) {
            var newParamsName = ''
            const paramsNameNumber = params.length
            const provideNumber = 7 // 单行显示文字个数
            const rowNumber = Math.ceil(paramsNameNumber / provideNumber)
            if (paramsNameNumber > provideNumber) {
              for (let p = 0; p < rowNumber; p++) {
                var tempStr = ''
                var start = p * provideNumber
                var end = start + provideNumber
                if (p === rowNumber - 1) {
                  tempStr = params.substring(start, paramsNameNumber)
                } else {
                  tempStr = params.substring(start, end) + '\n'
                }
                newParamsName += tempStr
              }
            } else {
              newParamsName = params
            }
            return newParamsName
          }
        }
      }
      return axisLabel
    },
    initCharts(data, dom, num) {
      if (!data) return
      this.$nextTick(() => {
        // if (
        //    this.leftChart1 !== null &&
        //   this.leftChart1 !== "" &&
        //    this.leftChart1 !== undefined
        // ) {
        //    this.leftChart1.dispose();
        // }
        let leftChart1 = null
        if (num == 1) {
          leftChart1 = this.chart1
        }
        if (num == 2) {
          leftChart1 = this.chart2
        }
        if (num == 3) {
          leftChart1 = this.chart3
        }
        if (num == 4) {
          leftChart1 = this.chart4
        }
        if (num == 5) {
          leftChart1 = this.chart5
        }
        if (num == 6) {
          leftChart1 = this.chart6
        }
        const lineList = []
        const columnarList = []
        const nameList = []
        let xName = '申诉量'
        if (num == 2 || num == 5) {
          xName = '申诉率'
        } else {
          xName = '申诉量'
        }
        let columnar = ''
        Object.keys(data).map((el) => {
          const statdate = data[el].statdate || data[el].targetalias || '-'
          if (xName == '申诉量') {
            columnar = data[el].score || '0'
          } else {
            columnar = parseFloat(data[el].score).toFixed(2).toString() || '0'
          }

          const line = data[el].momrate
          const momratetype = data[el].momratetype == '1' ? '-' : ''
          lineList.push(momratetype + line)
          columnarList.push(columnar)
          nameList.push(statdate)
        })
        leftChart1 = this.$echarts.init(dom)
        if (this.level1 == 2 || this.level2 == 2) {
          this.dataZoom[0].start = 15
        } else {
          this.dataZoom[0].start = 0
        }
        const option = {
          dataZoom:
            nameList.length > 8 || num == 3 || num == 6 ? this.dataZoom : null,
          color: ['#7097F6', '#7CDDD3', '#FFBE4D', '#F9906F'],
          grid: {
            top: '15%',
            left: '5%',
            right: '5%',
            bottom: '10%',
            containLabel: true
          },

          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255,255,255)'
          },

          legend: {
            itemWidth: 10,
            itemHeight: 5,
            top: '5%',
            data: [xName, '环比']
          },
          xAxis: [
            {
              type: 'category',
              data: nameList,
              axisLabel: this.getAixsLable(nameList, num),
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: xName,
              axisLabel: {
                formatter: '{value} '
              }
            },
            {
              type: 'value',
              name: '环比',
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                show: false // 不显示网格线
              },
              axisLabel: {
                formatter: function(value) {
                  return value * 100 + '%'
                }
              }
            }
          ],
          series: [
            {
              name: xName,
              type: 'bar',
              barWidth: 15,
              tooltip: {
                valueFormatter: function(value) {
                  return value
                }
              },
              data: columnarList
            },
            {
              name: '环比',
              type: 'line',
              yAxisIndex: 1,
              lineStyle: {
                color: 'rgba(255,153,0,1)',
                width: 1
              },
              tooltip: {
                valueFormatter: function(value) {
                  return value && value != '-' && value != 'undefined' && !isNaN(value)
                    ? (value * 100).toFixed(2) + '%'
                    : '-'
                }
              },
              data: lineList
            }
          ]
        }
        leftChart1.clear()
        if (data.length) {
          leftChart1.setOption(option, true)
        }
        if (num == 3 || num == 6) {
          leftChart1.getZr().on('click', (data) => {
            const pointInPixel = [data.offsetX, data.offsetY]
            if (leftChart1.containPixel('grid', pointInPixel)) {
              // 点击第几个柱子
              const pointInGrid = leftChart1.convertFromPixel(
                { seriesIndex: 0 },
                pointInPixel
              )
              // 也可以通过data.offsetY 来判断鼠标点击的位置是否是图表展示区里面的位置
              // 也可以通过name[xIndex] != undefined，name是x轴的坐标名称来判断是否还是点击的图表里面的内容
              // x轴数据的索引
              const xIndex = pointInGrid[0]
              // y轴数据的索引
              const yIndex = pointInGrid[1]
              const a = nameList[xIndex]
              if (num == 3) {
                if (this.level1 == 2) {
                  return
                }
                this.getDrillDown(a, '303')
                this.level1++
              } else if (num == 6) {
                if (this.level2 == 2) {
                  return
                }
                this.getDrillDown(a, '503')
                this.level2++
              } else {
                return a
              }
            }
          })
        }

        window.addEventListener('resize', () => {
          leftChart1.resize()
        })
      })
    },
    getDrillDown(val, id) {
      // 部门点击下钻
      let params = {}
      if (id == '303') {
        params = {
          statType: this.MIITstatType,
          opTime: this.defaultDateOne,
          cityId: this.gridIdOne,
          appartName: val,
          targetId: id
        }
      } else {
        params = {
          statType: this.moveStatType,
          opTime: this.defaultDateTwo,
          cityId: this.gridIdTwo,
          appartName: val,
          targetId: id
        }
      }
      getDrillDownData(params).then((res) => {
        if (res.code == 200) {
          if (id == '303') {
            this.initCharts(res.data.data, this.$refs.secondChart3, 3)
          } else {
            this.initCharts(res.data.data, this.$refs.bottomChart, 6)
          }
        }
      })
    },
    init() {
      // 初始化获取所有区域
      // const { data, code } = await AreaByUser({
      //   restSerialNo: '6MujCDCs'
      // })
      // if (code == 200) {
      //   this.optionsCascader2 = data[0]
      //   this.getMIITDataList()
      //   this.getMoveDataList()
      // }

      this.getMIITDataList()
      this.getMoveDataList()
    },
    dateTwoType(val) {
      if (val == '日') {
        this.moveStatType = '1'
      } else if (val == '月') {
        this.moveStatType = '3'
      } else if (val == '日累计') {
        this.moveStatType = '5'
      }
      this.$nextTick(() => {
        this.getMoveDataList()
      })
    },
    dateOneChange(params) {
      const [ dateType, val ] = params
      if (dateType == '日') {
        this.MIITstatType = '1'
      } else if (dateType == '月') {
        this.MIITstatType = '3'
      } else if (dateType == '日累计') {
        this.MIITstatType = '5'
      }
      this.defaultDateOne = val
      this.$nextTick(() => {
        this.getMIITDataList()
      })
    },
    dateTwoChange(params) {
      const [ dateType, val ] = params
      if (dateType == '日') {
        this.moveStatType = '1'
      } else if (dateType == '月') {
        this.moveStatType = '3'
      } else if (dateType == '日累计') {
        this.moveStatType = '5'
      }
      this.defaultDateTwo = val
      this.$nextTick(() => {
        this.getMoveDataList()
      })
    },
    gridIdChange(val) {
      this.gridIdOne = this.getAllGridPathObj(val)
      this.getMIITDataList()
    },
    gridChange(val) {
      this.gridIdTwo = this.getAllGridPathObj(val)
      this.getMoveDataList()
    },
    getAllGridPathObj(keyarr) {
      const len = keyarr.length
      let gridId = ''
      if (len) {
        if (len == 1) {
          gridId = keyarr[0]
        } else if (len == 2) {
          gridId = keyarr[1]
        } else if (len == 3) {
          gridId = keyarr[2]
        }
      }
      return gridId
    }
  }
}
</script>
<style lang="scss" scoped>
.escalationComplaint {
  // position: relative;
  width: 100%;
  height: 100%;
  background-color: rgba(242, 242, 242, 1);
  padding: 30px 40px 40px 40px;
}
.firstPart {
  position: relative;
}
.secondPart {
  position: relative;
}
.header-search {
  position: absolute;
  right: 30px;
  top: 40px;
  .block {
    display: flex;
  }
}
.el-cascader {
  :deep(.el-icon-arrow-down:before ) {
    content: "\E6E1";
  }
  :deep(.el-icon-arrow-down ) {
    transform: rotate(180deg);
  }
  :deep(.is-reverse.el-icon-arrow-down ) {
    transform: rotate(0deg);
  }
}
.content {
  position: relative;
  margin-top: 20px;
  width: 100%;
  min-height: 450px;
  background: #ffffff;
  .content-top {
    width: 100%;
    height: 450px;
    display: flex;
    justify-content: space-around;
    .leftPie {
      width: 46%;
      height: 100%;
      .header-title {
        position: relative;
        width: 100%;
        span {
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: bold;
          font-size: 16px;
          line-height: 32px;
        }
      }
    }
    .leftBtn {
      position: absolute;
      right: 15px;
      top: 7px;
    }
    .goBack {
      cursor: pointer;
      position: absolute;
      left: 86%;
      top: 11px;
      z-index: 12;
    }
  }
  .el-table {
    margin-top: 30px;

    :deep( .el-table--border .el-table__cell ) {
      border-right: 2px solid #dfe6ec;
    }
  }
}
</style>
