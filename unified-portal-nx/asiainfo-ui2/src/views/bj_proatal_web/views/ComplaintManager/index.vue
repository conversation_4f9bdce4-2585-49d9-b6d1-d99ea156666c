<template>
  <div class="solicitude-task">
    <Banner title="投诉指标与预警体系" desc="投诉指标监控与预警与预警体系专题展示" />
    <div class="layout" style="margin-top:24px">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <el-form-item label="业务场景">
                <el-cascader
                  v-model="csmcustomerlableSeleted"
                  :options="treeLabelData"
                  :props="cascaderProps"
                  collapse-tags
                  popper-class="treeLabelDataCascader"
                  :show-all-levels="false"
                  clearable
                />

              </el-form-item>
              <el-form-item label="投诉预警类型" prop="isComplaint">
                <el-select v-model="formInline.isComplaint" placeholder="请选择" :size="conditionsize">
                  <el-option label="是" value="Y" />
                  <el-option label="否" value="N" />
                </el-select>
              </el-form-item>

              <el-form-item label="当前工作组" prop="gridId">
                <el-cascader
                  v-model="formInline.gridId"
                  :options="optionsCascader"
                  :size="conditionsize"
                  clearable
                  :props="{
                    checkStrictly:true
                  }"
                  :show-all-levels="false"
                />
              </el-form-item>

              <el-form-item label="是否可用" prop="type">
                <el-select v-model="formInline.type" placeholder="请选择" :size="conditionsize" style="width:100px;">
                  <el-option label="显性" value="1" />
                  <el-option label="潜在" value="0" />
                </el-select>
              </el-form-item>

              <el-form-item label="归属地市" prop="phoneNo">
                <el-input
                  v-model="formInline.phoneNo"
                  :size="conditionsize"
                  placeholder="请输入手机号进行搜索"
                >
                  <!-- <el-button slot="append" @click="query">搜索</el-button> -->
                </el-input>
              </el-form-item>

              <el-form-item label="预警级别类型" prop="repairStatus">
                <el-select v-model="formInline.repairStatus" placeholder="请选择" :size="conditionsize">
                  <el-option v-show="activeName==0||activeName==3" label="下发未接触成功" value="0" />
                  <el-option v-show="activeName==0||activeName==3" label="已接触未修复" value="1" />
                  <el-option v-show="activeName==0||activeName==2" label="已修复" value="2" />
                  <el-option v-show="activeName==0||activeName==3" label="待修复" value="3" />

                </el-select>
              </el-form-item>

               <el-form-item label="提醒号码" prop="repairStatus">
               <el-input></el-input>
              </el-form-item>

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
              </el-form-item>

            </el-form>
            <div class="btnbox">
              <!-- <el-button :size="conditionsize" type="default">导入客户</el-button> -->
              <!-- <el-button :size="conditionsize" type="default" @click="drawer=true">生成客群</el-button> -->
              <!-- <el-button :size="conditionsize" type="default">下发工单</el-button> -->
            </div>
          </div>
        </div>
        <div class="btnOperate">
          <el-button>删除</el-button>
          <el-button type="primary">启用</el-button>
          <el-button type="primary">查看预警</el-button>
          <el-button type="primary">编辑</el-button>
          <el-button type="primary" @click="addTable">新增</el-button>
        </div>
        <div class="conbox" style="background:#fff;">
          <div style="margin-top:24px">
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              max-height="560"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                type="selection"
                width="55"
              />
              <el-table-column
                v-for="item in columnList"
                :key="item.name"
                :prop="item.key"
                :label="item.name"
                :width="item.width?item.width:'unset'"
                align="center"
                :show-overflow-tooltip="true"
                :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
              >
                <template slot-scope="scope">
                  <span v-if="item.key=='type'">
                    {{ scope.row[item.key]==1?'显性':scope.row[item.key]==0?'潜在':'' }}
                  </span>
                  <span v-else-if="item.key=='isComplaint'">
                    {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
                  </span>

                  <span v-else-if="item.key=='isSend'">
                    {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
                  </span>
                  <span v-else-if="item.key=='repairStatus'">
                    {{ scope.row[item.key]==3?'待修复':scope.row[item.key]==2?'已修复':scope.row[item.key]==1?'已接触未修复':scope.row[item.key]==0?'下发未接触成功':'' }}
                  </span>
                  <!-- 不满指标 低于6分显示红  -->
                  <span v-else-if="item.key=='csmcustomerlableVisibleTxt'">
                    <span v-for="(x,ix) in scope.row[item.key]" :key="ix">
                      <div v-if="x.point>-1">
                        <!-- <span>{{ scope.row[item.key].length }}</span> -->
                        <span :style="{color:0<x.point<7?'red':''}">{{ x.txt }}</span>
                        <span v-show="ix!==scope.row[item.key].length-1">{{ '、' }}</span>
                      </div>
                    </span>

                  </span>

                  <span v-else>{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>

              <el-table-column
                label="操作"
                align="center"
                width="200"
              >
                <template slot-scope="scope">
                  <div class="optionbtnbox">
                    <div>
                      <span class="coloryellow" @click="openCheckDetail(scope.row)">查看</span>
                    </div>
                    <div>
                      <span class="coloryellow" @click="sendMsg(scope.row)">短信回访</span>
                    </div>
                    <div>

                      <el-popconfirm
                        confirm-button-text="删除"
                        cancel-button-text="取消"
                        icon="el-icon-info"
                        icon-color="red"
                        title="确定删除本条数据？"
                        @confirm="dele(scope.row)"
                      >
                        <span slot="reference" class="coloryellow">删除</span>
                      </el-popconfirm>

                    </div>
                  </div>
                </template>
              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>
    <el-dialog title="评价短信群发" width="55%" class="msgdialog" :visible.sync="dialogFormVisible" :append-to-body="true">
      <msgAdd v-if="dialogFormVisible" :check-row="checkRow" @cancel="cancel" />
    </el-dialog>
    <el-dialog title="查看" :visible.sync="dialogFormVisible2" width="75%" class="detaildialog">
      <Check
        v-if="dialogFormVisible2"
        :check-row="checkRow"
        :colum-names-map="columNamesMap"
        :colum-names-point-map="columNamesPointMap"
      />
    </el-dialog>
  </div>
</template>
<script>
import Banner from './../../components/common/Banner.vue'
import customerAddForm from './../../components/common/CustomerAddForm.vue'
import msgAdd from './../../components/common/msgAdd'
import Check from './Checkdetail'
import { csmcustomerlable, getColumScore, getColumNames, csmcustomerbaseinfo, delId, getByPhoneNo, getAllGrides, labelLists } from '@/api/customer/index'
import tool from '@/views/bj_proatal_web/utils/utils'
import {mapMutations} from 'vuex';
export default {
  name:'ComplaintManager',
  components: {
    Banner,
    customerAddForm,
    msgAdd,
    Check
  },
  data() {
    return {
      checkRow: {},
      cascaderProps: {
        multiple: true,
        checkStrictly: true
      },
      // 不满意指标map
      columNamesMap: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明
      treeLabelData: [],

      csmcustomerlableSeleted: [],

      formInline: {
        satisfaction: '',
        timeRange: [],
        isComplaint: '',
        repairStatus: '',
        gridId: [],
        phoneNo: ''
      },
      tableLoading: false,
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      activeName: '0',
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      multipleSelection: [],
      satisfactionOpts: [
        { label: '0~1 分', value: '0,1' },
        { label: '1~2 分', value: '1,2' },
        { label: '2~3 分', value: '2,3' },
        { label: '3~4 分', value: '3,4' },

        { label: '4~5 分', value: '4,5' },

        { label: '5~6 分', value: '5,6' },

        { label: '6~7 分', value: '6,7' },

        { label: '7~8 分', value: '7,8' },

        { label: '8~9 分', value: '8,9' },

        { label: '9~10 分', value: '9,10' }

      ], // 得分
      tableDatas: [],
      columnList: [
        {
          key: 'batch',
          name: '数据批次'
        },
        {
          key: 'userId',
          name: '用户ID'
        },
        {
          key: 'csmcustomerlableVisibleTxt',
          name: '不满指标'
        },
        {
          key: 'phoneNo',
          name: '手机号码',
          width: '120px'
        },

        {
          key: 'type',
          name: '类型'
        },

        {
          key: 'isComplaint',
          name: '是否投诉'
        },
        {
          key: 'isSend',
          name: '是否下发短信'
        },
        {
          key: 'lastContactTime',
          name: '最近接触时间',
          width: '150px'
        },

        {
          key: 'repairStatus',
          name: '修复状态'
        }

      ],
      drawer: false,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      optionsCascaderExpand: [],

      // 不满意各个指标的不满意分数对应map
      columNamesPointMap: null

    }
  },
  watch: {
    csmcustomerlableSeleted: {
      deep: true,
      handler: function(v, oldv) {
        console.log('csmcustomerlableSeleted:', v)
      }
    },
    activeName: {
      deep: true,
      handler: function(v, oldv) {
        this.resetForm('queryForm')
        this.page.current = 1
        if (v == 2) {
          this.$nextTick(() => {
            this.formInline.repairStatus = '2'
          })
        } else {
          this.formInline.repairStatus = ''
        }

        this.$nextTick(() => {
          this.queryList()
        })
      }
    }
  },
  created() {
    // 获取每个指标对应的不满意的值
    getColumScore().then(res => {
      const { code, data } = res
      if (code == 200) {
        this.columNamesPointMap = data
        this.init()
      }
    })
  },
  mounted() {

  },
  methods: {
     ...mapMutations(['changeFormData']),
    init() {
      const p0 = labelLists()// 获取所有标签tree
      const p1 = getColumNames()// 获取与标签对应的map

      Promise.all([p0, p1]).then(values => {
        const res0 = values[0]
        const res1 = values[1]

        let { code, data } = res0
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
            this.$store.commit('UPDATE_TREE_LABEL_DATA', data)
          }
        }

        if (res1.code == 200) {
          this.columNamesMap = res1.data
          this.$store.commit('UPDATE_COLUMNAMESMAP', res1.data)
        }

        this.queryList()
      })

      // 获取所有网格
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr
          this.handlerCascaderDataExpand(arr)
        }
      })
    },
    // 获取不满意指标map
    getColumNames() {
      getColumNames().then(res => {
        const { code, data } = res
        if (code == 200) this.columNamesMap = data
      })
    },
    // 获取所有标签
    labelLists() {
      labelLists().then(res => {
        let { code, data } = res
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }
      })
    },
    cancel() {
      this.dialogFormVisible = false
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    },
    handleSelectionChange(val) {
      console.log('val:', val)
      this.multipleSelection = val
    },
    openCheckDetail(row) {
      this.checkRow = row
      this.dialogFormVisible2 = true
    },
    sendMsg(row) {
      console.log('row:', row)
      this.checkRow = row
      this.dialogFormVisible = true
    },
    // 删除
    dele(row) {
      delId(row.id).then(res => {
        console.log(row)
        const { code, data } = res
        if (code == 200) {
          this.queryList()
        }
      })
    },

    getByPhoneNo(row) {
      getByPhoneNo(row.phoneNo).then(res => {
        const { code, data } = res
      })
    },
    query() {
      this.page.current = 1
      this.queryList()
    },

    // 查询表格数据
    queryList() {
      let columFiles = []
      // endContactTime beginContactTime
      const { formInline, page, csmcustomerlableSeleted } = this
      const { timeRange } = formInline
      let params = Object.assign({}, formInline, { pagination: { currentPage: page.current, pageSize: page.size }})

      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.beginContactTime = tool.formatterDate(timeRange[0], 'yyyy-MM-dd')
        params.endContactTime = tool.formatterDate(timeRange[1], 'yyyy-MM-dd')
      }
      if (params.satisfaction) {
        const temparr = params.satisfaction.split(',')
        params.beginSatisfaction = temparr[0]
        params.endSatisfaction = temparr[1]
      }

      // if (Array.isArray(params.gridId) && params.gridId.length) {
      //   params.gridId = params.gridId[params.gridId.length - 1]
      // }

      // 处理网格
      console.log('网格：', formInline.gridId)
      if (Array.isArray(formInline.gridId)) {
        const gridObj = this.getAllGridPathObj(formInline.gridId)
        params = Object.assign(params, gridObj)
      }
      console.log('params:', params)

      if (csmcustomerlableSeleted.length) {
        columFiles = csmcustomerlableSeleted.map(j => {
          const len = j.length
          return j[len - 1]
        })
      }

      params.columFiles = columFiles || []

      delete params.satisfaction
      delete params.timeRange
      delete params.total

      console.log('params:', params)
      this.tableLoading = true
      csmcustomerbaseinfo(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total } = data
 
          

          // 处理不满意指标
          const { columNamesMap = {}, columNamesPointMap = {}} = this
          records.forEach(i => {
            i.csmcustomerlableVisibleTxt = []
            for (const ke in i) {
              if (columNamesMap.hasOwnProperty(ke) && columNamesPointMap.hasOwnProperty(ke) && i[ke] && i[ke] < columNamesPointMap[ke] && i[ke] != -1) {
                let strobj = { txt: '', point: '' }
                strobj = {
                  txt: `${columNamesMap[ke]}(${i[ke]}) `,
                  point: i[ke] }

                i.csmcustomerlableVisibleTxt.push(strobj)
                console.log(i.csmcustomerlableVisibleTxt)
              }
            }
          })

          this.tableDatas = records || []
          //这里是vuex方法
          changeFormData({FormData: records})
          this.page.total = total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    resetForm(formName) {
      this.formInline.gridId = []
      this.formInline.timeRange = []
      this.csmcustomerlableSeleted = []
      this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },
    handleClickTab() {

    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        cityId: null,
        cityName: null,
        countyId: null,
        countyName: null,
        gridId: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        if (level == 1) {
          obj.cityId = tar.value
          obj.cityName = tar.label
        } else if (level == 2) {
          obj.countyId = tar.value
          obj.countyName = tar.label
        } else if (level == 3) {
          obj.gridId = tar.value
          obj.gridName = tar.label
        }
      }

      return obj
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    handleCloseDrwer(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .ca
        tch(_ => {})
    },
    //按钮区域新增
    addTable(){
      this.$router.push('/Complaintoperate')

    }

  }
  //

}
</script>
<style >
.el-popper.el-cascader__dropdown.treeLabelDataCascader .el-checkbox.is-disabled .el-checkbox__input.is-disabled{

  opacity: 0!important;

}
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}
</style>

<style lang='scss' scoped>

.msgdialog /deep/.el-form-item__label{
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog /deep/.el-checkbox__label{
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
/deep/.el-dialog__header{
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
/deep/.el-dialog__headerbtn{
  top:12px;
  line-height: 20px;

}
/deep/.el-select .el-input.is-focus .el-input__inner{
  border-color: #ff9900;
}
/deep/.el-textarea__inner:focus{
  border-color: #ff9900;
}

/deep/ .el-cascader__tags{
  .el-tag{
    >span{
      flex: auto !important;
    }
  }
}
/deep/ .el-select__tags{
    .el-tag{
        >span{
        flex: auto !important;
        }
    }
}
/deep/ .el-tag.el-tag--info{
    display: inline-block;
}

.solicitude-task{
        background-color: rgba(242, 242, 242, 1);
        min-height: 100vh;
        /deep/.el-input-group__append, /deep/.el-input-group__prepend{
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            padding: 0 50px;

            /deep/.el-tabs__nav-wrap::after{
              height:1px;
              background:#E6E6E6;
            }
            /deep/.el-tabs__active-bar{
              background:#FF9900;
            }
            /deep/.el-tabs__item.is-active{
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            /deep/.el-tabs__item{
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            /deep/.el-form-item.el-form-item--medium{margin-right:20px}
            /deep/.el-form-item__content .el-input-group{
              vertical-align: middle;
            }
            /deep/.el-pagination{text-align: right;}
            .speformitem /deep/.el-form-item__label{
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        /deep/.el-icon-arrow-down:before {
          content: "\E6E1";
        }
       /deep/.el-icon-arrow-down{
         transform: rotate(180deg);
       }
       /deep/.is-reverse.el-icon-arrow-down{
         transform: rotate(0deg);
       }

  }

    }

 /deep/.el-table--medium .el-table__cell{
   padding:8px 0;
 }
/deep/.el-table th.el-table__cell.is-leaf,
/deep/.el-table td.el-table__cell{
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;
  margin-top:55px;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
.btnOperate{
  float:right;
  margin-right:15px
}

</style>
