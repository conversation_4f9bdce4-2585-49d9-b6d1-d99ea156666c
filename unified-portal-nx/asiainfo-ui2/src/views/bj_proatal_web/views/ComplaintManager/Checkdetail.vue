<template>
  <div>
    <div>
      <span class="infoitem">
        <span class="lab">用户ID ：</span>
        <span class="vl">{{ checkRow.userId }}</span>
      </span>
      <span class="infoitem">
        <span class="lab">手机号码 ：</span>
        <span class="vl">{{ checkRow.phoneNo }}</span>
      </span>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableDatas"
        style="width: 100%"
      >

        <el-table-column
          v-for="item in columnList"
          :key="item.name"
          :prop="item.key"
          :label="item.name"
          align="center"
          :show-overflow-tooltip="true"
          min-width="100"
        >
          <template slot-scope="scope" style="height:30px;">
            <span v-if="item.key=='type'">
              {{ scope.row[item.key]==1?'显性':scope.row[item.key]==2?'潜在':'' }}
            </span>
            <span v-else-if="item.key=='isComplaint'">
              {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
            </span>

            <span v-else-if="item.key=='isSend'">
              {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
            </span>

            <span v-else-if="item.key=='csmcustomerlableVisibleTxt'">

              <span v-for="(x,ix) in scope.row[item.key]" :key="ix">
                <span :style="{color:x.point<7?'red':''}">{{ x.txt }}</span>
                <span v-show="ix!==scope.row[item.key].length-1">{{ '、' }}</span>
              </span>

            </span>

            <span v-else-if="item.key=='repairStatus'">
              {{ scope.row[item.key]==3?'待修复':scope.row[item.key]==2?'已修复':scope.row[item.key]==1?'已接触未修复':scope.row[item.key]==0?'下发未接触成功':'' }}
            </span>

            <span v-else>{{ scope.row[item.key] }}</span>
          </template>

        </el-table-column>

        <!-- <el-table-column
          label="操作"
          align="center"
          width="200"
        >
          <template slot-scope="scope">
            <div class="optionbtnbox">
              <div>
                <span class="coloryellow">查看</span>
              </div>
              <div>
                <span>发送短信</span>
              </div>
              <div>
                 <el-popconfirm
                        confirm-button-text="删除"
                        cancel-button-text="取消"
                        icon="el-icon-info"
                        icon-color="red"
                        title="确定删除本条数据？"
                        @confirm="dele(scope.row)"
                      >
                        <span slot="reference" class="coloryellow">删除</span>
                      </el-popconfirm>
              </div>
            </div>
          </template>
        </el-table-column> -->

      </el-table>
    </div>
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        v-if="tableDatas"
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { getByPhoneNo } from '@/api/customer/index'
export default {
  props: {
    checkRow: {
      type: Object,
      required: true
    },

    columNamesMap: {
      type: Object,
      required: true
    },
    columNamesPointMap: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      tableDatas: [],
      tableLoading: false,
      columnList: [
        {
          key: 'batch',
          name: '数据批次'
        },
        {
          key: 'userId',
          name: '用户ID'
        },
        {
          key: 'csmcustomerlableVisibleTxt',
          name: '不满指标'
        },
        {
          key: 'phoneNo',
          name: '手机号码'
        },

        {
          key: 'type',
          name: '类型'
        },

        {
          key: 'isComplaint',
          name: '是否投诉'
        },
        {
          key: 'isSend',
          name: '是否下发短信'
        },
        {
          key: 'lastContactTime',
          name: '最近接触时间'
        },

        {
          key: 'repairStatus',
          name: '修复状态'
        }

      ],
      page: {
        current: 1,
        size: 10,
        total: 0
      }

    }
  },
  watch: {
    checkRow: {
      deep: true,
      handler: function(v, oldv) {
        this.getByPhoneNo()
      }
    }
  },
  mounted() {
    this.getByPhoneNo()
  },
  methods: {
    getByPhoneNo() {
      const { page } = this
      const p = {
        current: page.current,
        size: page.size,
        phoneNo: this.checkRow.phoneNo
      }
      this.tableLoading = true
      getByPhoneNo(p).then(res => {
        const { code, data } = res

        if (code == 200) {
          // 处理不满意指标

          const { columNamesMap = {}, columNamesPointMap = {}} = this
          data.records.forEach(i => {
            i.csmcustomerlableVisibleTxt = []
            for (const ke in i) {
              if (columNamesMap.hasOwnProperty(ke) && columNamesPointMap.hasOwnProperty(ke) && i[ke] && i[ke] < columNamesPointMap[ke]) {
                let strobj = { txt: '', point: '' }
                strobj = {
                  txt: `${columNamesMap[ke]}(${i[ke]}) `,
                  point: i[ke] }

                i.csmcustomerlableVisibleTxt.push(strobj)
              }
            }
          })

          this.page.total = data.total
          this.page.current = data.current
          this.page.size = data.size
          this.tableDatas = data.records
        }
      }).finally(() => { this.tableLoading = false })
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      // 下方添加查询逻辑
      this.$nextTick(() => {
        this.getByPhoneNo()
      })
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val

      // 下方添加查询逻辑
      this.$nextTick(() => {
        this.getByPhoneNo()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.infoitem{
    margin-bottom: 10px;
    display:inline-block;
    margin-right:50px;
    font-weight: bolder;
    .lab{

        font-size: 14px;

        text-align: right;
        color: #262626;
        letter-spacing: -0.27px;
        padding-right:10px;

    }
    .vl{

    }
}
/deep/.el-pagination{text-align: right;}

/deep/.el-table--medium .el-table__cell{
   padding:0px 0px!important;
 }
/deep/.el-table .cell{
  line-height: 40px!important;
}

</style>
