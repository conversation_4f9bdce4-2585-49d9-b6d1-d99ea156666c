<template>
  <div class="solicitude-task">
    <Banner title="调研话术库管理" desc="调研话术库管理" />
    <div class="layout" style="margin-top:20px;">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
              <el-form-item label="选择时间段" class="timeRange" prop="timeRange">
                <el-date-picker
                  v-model="formInline.timeRange"
                  popper-class="xps"
                  style="width:250px"
                  :size="conditionsize"
                  type="daterange"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />

              </el-form-item>

              <el-form-item label="短信内容" prop="content">
                <el-input
                  v-model="formInline.content"
                  :size="conditionsize"
                  placeholder="请输入短信内容"
                >
                  <!-- <el-button slot="append" @click="query">搜索</el-button> -->
                </el-input>
              </el-form-item>

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
              </el-form-item>

            </el-form>

            <div class="btnbox">
              <el-button :size="conditionsize" type="default" @click="dialogFormVisible=true">新建短信模版</el-button>
            </div>
          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              :data="tableDatas"
              style="width: 100%"
              max-height="560"
            >

              <el-table-column
                v-for="item in columnList"
                :key="item.name"
                :prop="item.key"
                :label="item.name"
                align="center"
                :show-overflow-tooltip="true"
                min-width="100"
              >
                <template slot-scope="scope">

                  <span v-if="item.key=='type'">
                    {{ scope.row[item.key]==1?'修复后评价':scope.row[item.key]==0?'主动关怀':'' }}
                  </span>

                  <span v-else>{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="100"
              >
                <template slot-scope="scope">
                  <div class="optionbtnbox">
                    <div>
                      <span class="coloryellow" @click="openCheckDetail(scope.row)">编辑</span>
                    </div>
                    <div>
                      <el-popconfirm
                        confirm-button-text="删除"
                        cancel-button-text="取消"
                        icon="el-icon-info"
                        icon-color="red"
                        title="确定删除本条数据？"
                        @confirm="dele(scope.row)"
                      >
                        <span slot="reference" class="coloryellow">删除</span>
                      </el-popconfirm>

                    </div>
                  </div>
                </template>
              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>

    <el-dialog :title="checkRow.id?'修改短信模版':'新建短信模版'" class="msgdialog" width="55%" :visible.sync="dialogFormVisible">
      <msgAdd v-if="dialogFormVisible" confirmtxt="确认" :is-add-msg-tep="true" :check-row="checkRow" @cancel="cancelDialog" />
    </el-dialog>

  </div>
</template>
<script>
import Banner from './../../components/common/Banner.vue'

import msgAdd from './../../components/common/msgAdd.vue'

import { csmmessagetemplate, csmmessagetemplateDel } from '@/api/customer/index'

import tool from '@/views/bj_proatal_web/utils/utils'

export default {
  components: {
    Banner,

    msgAdd

  },
  data() {
    return {
      // 文件导入新建客群

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',
      formInline: {
        timeRange: [],
        content: ''
      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 10,
        total: 0
      },

      tableDatas: [],

      //       content: "1"
      // createTime: "2022-05-11T16:47:29"
      // createUserId: "1"
      // createUserName: "1"
      // deleteStatus: 0
      // id: 10000042
      // title: "1"
      // type: "1"
      columnList: [
        {
          key: 'title',
          name: '模板名称'
        },
        {
          key: 'content',
          name: '模板内容'
        },
        {
          key: 'type',
          name: '模板类型'
        },
        {
          key: 'createUserName',
          name: '创建人'
        },
        {
          key: 'createTime',
          name: '创建时间'
        }

      ],

      dialogFormVisible: false,
      dialogFormVisible2: false

    }
  },

  watch: {
    dialogFormVisible: {
      deep: true,
      handler: function(v, oldv) {
        if (v == false) {
          this.checkRow = {}
        }
      }
    }
  },

  mounted() {
    // console.log('this.$store:', this.$store)
    // const code = this.$route.params.code
    // this.customerCode = code

    this.init()
  },
  methods: {
    init() {
      this.queryList()
    },
    cancelDialog() {
      this.dialogFormVisible = false
      this.queryList()
      this.checkRow = {}
    },

    // 查看短信发送记录
    openCheckDetail(row) {
      this.checkRow = row
      this.dialogFormVisible = true
    },
    // 删除
    dele(row) {
      console.log(row)
      csmmessagetemplateDel(row.id).then(res => {
        console.log(row)
        const { code, data } = res
        if (code == 200) {
          this.$message.success('删除模版成功！')
          this.queryList()
        }
      })
    },
    query() {
      this.page.current = 1
      this.queryList()
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, formInline } = this
      const timeRange = formInline.timeRange
      const params = Object.assign({}, { pagination: { currentPage: page.current, pageSize: page.size }}, { content: formInline.content })
      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.beginTime = tool.formatterDate(timeRange[0], 'yyyy-MM-dd')
        params.endTime = tool.formatterDate(timeRange[1], 'yyyy-MM-dd')
      }

      // if (Array.isArray(params.gridId) && params.gridId.length) {
      //   params.gridId = params.gridId[params.gridId.length - 1]
      // }

      // delete params.timeRange

      console.log('params:', params)
      csmmessagetemplate(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, current } = data

          // 处理不满意指标

          this.tableDatas = records || []
          this.page.total = total
          this.page.current = current
        }
      })
    },

    preback() {
      sessionStorage.setItem('menuUrl', `/customergroup`)
      this.$router.push({
        name: 'customergroup'
      })
    },

    resetForm(formName) {
      this.$refs[formName].resetFields()

      // this.csmcustomerlableSeleted = []
      // this.formInline.timeRange = []
      // this.formInline.content = ''
      // this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    }

  }

}
</script>

<style >
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>

<style lang='scss' scoped>

.msgdialog :deep(.el-form-item__label) {
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog :deep(.el-checkbox__label) {
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
:deep(.el-dialog__header) {
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn) {
  top:12px;
  line-height: 20px;

}
:deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus) {
  border-color: #ff9900;
}
.solicitude-task{
        background-color: rgba(242, 242, 242, 1);
        min-height: 100vh;
        :deep(.el-input-group__append), :deep(.el-input-group__prepend) {
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            padding: 0 50px;

            :deep(.el-tabs__nav-wrap::after) {
              height:1px;
              background:#E6E6E6;
            }
            :deep(.el-tabs__active-bar) {
              background:#FF9900;
            }
            :deep(.el-tabs__item.is-active) {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            :deep(.el-tabs__item) {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            :deep(.el-form-item.el-form-item--medium) {margin-right:20px}
            :deep(.el-form-item__content .el-input-group) {
              vertical-align: middle;
            }
            :deep(.el-pagination) {text-align: right;}
            .speformitem :deep(.el-form-item__label) {
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        :deep(.el-icon-arrow-down:before ) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down) {
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down) {
         transform: rotate(0deg);
       }

  }

    }

 :deep(.el-table--medium .el-table__cell) {
   padding:8px 0;
 }
 :deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell) {
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}

</style>
