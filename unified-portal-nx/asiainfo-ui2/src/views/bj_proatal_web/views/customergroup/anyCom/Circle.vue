<template>
  <div class="circle" :id="t">
    <!-- <v-chart ref="circleCharts" class="echarts" :options="circleOptions" autoresize/> -->
  </div>
</template>

<script>
// import Vue from 'vue';
// import ECharts from 'vue-echarts';
// // 手动引入 ECharts 各模块来减小打包体积
// import 'echarts/lib/chart/pie';
// import 'echarts/lib/component/legend';
// import 'echarts/lib/component/tooltip';
// import 'echarts/lib/component/title';
// import 'echarts/lib/component/markLine';

// Vue.component('v-chart', ECharts);

export default {
  props: {
    data: {
      type: Array,
      default: () => [
        {name: '一月', count: 100},
        {name: '二月', count: 250},
        {name: '三月', count: 300},
        {name: '四月', count: 60},
        {name: '五月', count: 120},
        {name: '六月', count: 190},
        {name: '七月', count: 520},
        {name: '八月', count: 200},
        {name: '九月', count: 150},
        {name: '十月', count: 360},
        {name: '十一月', count: 460},
        {name: '十二月', count: 100},
      ],
    },
  },
  data() {
    return {
      t:'',
      chart:null,
      circleOptions: {
        tooltip: {
          formatter: (params) => {
            console.log('params=>',params)
            return `${params.name}:${params.value}<br/>${params.data.percent}%`
          },
        },
        legend: {},
        color: ['#5E7DFF', '#41B1FF', '#66E566', '#FFCD3C'],
        series: [
          {
            type: 'pie',
            clockwise: true,
            center: ['50%', '50%'],
            radius: ['30%', '45%'],
            yAxisIndex: 1,
            data: [],
            label: {
              normal: {
                // formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
                // formatter: '{d}%',
                formatter: (params) => {
            console.log('params111111=>',params)
            return `${params.data.percent}%`
          }
              },
            },
          },
        ],
      },
    };
  },
  created(){
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    console.log('v=>',this.data)
    this.setOptions();
  },
  methods: {
    setOptions() {
      this.circleOptions.series[0].data = this.data.map((d, index) => ({
        value: d.count,
        name: `${d.name}`,
        percent:`${d.percent}`,
        itemStyle: {
          normal: {
            color: this.circleOptions.color[index],
          },
        },
      }));


      this.chart = null
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.chart&&this.chart.setOption(this.circleOptions)
     
    },
  },
  watch: {
    data(v) {
      console.log('1122122')
      console.log('v=>',v)
      this.setOptions();
    },
  },
};
</script>

<style lang="less" scoped>
  .circle{
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    .echarts{
      width: 100%;
      height: 100%;
      
      // background:red;
    }
  }
</style>
