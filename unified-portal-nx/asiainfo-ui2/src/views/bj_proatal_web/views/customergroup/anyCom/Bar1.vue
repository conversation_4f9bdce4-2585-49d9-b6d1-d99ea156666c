<template>
  <!-- <div class="bar">
    <v-chart ref="barCharts" class="echarts" :options="barOptions" autoresize/>
  </div> -->
  <div class="bar" :id="t">
   
  </div>
</template>

<script>
// import Vue from 'vue';
// import ECharts from 'vue-echarts';
// // 手动引入 ECharts 各模块来减小打包体积
// import 'echarts/lib/chart/bar';
// import 'echarts/lib/component/legend';
// import 'echarts/lib/component/tooltip';
// import 'echarts/lib/component/title';

// Vue.component('v-chart', ECharts);

export default {
  props: {
    data: {
      type: Array,
      default: () => [
        {name: '一月', count: 100},
        {name: '二月', count: 250},
        {name: '三月', count: 300},
        {name: '四月', count: 60},
        {name: '五月', count: 120},
        {name: '六月', count: 190},
        {name: '七月', count: 520},
        {name: '八月', count: 200},
        {name: '九月', count: 150},
        {name: '十月', count: 360},
        {name: '十一月', count: 460},
        {name: '十二月', count: 100},
      ],
    },
  },
  data() {
    return {
      t:'',
      chart:null,
      barOptions: {
        color: ['#41B1FF', '#66E566', '#FFCD3C', '#5E7DFF'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: '{b}: {c}%',
        },
        grid:{
          bottom:'35%',
          top:'5%',
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            splitLine: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: '#E1E4E8',
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#383D44',
              rotate:60,
              formatter(params) {
                let newParamsName = '';// 最终拼接成的字符串
                const paramsNameNumber = params.length;// 实际标签的个数
                const provideNumber = 6;// 每行能显示的字的个数
                const rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                /**
                             * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
                             */
                // 条件等同于rowNumber>1
                if (paramsNameNumber > provideNumber) {
                  /** 循环每一行,p表示行 */
                  for (let p = 0; p < rowNumber; p++) {
                    let tempStr = '';// 表示每一次截取的字符串
                    const start = p * provideNumber;// 开始截取的位置
                    const end = start + provideNumber;// 结束截取的位置
                    // 此处特殊处理最后一行的索引值
                    if (p == rowNumber - 1) {
                      // 最后一次不换行
                      tempStr = params.substring(start, paramsNameNumber);
                    } else {
                      // 每一次拼接字符串并换行
                      tempStr = `${params.substring(start, end)}\n`;
                    }
                    newParamsName += tempStr;// 最终拼成的字符串
                  }

                } else {
                  // 将旧标签的值赋给新标签
                  newParamsName = params;
                }
                // 将最终的字符串返回
                return newParamsName;
              }
            },
          },
        ],
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter(val) {
              return `${val}%`;
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#D5DAE0',
            },
          },
        },
        series: [
          {
            type: 'bar',
            barWidth: '20%',
            data: [],
          },
        ],
      },
    };
  },
created(){
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    this.setOptions();
  },
  methods: {
    setOptions() {
      const tempData = [];
      const tempxData = [];
      this.data.forEach((d, index) => {
        tempData.push(d.percent);
        tempxData.push(d.name);
        // tempxData.push("10086短信是舍呢么");
      });
      this.barOptions.series[0].data = tempData;

      this.barOptions.xAxis[0].data = tempxData;


      this.chart = null
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.chart&&this.chart.setOption(this.barOptions)


    },
  },
  watch: {
    data() {
      this.setOptions();
    },
  },
};
</script>

<style lang="less" scoped>
.bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
