<template>
  <div class="searchComponents">
    <el-input
      class="searchInput"
      v-model="searchValue"
      placeholder="请输入内容"
      prefix-icon="el-icon-search"
      value-key="name"
      size="small"
      clearable
    ></el-input>
    <div class="contentArea">
      <el-table
        class="table"
        :data="tableData"
        border
        v-loading="loading"
        @row-click="handleSelect"
        :header-cell-style="{backgroundColor:'#F4FAFF'}"
      >
        <el-table-column prop="name" label="调研主题"></el-table-column>
        <!-- <el-table-column prop="canDown" label="分公司下载"></el-table-column> -->
        <!-- <el-table-column prop="type" label="执行方式"></el-table-column> -->
        <el-table-column prop="time1" label="执行实际开始时间"></el-table-column>
        <el-table-column prop="time2" label="执行实际结束时间"></el-table-column>
        <el-table-column prop="count1" label="目标客户"></el-table-column>
        <el-table-column prop="count2" label="到达人数"></el-table-column>
        <el-table-column prop="count3" label="参与客户"></el-table-column>
        <el-table-column prop="percent" label="参与率"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  Input, Icon, Table, TableColumn,
} from 'element-ui';

Vue.use(Input).use(Icon).use(Table).use(TableColumn);
export default {
  data() {
    return {
      searchValue: '',
      activeId: '',
      loading: true,
      data: [],
      timeout: null,
    };
  },
  computed: {
    tableData() {
      return this.data.filter((it) => it.name.indexOf(this.searchValue) !== -1);
    },
  },
  created() {
    this.querySearchAsync(true);
  },
  methods: {
    querySearchAsync(isdefault) {
      this.loading = true;
      this.$http.post('getQuestionnairesByUserID', {keywords: this.searchValue}).then((res) => {
        this.data = res.data;
        this.loading = false;
        if (isdefault) {
          this.$emit('initData', {id: res.data[0].id, name: res.data[0].name});
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    handleSelect(it) {
      this.$emit('select', it);
    },
  },
  watch: {
    searchValue(val) {
      // 节流
      // if (this.timeout) {
      //   clearTimeout(this.timeout);
      // }
      // this.timeout = setTimeout(() => {
      //   this.querySearchAsync();
      // }, 1000);
    },
  },
};
</script>

<style lang="less" scoped>
.searchComponents {
  width: 100%;
  .searchInput {
    position: relative;
    margin-bottom: 10px;
  }
  .contentArea {
    max-height: 70vh;
    overflow: auto;
    position: relative !important;
    .table{
      font-size: 12px;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
.searchInput {
  .el-input__inner {
    width: 30%;
    min-width: 200px;
    background-color: #f3f6f9 !important;
    border: none;
    border-radius: 16px;
  }
  .el-input__suffix{
    // left: 0;
    width: 50px;
    left: calc(30% - 50px);
  }
}
</style>
