<template>
  <div class="downloadComponent">
    <el-dialog :visible.sync="showModal" @close="closeWin">
      <img class="previewImg" :src="base64" alt>
      <div slot="footer" class="dialog-footer">
        <a
          target="_blank"
          class="el-button el-button--primary is-round"
          :href="gethref(fileName)"
          @click="closeWin"
        >保存图片</a>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Vue from 'vue';
import {Dialog} from 'element-ui';

Vue.use(Dialog);
export default {
  name: 'Download',
  props: ['base64', 'show', 'fileName'],
  data() {
    return {
      showModal: this.show,
    };
  },
  created() {

  },
  methods: {
    closeWin() {
      this.showModal = false;
    },
    gethref(fileName) {
      if (process.env.NODE_ENV === 'development') {
        return `/proxy/investigation/downLoadByName?jpgName=${fileName}`;
      }
      return `/bjcem/investigation-service/investigation/downLoadByName?jpgName=${fileName}`;
    },
  },
  watch: {
    showModal() {
      this.$emit('update:show', false);
    },
    show(val) {
      if (val) {
        this.showModal = true;
      }
    },
  },
};
</script>
<style lang="less">
.downloadComponent {
  .previewImg {
    width: 100%;
  }
  a {
    text-decoration: none;
    float: none;
  }
  .el-dialog__body {
    max-height: 60vh;
    overflow: auto;
  }
}
</style>
