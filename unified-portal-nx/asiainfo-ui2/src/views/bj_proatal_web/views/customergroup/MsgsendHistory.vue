<template>
  <div class="solicitude-task">
    <Banner title="短信发送查看" desc="短信发送查看" btntxt="客群管理" @preback="preback" />
    <div class="layout" style="margin-top:20px;">
      <div class="grid" style="background:#fff;padding:10px;">
        <div class="flexbox">
          <div class="formbox" style="position:relative">
            <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">

              <el-form-item label="选择时间段" class="timeRange">
                <el-date-picker
                  v-model="formInline.timeRange"
                  style="width:250px"
                  :size="conditionsize"
                  type="daterange"
                  popper-class="xps"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />

              </el-form-item>

              <el-form-item label="客群名称" prop="content">
                <el-input
                  v-model="formInline.content"
                  :size="conditionsize"
                  placeholder="请输客群名称进行搜索"
                >
                  <!-- <el-button slot="append" @click="query">搜索</el-button> -->
                </el-input>
              </el-form-item>

              <el-form-item>
                <el-button :size="conditionsize" type="primary" @click="query">查询</el-button>
                <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
              </el-form-item>

            </el-form>

            <div class="btnbox">
              <el-button :size="conditionsize" type="default" @click="dialogFormVisible=true">新建短信</el-button>
            </div>
          </div>
        </div>
        <div class="conbox" style="background:#fff;">
          <div>
            <el-table
              v-loading="tableLoading"
              :data="tableDatas"
              style="width: 100%"
              max-height="560"
            >

              <el-table-column
                v-for="item in columnList"
                :key="item.name"
                :prop="item.key"
                :label="item.name"
                align="center"
                :show-overflow-tooltip="true"
                min-width="100"
              >
                <template slot-scope="scope">

                  <span v-if="item.key=='careStatus'">
                    {{ scope.row[item.key]==1?'待关怀':scope.row[item.key]==0?'已关怀':'' }}
                  </span>

                  <span v-else>{{ scope.row[item.key] }}</span>
                </template>

              </el-table-column>

            </el-table>

          </div>

        </div>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableDatas"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </div>
    </div>

    <el-dialog title="评价短信群发" class="msgdialog" :visible.sync="dialogFormVisible">
      <msgAdd v-if="dialogFormVisible" @cancel="dialogFormVisible=false" />
    </el-dialog>

  </div>
</template>
<script>
import Banner from './../../components/common/customerBanner.vue'

import msgAdd from './../../components/common/msgAdd.vue'

import { csmsendmessagehistory } from '@/api/customer/index'
import tool from '@/views/bj_proatal_web/utils/utils'

export default {
  components: {
    Banner,
    msgAdd
  },
  data() {
    return {
      // 文件导入新建客群

      //
      tableLoading: false,
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],
      customerCode: '',
      formInline: {
        timeRange: [],
        careStatus: '',
        gridId: '',
        content: ''
      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 10,
        total: 0
      },

      tableDatas: [],
      columnList: [
        {
          key: 'content',
          name: '短信内容'
        },
        {
          key: 'customerName',
          name: '客户群名称'
        },
        {
          key: 'sendNum',
          name: '发送人数'
        },
        {
          key: 'createTime',
          name: '发送时间'
        }

      ],

      dialogFormVisible: false,
      dialogFormVisible2: false

    }
  },

  mounted() {
    console.log('this.$store:', this.$store)
    const code = this.$route.params.code
    this.customerCode = code
    this.checkRow = {

    }

    this.init()
  },
  methods: {
    init() {
      this.queryList()
    },

    query() {
      this.page.current = 1
      this.queryList()
    },

    // 查询表格数据
    queryList() {
      // ID10000041
      const { page, customerCode, formInline } = this
      const timeRange = formInline.timeRange
      const params = Object.assign({}, { pagination: { currentPage: page.current, pageSize: page.size }}, { customerCode })
      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.beginTime = tool.formatterDate(timeRange[0], 'yyyy-MM-dd')
        params.endTime = tool.formatterDate(timeRange[1], 'yyyy-MM-dd')
      }

      // if (Array.isArray(params.gridId) && params.gridId.length) {
      //   params.gridId = params.gridId[params.gridId.length - 1]
      // }

      // delete params.timeRange
      // params.customerCode = 'ID10000041'

      console.log('params:', params)
      this.tableLoading = true
      csmsendmessagehistory(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, current } = data

          // 处理不满意指标

          this.tableDatas = records || []
          this.page.total = total
          this.page.current = current
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    preback() {
      sessionStorage.setItem('menuUrl', `/customergroup`)
      this.$router.push({
        name: 'customergroup'
      })
    },

    resetForm(formName) {
      this.csmcustomerlableSeleted = []
      this.formInline.timeRange = []
      this.$refs[formName].resetFields()
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    }

  }

}
</script>
<style >
 .xps .el-date-table td.today span{
  background-color: #FF9900!important;
}

</style>
<style lang='scss' scoped>

.msgdialog :deep(.el-form-item__label) {
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog :deep(.el-checkbox__label) {
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
:deep(.el-dialog__header) {
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn) {
  top:12px;
  line-height: 20px;

}
:deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus) {
  border-color: #ff9900;
}
.solicitude-task{
        background-color: rgba(242, 242, 242, 1);
        min-height: 100vh;
        :deep(.el-input-group__append), :deep(.el-input-group__prepend) {
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }
        .layout{
            padding: 0 50px;

            :deep(.el-tabs__nav-wrap::after) {
              height:1px;
              background:#E6E6E6;
            }
            :deep(.el-tabs__active-bar) {
              background:#FF9900;
            }
            :deep(.el-tabs__item.is-active) {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #262626;
            }
            :deep(.el-tabs__item) {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              color: #595959;

            }
            :deep(.el-form-item.el-form-item--medium) {margin-right:20px}
            :deep(.el-form-item__content .el-input-group) {
              vertical-align: middle;
            }
            :deep(.el-pagination) {text-align: right;}
            .speformitem :deep(.el-form-item__label) {
              width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          bottom: 22px;
        }
      }

      .el-cascader {
        :deep(.el-icon-arrow-down:before ) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down) {
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down) {
         transform: rotate(0deg);
       }

  }

    }

 :deep(.el-table--medium .el-table__cell) {
   padding:8px 0;
 }
 :deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell) {
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}

</style>
