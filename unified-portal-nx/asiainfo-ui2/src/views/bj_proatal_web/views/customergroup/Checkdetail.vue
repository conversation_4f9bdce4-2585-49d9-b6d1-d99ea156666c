<template>
  <div>
    <div>
      <span class="infoitem">
        <span class="lab">用户ID:</span>
        <span class="vl">{{ checkRow.userId }}</span>
      </span>
      <span class="infoitem">
        <span class="lab">手机号码:</span>
        <span class="vl">{{ checkRow.phoneNo }}</span>
      </span>
    </div>
    <div>
      <el-table
        :data="tableDatas"
        style="width: 100%"
        max-height="560"
      >

        <el-table-column
          v-for="item in columnList"
          :key="item.name"
          :prop="item.key"
          :label="item.name"
          align="center"
          :show-overflow-tooltip="true"
          min-width="100"
        >

          <template slot-scope="scope">
            <span v-if="item.key=='type'">
              {{ scope.row[item.key]==1?'显性':scope.row[item.key]==2?'潜在':'' }}
            </span>
            <span v-else-if="item.key=='isComplaint'">
              {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
            </span>

            <span v-else-if="item.key=='isSend'">
              {{ scope.row[item.key]=='Y'?'是':scope.row[item.key]=='N'?'否':'' }}
            </span>

            <span v-else-if="item.key=='repairStatus'">
              {{ scope.row[item.key]==3?'待修复':scope.row[item.key]==2?'已修复':scope.row[item.key]==1?'已接触未修复':scope.row[item.key]==0?'下发未接触成功':'' }}
            </span>

            <span v-else>{{ scope.row[item.key] }}</span>
          </template>

        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="200"
        >
          <template slot-scope="scope">
            <div class="optionbtnbox">
              <div>
                <span class="coloryellow">查看</span>
              </div>
              <!-- <div>
                <span>发送短信</span>
              </div> -->
              <div>
                <el-popconfirm
                  confirm-button-text="删除"
                  cancel-button-text="取消"
                  icon="el-icon-info"
                  icon-color="red"
                  title="确定删除本条数据？"
                  @confirm="dele(scope.row)"
                >
                  <span slot="reference" class="coloryellow">删除</span>
                </el-popconfirm>

              </div>
            </div>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div style="padding:10px 0;background:#fff;">
      <el-pagination
        v-if="tableDatas"

        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { getByPhoneNo } from '@/api/customer/index'
export default {
  props: {
    checkRow: {
      type: Object,
      required: true
    },
    csmcustomerlableKeys: {
      type: Array,
      required: true
    },
    csmcustomerlableOpt: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      tableDatas: [],
      columnList: [
        {
          key: 'batch',
          name: '数据批次'
        },
        {
          key: 'userId',
          name: '用户ID'
        },
        {
          key: 'csmcustomerlableVisibleTxt',
          name: '不满指标'
        },
        {
          key: 'phoneNo',
          name: '手机号码'
        },

        {
          key: 'type',
          name: '类型'
        },
        {
          key: 'satisfaction',
          name: '得分'
        },
        {
          key: 'isComplaint',
          name: '是否投诉'
        },
        {
          key: 'isSend',
          name: '是否下发短信'
        },
        {
          key: 'lastContactTime',
          name: '最近接触时间'
        },

        {
          key: 'repairStatus',
          name: '修复状态'
        }

      ],
      page: {
        current: 1,
        size: 10,
        total: 0
      }

    }
  },
  watch: {
    checkRow: {
      deep: true,
      handler: function(v, oldv) {
        this.getByPhoneNo()
      }
    }
  },
  mounted() {
    this.getByPhoneNo()
  },
  methods: {
    dele(row) {

    },
    getByPhoneNo() {
      const { page } = this
      const p = {
        current: page.current,
        size: page.size,
        phoneNo: this.checkRow.phoneNo
      }

      getByPhoneNo(p).then(res => {
        const { code, data } = res

        // const { total, records } = data
        if (code == 200) {
          // 处理不满意指标
          const { csmcustomerlableKeys, csmcustomerlableOpt } = this
          data.forEach(i => {
            i.csmcustomerlableVisibleTxt = ''// 用于显示的不满意指标
            for (const ke in i) {
              if (csmcustomerlableKeys.indexOf(ke) != -1 && i[ke]) {
                const tempTar = csmcustomerlableOpt.filter(i => i.code == ke)
                console.log('tempTar:', tempTar)
                if (!i.csmcustomerlableVisibleTxt) {
                  i.csmcustomerlableVisibleTxt = `${tempTar[0].name}`
                } else {
                  i.csmcustomerlableVisibleTxt += `、${tempTar[0].name}`
                }
              }
            }
            //  this.page.total = total || 0
            this.tableDatas = data
          })
        }
      })
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      // 下方添加查询逻辑
      this.$nextTick(() => {
        this.getByPhoneNo()
      })
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val

      // 下方添加查询逻辑
      this.$nextTick(() => {
        this.getByPhoneNo()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.infoitem{
    margin-bottom: 10px;
    display:inline-block;
    margin-right:50px;
    .lab{
        font-weight: bold;
        font-size: 16px;
    }
    .vl{

    }
}
  :deep(.el-pagination) {text-align: right;}

</style>
