<template>
  <div class="solicitude-task">
    <Banner title="客户群管理" desc="客户群管理" />
    <div class="pbox" style="position:relative;z-index:1">
      <el-radio-group v-model="layoutType" style="padding-left:30px;position:absolute;top:-46px;left:0;">
         <el-radio-button label="layoutType3">客群管理</el-radio-button>
        <el-radio-button label="layoutType1">集团下发客户</el-radio-button>
        <el-radio-button label="layoutType2">省内重保客户</el-radio-button>
      </el-radio-group>
      <!-- 集团下发客户 -->
      <div v-if="layoutType=='layoutType1'">
        <div class="layout sp">
          <el-tabs v-model="customertype" style="margin-top:10px;" @tab-click="handleClickTabcustomerType">
            <el-tab-pane label="关怀客群" name="0" />
            <el-tab-pane label="修复客群" name="1" />
          </el-tabs>
        </div>

        <div class="layout nobottomline">
          <div style="background:#fff;border-bottom:1px solid #e6e6e6;padding-left:10px;padding-top:15px">
            <el-tabs v-model="activeName" @tab-click="handleClickTab">
              <el-tab-pane label="全部" name="2" style="margin-right:20px;" />
              <el-tab-pane v-if="customertype==0" label="待关怀" name="1" />
              <el-tab-pane v-if="customertype==0" label="已关怀" name="0" />
              <el-tab-pane v-if="customertype==1" label="待修复" name="1" />
              <el-tab-pane v-if="customertype==1" label="已修复" name="0" />
            </el-tabs>
          </div>
          <div class="grid" style="background:#fff;padding:10px;padding-top:20px">
            <div class="flexbox">
              <div class="formbox" style="position:relative;">
                <el-form ref="queryForm" :inline="true" :model="formInline" class="demo-form-inline" label-position="right">
                  <!-- <el-form-item label="指数指标名称">
                <el-select v-model="csmcustomerlableSeleted" multiple :size="conditionsize" placeholder="请选择" collapse-tags>
                  <el-option v-for="(i,idx) in csmcustomerlableOpt" :key="`${idx}1`" :label="i.label" :value="i.value" />
                </el-select>
              </el-form-item> -->
                  <!-- <el-form-item label="是否投诉" prop="isComplaint">
                <el-select v-model="formInline.isComplaint" placeholder="请选择" :size="conditionsize">
                  <el-option label="是" value="Y" />
                  <el-option label="否" value="N" />
                </el-select>
              </el-form-item> -->

                  <el-form-item label="网格" prop="gridId">
                    <el-cascader
                      v-model="formInline.gridId"
                      :options="optionsCascader"
                      :size="conditionsize"
                      :show-all-levels="false"
                      clearable
                      :props="{
                        checkStrictly:true
                      }"

                      @change="handleChangeCascader"
                    />
                  </el-form-item>

                  <!-- <el-form-item label="类型">
                <el-select v-model="formInline.type" placeholder="请选择" :size="conditionsize" style="width:100px;">
                  <el-option label="显性" value="1" />
                  <el-option label="潜在" value="0" />
                </el-select>
              </el-form-item> -->

                  <el-form-item :label="customertype=='0'?'关怀状态':'修复状态'" prop="careStatus" class="speformitem">
                    <el-select v-model="formInline.careStatus" placeholder="请选择" style="width:130px" :size="conditionsize">
                      <el-option v-show="activeName==='2'||activeName==='0'" :label="customertype=='0'?'已关怀':'已修复'" value="0" />
                      <el-option v-show="activeName==='2'||activeName==='1'" :label="customertype=='1'?'待修复':'待关怀'" value="1" />
                    </el-select>
                  </el-form-item>

                  <!-- <el-form-item label="得分" prop="satisfaction">
                <el-select v-model="formInline.satisfaction" placeholder="请选择" :size="conditionsize">
                  <el-option v-for="(j,idx) in satisfactionOpts" :key="`${idx}j`" :label="j.label" :value="j.value" />

                </el-select>
              </el-form-item> -->

                  <el-form-item label="创建时间" class="timeRange">
                    <el-date-picker
                      v-model="formInline.timeRange"
                      style="width:230px"
                      :size="conditionsize"
                      type="daterange"
                      popper-class="xps"
                      range-separator="~"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    />

                  </el-form-item>

                  <el-form-item label="客群名称" prop="name">
                    <el-input
                      v-model="formInline.name"
                      :size="conditionsize"
                      placeholder="请输客群名称进行搜索"
                    >
                      <!-- <el-button slot="append" @click="query">搜索</el-button> -->
                    </el-input>
                  </el-form-item>

                  <el-form-item>
                    <el-button :size="conditionsize" type="primary" @click="queryList">查询</el-button>
                    <el-button :size="conditionsize" type="default" @click="resetForm('queryForm')">重置</el-button>
                  </el-form-item>

                </el-form>

                <div class="btnbox">
                  <el-button v-show="customertype=='0'" :size="conditionsize" type="default" @click="uploadDialog2=true">根据基站导入客户</el-button>
                  <el-button v-show="customertype=='1'" :size="conditionsize" type="default" @click="drawer=true">生成客群</el-button>
                  <el-button v-show="customertype!='1'" :size="conditionsize" type="default" @click="uploadDialog=true">导入客户</el-button>
                </div>

              </div>
            </div>
            <div class="conbox" style="background:#fff;margin-top:30px;">

              <div>
                <el-table
                  v-loading="tableLoading"
                  :data="tableDatas"
                  style="width: 100%;"
                  max-height="560"
                >

                  <el-table-column
                    v-for="item in columnList"
                    :key="item.name"
                    :prop="item.key"
                    :label="item.name"
                    align="center"
                    :show-overflow-tooltip="true"
                    min-width="100"
                  >
                    <template slot-scope="scope">

                      <span v-if="item.key=='careStatus'">
                        <span v-if="customertype==0">
                          {{ scope.row[item.key]==1?'待关怀':scope.row[item.key]==0?'已关怀':'' }}
                        </span>
                        <span v-else>
                          {{ scope.row[item.key]==1?'待修复':scope.row[item.key]==0?'已修复':'' }}
                        </span>

                      </span>
                      <span v-else-if="item.key=='isSend'">
                        {{ scope.row[item.key]=='0'?'已发送':scope.row[item.key]=='1'?'未发送':'' }}
                      </span>
                      <span v-else-if="item.key=='isPush'">
                        <!-- 1-未推送 2-推送中 3-已推送 -->
                        {{ scope.row[item.key]=='1'?'未推送':scope.row[item.key]=='2'?'推送中':scope.row[item.key]=='3'?'已推送':'' }}
                      </span>

                      <span v-else>{{ scope.row[item.key] }}</span>
                    </template>

                  </el-table-column>

                  <el-table-column
                    label="操作"
                    align="center"
                    :width="customertype==0?'260px':'310px'"
                  >
                    <template slot-scope="scope">
                      <div v-show="customertype==0" class="optionbtnbox">
                        <div>
                          <!-- <span class="coloryellow" @click="openCheckDetail(scope.row)">查看</span> -->
                          <span v-show="scope.row.number" class="coloryellow" @click="downDetail(scope.row)">下载</span>
                        </div>
                        <div >
                          <span v-if="scope.row.isSend==1" class="coloryellow" @click="sendMsg(scope.row)">发起关怀</span>
                          <span v-if="scope.row.isSend==0" style="color:#8C8C8C;cursor:not-allowed" class="coloryellow">发起关怀</span>
                        </div>
                        <div>
                          <el-popconfirm
                            confirm-button-text="删除"
                            cancel-button-text="取消"
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定删除本条数据？"
                            @confirm="dele(scope.row)"
                          >
                            <span slot="reference" class="coloryellow">删除</span>
                          </el-popconfirm>

                        </div>
                        <div>

                          <span v-show="showExportBtn" class="coloryellow" @click="down(scope.row)">导出</span>
                        </div>
                      </div>

                      <div v-show="customertype==1" class="optionbtnbox x">
                        <div>
                          <span class="coloryellow" @click="openCheckDetailEcharts(scope.row)">查看结果</span>
                        </div>
                        <!-- <div>
                      <el-popover
                        v-model="scope.row.popvervis"
                        placement="top"
                        title=""
                        width="200"
                        trigger="manual"
                      >

                        <div class="syst">选择推送的系统：</div>
                        <div class="sysbox">
                          <div class="sysboxitem"><span :class="{active:tuisongRadio=='EMOS'}" @click="tuisongRadio='EMOS'">EMOS</span></div>
                          <div class="sysboxitem"><span :class="{active:tuisongRadio=='IOP'}" @click="tuisongRadio='IOP'">IOP</span></div>
                          <div class="sysboxitem"><span :class="{active:tuisongRadio=='WGTONG'}" @click="tuisongRadio='WGTONG'">网格通</span></div>
                        </div>

                        <div style="text-align: right; margin: 0">
                          <el-button type="primary" size="mini" @click="putSys(scope.row)">确定</el-button>
                          <el-button type="default" size="mini" @click="scope.row.popvervis=false">取消</el-button>

                        </div>

                        <span slot="reference" class="coloryellow" @click="openPopver(scope.row)">推送</span>

                      </el-popover>

                    </div> -->
                        <div>
                          <!-- <span class="coloryellow" @click="gopushCode(scope.row)">推送</span> -->
                          <el-popconfirm
                            confirm-button-text="确定"
                            cancel-button-text="取消"
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定推送"
                            @confirm="gopushCode(scope.row)"
                          >
                            <!-- 根据isPush来推送 -->
                            <!-- 1-未推送 2-推送中 3-已推送 -->
                            <span v-if="scope.row.isPush==1" slot="reference" class="coloryellow">推送</span>
                            <span v-else slot="reference" class="coloryellow" style="color:#8C8C8C;cursor:not-allowed">推送</span>

                          </el-popconfirm>

                        </div>
                        <div>
                          <span v-if="scope.row.isSend==1" class="coloryellow" @click="sendMsg(scope.row)">短信回访</span>
                          <span v-if="scope.row.isSend==0" style="color:#8C8C8C;cursor:not-allowed" class="coloryellow">短信回访</span>
                        </div>

                        <div>
                          <el-popconfirm
                            confirm-button-text="删除"
                            cancel-button-text="取消"
                            icon="el-icon-info"
                            icon-color="red"
                            title="确定删除本条数据？"
                            @confirm="dele(scope.row)"
                          >
                            <span slot="reference" class="coloryellow">删除</span>
                          </el-popconfirm>

                        </div>
                        <div>
                          <span v-show="showExportBtn" class="coloryellow" @click="down(scope.row)">导出</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>

                </el-table>

              </div>

            </div>
            <!-- 分页功能 -->
            <div style="padding:10px 0;background:#fff;">
              <el-pagination
                v-if="tableDatas"
                :current-page="page.current"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
                @size-change="sizeChange"
                @current-change="pageCurrentChange"
              />
            </div>

          </div>
        </div>
      </div>
      <!-- 省内重保客户 -->
      <div v-if="layoutType=='layoutType2'">
        <div class="layout sp">
          <el-tabs v-model="customertypeSN" style="margin-top:10px;">
            <el-tab-pane label="关怀客群" name="1" />
            <el-tab-pane label="修复客群" name="2" />
          </el-tabs>
        </div>
        <div class="layout nobottomline">
          <div class="grid" style="background:#fff;padding:10px">
            <Imp1 :imp-active-name="customertypeSN" />
          </div>
        </div>

      </div>
       <div v-if="layoutType=='layoutType3'" class="layout" style="margin-top:10px;">
        <customergroupCommonTable />
      </div>
    </div>
    <el-drawer

      :visible.sync="drawer"
      size="50%"
    >
      <div slot="title">
        <span style="font-weight:bold;font-size:18px;color:black;">生成客群</span>

      </div>
      <customerAddForm v-if="drawer" :tree-data="treeLabelData" :csmcustomerlable-opt="csmcustomerlableOpt" :options-cascader="optionsCascader" @closedrawer="drawer=false" />
    </el-drawer>
    <el-dialog title="短信群发" width="55%" class="msgdialog" :visible.sync="dialogFormVisible">
      <msgAdd v-if="dialogFormVisible" :check-row="checkRow" :pre-type="customertype" @cancel="dialogFormVisible=false" />
    </el-dialog>
    <el-dialog title="查看" :visible.sync="dialogFormVisible2" width="75%">
      <Check :check-row="checkRow" :csmcustomerlable-opt="csmcustomerlableOpt" :csmcustomerlable-keys="csmcustomerlableKeys" />
    </el-dialog>

    <el-dialog
      title="文件导入新建客群"
      :visible.sync="uploadDialog"
      width="500px"
      center
      @closed="closeDialog"
    >

      <el-row v-loading="uploadingpage">
        <el-col>

          <el-row>
            <el-col :span="6" class="up-label-txt text-right">客户群名称：</el-col>
            <el-col :span="18">
              <el-input v-model="name" size="small" />
            </el-col>
          </el-row>
          <!--<el-row style="margin-top:20px">
            <el-col :span="6" class="up-label-txt text-right">网格：</el-col>
            <el-col :span="18">
              <el-cascader
                v-model="gridIdUp"
                style="width:100%"
                :options="optionsCascader"
                size="small"
                clearable
                :props="{
                  checkStrictly:true
                }"
                :show-all-levels="false"
                @change="upCascaderChange"
              />
            </el-col>
          </el-row>-->

          <el-row style="margin-top:10px">
            <el-col :span="6" class="text-right">
              <span style="opacity:0">opacity0</span>
            </el-col>
            <el-col :span="18" style="position:relative;text-algin:left">
              <el-upload
                text="提交数据中..."
                class="upload-demo"
                :action="actionUrl"
                :before-upload="beforeFileUpload"
                :on-success="upsuccess"
                :on-error="uperror"
                :headers="headers"
                :with-credentials="true"
                :data="{

                  type:customertype,
                  createUserName:createUserName,
                  createUserId:createUserId,
                  name:name,
                  countyId:countyId,
                  cityId:cityId,
                  gridId:gridId,
                  countyName:countyName,
                  gridName:gridName,
                  cityName:cityName
                }"
                multiple
                :limit="1"

                :file-list="fileList"
              >
                <el-button size="mini" type="primary">点击上传</el-button>

                <div slot="tip" class="el-upload__tip">请下载模板，根据模板上传文件，不超过5M</div>
              </el-upload>

              <a id="spea" class="inner" :href="downUrl"><span>下载模版</span></a>

            </el-col>
          </el-row>

        </el-col>
      </el-row>

    </el-dialog>
    <!-- 根据基站导入客群 -->
    <el-dialog
      title="根据基站ID导入客群"
      :visible.sync="uploadDialog2"
      width="500px"
      center
      @closed="closeDialog"
    >

      <el-row v-loading="uploadingpage">
        <el-col>
          <el-row style="margin-bottom:10px;display: flex; align-items: center;">
            <el-col :span="6" class="up-label-txt text-right">驻留类型：</el-col>
            <el-col :span="18">
              <!-- <el-select v-model="regionType" size="small" multiple style="width:100%">
                <el-option label="全天top1" value="全天top1" />
                <el-option label="全天top2" value="全天top2" />
                <el-option label="全天top3" value="全天top3" />
                <el-option label="办公top1" value="办公top1" />
                <el-option label="办公top2" value="办公top2" />
                <el-option label="办公top3" value="办公top3" />
                <el-option label="居家top1" value="居家top1" />
                <el-option label="居家top2" value="居家top2" />
                <el-option label="居家top3" value="居家top3" />
              </el-select> -->
              <div>居住地/办公地常驻用户</div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="6" class="up-label-txt text-right">客户群名称：</el-col>
            <el-col :span="18">
              <el-input v-model="name2" size="small" />
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="6" class="text-right">
              <span style="opacity:0">opacity0</span>
            </el-col>
            <el-col :span="18" style="position:relative;text-algin:left">
              <el-upload
                text="提交数据中..."
                class="upload-demo"
                :action="actionUrl2"
                :before-upload="beforeFileUpload2"
                :on-success="upsuccess"
                :on-error="uperror"
                :headers="headers"
                :with-credentials="true"
                :data="{
                  regionType:regionType,
                  type:customertype,
                  createUserName:createUserName,
                  createUserId:createUserId,
                  name:name2,
                  countyId:countyId,
                  cityId:cityId,
                  gridId:gridId,
                  countyName:countyName,
                  gridName:gridName,
                  cityName:cityName
                }"
                multiple
                :limit="1"
                :file-list="fileList"
              >
                <el-button size="mini" type="primary">点击上传</el-button>

                <div slot="tip" class="el-upload__tip">请下载模板，根据模板上传文件，不超过5M</div>
              </el-upload>

              <a id="spea" class="inner" :href="downUrl2"><span>下载模版</span></a>

            </el-col>
          </el-row>

        </el-col>
      </el-row>

    </el-dialog>

  </div>
</template>
<script>
import customergroupCommonTable from './components/customergroupCommonTable.vue'
import Banner from './../../components/common/Banner.vue'
import customerAddForm from './../../components/common/CustomerAddForm.vue'
import msgAdd from './../../components/common/msgAdd'
import Check from './Checkdetail'
import Imp1 from './imp'
import { csmcustomerlable, getColumNames, labelLists, exportCareCustomerPhoneList, delCsmcustomerById, getByPhoneNo, getAllGrides, csmcustomer, csmsendmessagehistory, exportDoc, pushCode } from '@/api/customer/index'
import tool from '@/views/bj_proatal_web/utils/utils'
import { getToken } from '@/utils/auth'
 import { eventBus } from '@/main.js'
let fromLocal = null
export default {
  components: {
    Banner,
    customerAddForm,
    msgAdd,
    Check,
    Imp1,
    customergroupCommonTable
  },
  inject: ['showExportBtn'],
  beforeRouteEnter(to, from, next) {
    fromLocal = from
    next()
  },

  data() {
    return {
      customertypeSN: '1',
      layoutType: 'layoutType3',
      from: '',
      // 文件导入新建客群
      tableLoading: false,
      createUserId: '',
      createUserName: '',
      name: '',
      name2: '',
      cityId: '',
      countyId: '',
      gridId: '',
      cityName: '',
      countyName: '',
      gridName: '',
      regionType: [],

      gridIdUp: [],

      fileList: [],

      actionUrl: process.env.VUE_APP_BASE_API + '/portal/csmcustomer/fileUpload', // 上传的图片服务器地址

      actionUrl2: process.env.VUE_APP_BASE_API + '/portal/csmcustomer/uploadRegionFile', // 上传的图片服务器地址

      headers: {
        Authorization: 'Bearer ' + getToken()
      },

      // actionUrl: '/dev-api/portal/csmcustomer/fileUpload',
      downUrl: process.env.VUE_APP_BASE_API + '/portal/csmcustomer/getTemplate',
      downUrl2: process.env.VUE_APP_BASE_API + '/portal/csmcustomer/getRegionTemplate',
      uploadDialog: false,
      uploadDialog2: false,
      uploadingpage: false,

      //
      checkRow: {},
      // 指数指标名称
      csmcustomerlableOpt: [],
      csmcustomerlableKeys: [],
      // 指数指标对象 所有标签 选中了哪个标签 哪个标签就传1 没有专门的参数字段明
      // 指数指标名称 没有专门的参数字段明

      csmcustomerlableSeleted: [],

      formInline: {
        timeRange: [],
        careStatus: '',
        gridId: '',
        name: ''
      },
      optionsCascader: [],
      conditionsize: 'small',
      menuActiveVue: 1,

      customertype: '0',
      activeName: '2',
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      satisfactionOpts: [
        { label: '0~1 分', value: '0,1' },
        { label: '1~2 分', value: '1,2' },
        { label: '2~3 分', value: '2,3' },
        { label: '3~4 分', value: '3,4' },
        { label: '4~5 分', value: '4,5' },
        { label: '5~6 分', value: '5,6' },
        { label: '6~7 分', value: '6,7' },
        { label: '7~8 分', value: '7,8' },
        { label: '8~9 分', value: '8,9' },
        { label: '9~10 分', value: '9,10' }
      ], // 得分
      tableDatas: [],
      columnList: [
        {
          key: 'code',
          name: '客户群编号'
        },
        {
          key: 'name',
          name: '客户群名称'
        },
        {
          key: 'number',
          name: '用户数量'
        },
        {
          key: 'careStatus',
          name: '状态'
        },
        {
          key: 'isSend',
          name: '是否下发短信'
        },
        // // 1-未推送 2-推送中 3-已推送
        // {
        //   key: 'isPush',
        //   name: '是否推送'
        // },
        // {
        //   key: 'gridName',
        //   name: '网格'
        // },
        {
          key: 'createTime',
          name: '创建时间'
        }
      ],
      tuisongRadio: 'EMOS',
      saveListParams: null,
      treeLabelData: [],
      columNamesMap: {},

      drawer: false,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      visible: false

    }
  },
  watch: {
    csmcustomerlableSeleted: {
      deep: true,
      handler: function(v, oldv) {

      }
    },
    activeName: {
      deep: true,
      handler: function(v, oldv) {
        // this.resetForm('queryForm')
        this.page.current = 1
        if (v === '1' || v === '0') {
          this.$nextTick(() => {
            this.formInline.careStatus = v
          })
        } else {
          this.formInline.careStatus = ''
        }

        this.$nextTick(() => {
          this.queryList()
        })
      }
    },
    customertype: {
      deep: true,
      handler: function(v, oldv) {
        if (v === '0') {
          // this.$set(this.columnList, 3, { key: 'careStatus', name: '状态' })// 关怀状态
          this.columnList.splice(5, 2)
        } else if (v === '1') {
          // this.$set(this.columnList, 3, { key: 'careStatus', name: '状态' })// 修复状态
          // {
        //   key: 'isSend',
        //   name: '是否下发短信'
        // },
        // // 1-未推送 2-推送中 3-已推送
        // {
        //   key: 'isPush',
        //   name: '是否推送'
        // },
          this.columnList.splice(5, 0, { key: 'isPush', name: '是否推送' }, { key: 'gridName', name: '网格' })
        }
      }
    }
  },
  mounted() {
    // /repair/10000058
    // /msghistory/**********
    if (fromLocal && (fromLocal.name == 'msghistory' || fromLocal.name == 'repairanalysis')) {
      const cacheparams = this.$store.getters.customergrouptablequery
      const { formInline, page, customertype } = cacheparams
      if (formInline && page && customertype) {
        this.formInline = formInline
        this.page = page
        this.customertype = customertype
      }
    }

    // 上传需要用户信息
    let userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      userInfo = JSON.parse(userInfo)
      this.createUserId = userInfo.id
      this.createUserName = userInfo.name
    }

    this.init()
  },
  methods: {
    down(row) {
      exportDoc({ code: row.code }).then(response => {
        // 兼容ie
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '客群明细.xls'
          )
          return false
        }

        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '客群明细' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    init() {
      // let csmcustomerlableOpt = []
      // const csmcustomerlableKeys = []
      // csmcustomerlable().then(res => {
      //   if (res.code == 200 && Array.isArray(res.data)) {
      //     csmcustomerlableOpt = res.data.map(i => {
      //       csmcustomerlableKeys.push(i.code)
      //       i.label = i.name
      //       i.value = i.code
      //       return i
      //     })
      //   }
      //   this.csmcustomerlableKeys = csmcustomerlableKeys || []
      //   this.csmcustomerlableOpt = csmcustomerlableOpt || []
      // })

      const p0 = labelLists()// 获取所有标签tree
      const p1 = getColumNames()// 获取与标签对应的map

      Promise.all([p0, p1]).then(values => {
        const res0 = values[0]
        const res1 = values[1]

        let { code, data } = res0
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }

        if (res1.code == 200) this.columNamesMap = res1.data

        this.queryList()
      })

      // 获取所有网格
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr
        }
      })
      // this.queryList()
    },
    gopushCode(row) {
      pushCode(row.code).then(res => {

      }).finally(() => {
        this.queryList()
      })
    },
    // 获取不满意指标map
    getColumNames() {
      getColumNames().then(res => {
        const { code, data } = res
        if (code == 200) this.columNamesMap = data
      })
    },
    // 获取所有标签
    labelLists() {
      labelLists().then(res => {
        let { code, data } = res
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }
      })
    },
    sendMsg(row) {
      this.checkRow = row
      this.dialogFormVisible = true
    },
    // 推送系统
    putSys(row) {
      row.popvervis = !row.popvervis
    },
    openPopver(row) {
      this.tuisongRadio = 'EMOS'
      row.popvervis = !row.popvervis
    },
    upCascaderChange(v) {
      if (Array.isArray(v) && v.length) {
        const tempgridobj = this.getAllGridPathObj(v)

        // 处理网格

        this.cityId = tempgridobj.cityId
        this.countyId = tempgridobj.countyId
        this.gridId = tempgridobj.gridId
        this.gridName = tempgridobj.gridName
        this.countyName = tempgridobj.countyName
        this.cityName = tempgridobj.cityName
      }
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascader } = this
      let grandtar = null// 第一级
      let parenttar = null// 第二级
      let gridtar = null// 第三级
      if (Array.isArray(keyarr) && keyarr.length) {
        grandtar = optionsCascader.filter((i) => i.cityId == keyarr[0])// 爷爷item
        console.log('grandtarxxxx:', grandtar)
        grandtar = grandtar[0]
        console.log('grandtar:', grandtar)
        if (grandtar && grandtar.children && grandtar.children.length && keyarr[1]) {
          parenttar = grandtar.children.filter((j) => j.cityId == keyarr[1])
          parenttar = parenttar[0]

          if (parenttar && parenttar.children && parenttar.children.length && keyarr[2]) {
            gridtar = parenttar.children.filter((j) => j.cityId == keyarr[2])
            gridtar = gridtar[0]
          }
        }
        const obj = {
          cityId: grandtar.cityId,
          cityName: grandtar.label,
          countyId: parenttar.cityId,
          countyName: parenttar.cityName,
          gridId: gridtar.cityId,
          gridName: gridtar.cityName
        }
        console.log('网格选择的是：', obj)
        return obj
      } else {
        return {}
      }
    },
    beforeFileUpload(file) {
      // 文本导入前判断文件类型
      const msg = file.name.substring(file.name.lastIndexOf('.') + 1)
      console.log('msg:', msg)
      if (!(msg === 'xlsx')) {
        this.$message.error('上传文件只能是 xlsx')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 10M!')
        return false // 必须返回false
      }
      // 客户群名称
      if (!this.name) {
        this.$message.error('输入客户群名称')
        return false
      }
      this.uploadingpage = true
      return true
    },
    beforeFileUpload2(file) {
      // 文本导入前判断文件类型
      const msg = file.name.substring(file.name.lastIndexOf('.') + 1)
      console.log('msg:', msg)
      if (!(msg === 'xlsx')) {
        this.$message.error('上传文件只能是 xlsx')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 10M!')
        return false // 必须返回false
      }
      // 客户群名称
      if (!this.name2) {
        this.$message.error('输入客户群名称')
        return false
      }
      this.uploadingpage = true
      return true
    },

    upsuccess(response, file, fileList) {
      console.log('response:', response)
      this.uploadDialog = false
      this.uploadDialog2 = false
      this.fileList = []
      this.name = ''
      this.name2 = ''
      if (response && response.code == 200) {
        this.$message.success('创建客群成功')
        this.queryList()
        this.uploadingpage = false
      } else {
        this.uploadingpage = false
        // let p = cryptoUtils.decrypt(response);
        // if(p) p = JSON.parse(p)

        this.$message.error(response.msg)
      }
      this.uploadingpage = false
    },
    uperror(response, file, fileList) {
      console.log(response)
      this.$message.error('创建客群失败')
      this.uploadingpage = false
    },
    closeDialog() {
      this.uploadDialog = false
      this.uploadDialog2 = false
      this.name2 = ''
      this.regionType = []
      this.name = ''
      this.gridIdUp = []
      this.fileList = []
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    },
    // 查看分析结果
    openCheckDetailEcharts(row) {
      console.log('row:', row)
      // // 测试
      // if (process.env.NODE_ENV === 'development') {
      //   row.id = 10000041
      // }
      sessionStorage.setItem('menuUrl', `/repair/${row.id}`)
      this.$router.push({
        path: `/repair/${row.id}`,
        query: this.saveListParams
      })
    },

    downDetail(row) {
      //  this.statType = mode
      // this.date = monthValue
    //  金库认证 后端返回 code 430 时 说明没有进行金库认证
      // eventBus.$emit('startJKauth');//开始金库认证
      const params = {
        code: row.code
      }
      this.$confirm('是否确认下载当前客群的手机号码?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return exportCareCustomerPhoneList(params)
        })
        .then((response) => {

          // 1引入 eventBus 2 添加如下代码
         //******** */
          let {code}=response||{};
          if(code=='430'){//没有进行金库认证

            eventBus.$emit('startJKauth');//开始金库认证
            return
          }
            //******** */

          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              `${row.name}` + '客群号码.xls'
            )
            return false
          }
          const url = URL.createObjectURL(response)
          console.log('url==>', url)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', `${row.name}` + '客群号码.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    },

    // 查看短信发送记录
    openCheckDetail(row) {
      this.checkRow = row
      sessionStorage.setItem('menuUrl', `/msghistory/${row.code}`)
      this.$router.push({
        path: `/msghistory/${row.code}`,
        query: this.saveListParams
      })
    },
    // 删除
    dele(row) {
      this.tableLoading = true
      delCsmcustomerById(row.id).then(res => {
        const { code, data } = res
        if (code == 200) {
          this.$message.success('删除成功')
          this.queryList()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },

    getByPhoneNo(row) {
      getByPhoneNo(row.phoneNo).then(res => {
        const { code, data } = res
      })
    },
    query() {
      this.page.current = 1
      this.queryList()
    },

    // 查询表格数据
    queryList() {
      const { formInline, page, customertype } = this
      const { timeRange } = formInline

      this.$store.commit('UPDATE_TABLE_QUERY', { formInline, page, customertype })

      const params = Object.assign({}, formInline, { pagination: { currentPage: page.current, pageSize: page.size }}, { type: customertype })

      if (timeRange && timeRange.length && Array.isArray(timeRange)) {
        params.beginTime = tool.formatterDate(timeRange[0], 'yyyy-MM-dd')
        params.endTime = tool.formatterDate(timeRange[1], 'yyyy-MM-dd')
      }

      if (Array.isArray(params.gridId) && params.gridId.length) {
        params.gridId = params.gridId[params.gridId.length - 1]
      }

      delete params.timeRange
      delete params.total

      console.log('list params:', params)
      this.tableLoading = true
      csmcustomer(params).then(res => {
        const { code, data } = res

        if (code == 200) {
          const { records, total, pages } = data

          // 处理不满意指标
          records.forEach(i => i.popvervis = false)

          console.log('records:', records)

          this.tableDatas = records || []
          this.page.total = total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },

    resetForm(formName) {
      this.csmcustomerlableSeleted = []
      this.formInline.timeRange = []
      this.$refs[formName].resetFields()
      this.activeName = '2'
        this.$nextTick(()=>{
         this.page.current = 1
      this.queryList()
      })
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },
    handleClickTab() {

    },
    handleClickTabcustomerType() {
      this.page.current = 1
      this.queryList()
    },
    handleCloseDrwer(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {})
    },
    handleChangeCascader() {

    }

  }

}
</script>

<style >
.el-popper.el-cascader__dropdown.treeLabelDataCascader .el-checkbox.is-disabled .el-checkbox__input.is-disabled{

  opacity: 0!important;

}

</style>

<style lang='scss' scoped>
 .xps :deep(.el-date-table td.today span) {
  background-color: #FF9900!important;
}
.up-label-txt{
  font-size: 14px;
  line-height: 32px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
a#spea{
color:#fff!important;
background:#00bbcf;
width:80px;
text-align: center;
height:28px;
border-radius: 3px;
margin-left: 10px;
text-decoration:none!important;
display:inline-block;
&.inner{
  position: relative;
  left: 100px;
  font-size: 12px;
  top:-52px;
  line-height: 28px;
}

}
.msgdialog :deep(.el-form-item__label) {
   font-size: 14px;
    font-weight: 400;
    text-align: right;
    color: #262626;
    letter-spacing: -0.27px;
}
.msgdialog :deep(.el-checkbox__label) {
  font-size: 14px;
font-weight: 400;
text-align: left;
color: #595959;
line-height: 20px;
letter-spacing: -0.27px;
}
:deep(.el-dialog__header) {
  padding:8px 10px;
  line-height: 20px;
  text-align: center;
  color:#262626;
  background:#f5f5f5;
  font-size: 20px;
}
:deep(.el-dialog__headerbtn) {
  top:12px;
  line-height: 20px;

}
:deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #ff9900;
}
:deep(.el-textarea__inner:focus) {
  border-color: #ff9900;
}

.solicitude-task{
        background-color: rgba(242, 242, 242, 1);
        min-height: 100vh;
        :deep(.el-input-group__append), :deep(.el-input-group__prepend) {
          background:#F19733;
          color:#262626;
          &:hover{
             color:#fff;
          }
        }

        .layout{
            padding: 0 50px;

            &.sp{
                :deep(.el-tabs__header) {
                margin:0!important;
              }
            }

            :deep(.el-tabs__nav-wrap::after) {
              height:0px;
              background:#E6E6E6;
            }

            :deep(.el-tabs__active-bar) {
              background:#FF9900;
            }
            &.nobottomline{

              :deep(.el-tabs__active-bar) {
              opacity: 0;

            }

            }

            :deep(.el-tabs__item.is-active) {
              font-size: 14px;
              font-weight: 500;
              text-align: left;
              // color: #262626;
              color:#FF9900;
              opacity: 1;

            }
            :deep(.el-tabs__item) {
              font-size: 14px;
              font-weight: 400;
              text-align: left;
              // color: #595959;
              color:rgba(96,98,102,0.6)

            }
            :deep(.el-form-item.el-form-item--medium) {margin-right:20px}
            :deep(.el-form-item__content .el-input-group) {
              vertical-align: middle;
            }
            :deep(.el-pagination) {text-align: right;}
            .speformitem :deep(.el-form-item__label) {
              // width:96px;
            }
            .timeRange {

            }

        }
      .flexbox{
        display: flex;
        position: relative;

        .formbox{
          flex:1;

        }
        .btnbox{
          position: absolute;
          right:0;
          top:44px;
        }
      }

      .el-cascader {
        :deep(.el-icon-arrow-down:before ) {
          content: "\E6E1";

        }
       :deep(.el-icon-arrow-down) {
         transform: rotate(180deg);
       }
       :deep(.is-reverse.el-icon-arrow-down) {
         transform: rotate(0deg);
       }

  }

    }

 :deep(.el-table--medium .el-table__cell) {
   padding:8px 0;
 }
:deep(.el-table th.el-table__cell.is-leaf),
:deep(.el-table td.el-table__cell) {
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }
.syst{
 font-weight: bold;
  color:#262626;
  font-size: 14px;
}
 .sysbox{
   display: flex;
   padding:20px 0;
   >div.sysboxitem{
     width:33%;
     flex:1;
     text-align: center;
     span{
       cursor: pointer;
       color:#595959;
       display: inline-block;
       padding:0 3px;
       border-radius: 3px;
       &.active{
         font-weight: bold;
         color:#fff;

         background:#ff9900;;
       }
     }
   }
 }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
.optionbtnbox{
  display: flex;
  >div{
    &:nth-child(1){
      width:80px;
        flex:1;
    }
     &:nth-child(2){
      width:80px;
    }
     &:nth-child(3){
      width:50px;
    }
     &:nth-child(4){
      width:50px;
    }
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
  &.x{
    >div{
       &:nth-child(1){
      width:80px;
        flex:1;
    }
    &:nth-child(2){
      width:50px;
    }
     &:nth-child(3){
      width:80px;
    }
     &:nth-child(4){
      width:50px;
    }
     &:nth-child(5){
      width:50px;
    }
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
    }
  }
}

</style>
