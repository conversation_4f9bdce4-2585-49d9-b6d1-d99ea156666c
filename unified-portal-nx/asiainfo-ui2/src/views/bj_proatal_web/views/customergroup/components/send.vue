<template>
  <div style="position:relative">
    <el-button style="position:absolute;right:0;top:-20px" type="default" size="small" @click="cancel">返回</el-button>
    <div v-show="!dialogFormVisible" v-loading="submitLoading" class="sendForm">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
        <div class="box">
          <div style="margin-bottom:20px">
            <div class="pt">场景管理</div>
            <div>
              <div class="liner" />
            </div>
          </div>
          <el-form-item label="选择场景" prop="scene">
            <el-radio-group v-model="ruleForm.scene" @change="sceneChange">
              <el-radio label="排查">排查</el-radio>
              <el-radio label="修复">修复</el-radio>
              <el-radio label="关怀">关怀</el-radio>
              <el-radio label="调研">调研</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="box" v-show="ruleForm.scene=='调研'">
          <div >
            <div class="pt">选择问卷</div>
            <div>
              <div class="liner" />
            </div>
            <div style="padding:10px;">
              <el-table
                :data="items"
                border
                center
                style="width: 100%"
                highlight-current-row
                @row-click="singleElection"
              >
                <el-table-column align="center" width="55" label="选择" header-align="center">
                  <template slot-scope="scope">
                      <el-radio
                      class="radio"
                      v-model="templateSelection"
                      :label="scope.$index"
                      >&nbsp;</el-radio>
                  </template>
                </el-table-column>

                <el-table-column prop="id" align="center" header-align="center" label="问卷编号"></el-table-column>
          <el-table-column prop="questionnaireName" align="center" header-align="center" label="问卷名称"></el-table-column>

          <el-table-column prop="userName" align="center" header-align="center" label="创建者"></el-table-column>
          <el-table-column  align="center" header-align="center" prop="releaseDate" label="发布时间"></el-table-column>
           </el-table>
           <div style="margin-top:20px;text-align:right">
                <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="fenye.currentPage"
                :page-size.sync="fenye.pageSize"
                layout="total,prev, pager, next, jumper"
                :total="fenye.totalCount"
        >
        </el-pagination>

           </div>


            </div>
          </div>
        </div>


        <div class="box" >
          <div>
            <div class="pt">策略管理</div>
            <div>
              <div class="liner" />
            </div>
          </div>
          <div class="secondpt">
            频次管理
          </div>
          <el-form-item label="关怀时间" prop="datetime">
            <el-date-picker
              :disabled="ruleForm.scene=='调研'?true:false"
              v-model="ruleForm.datetime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始"
              end-placeholder="结束"
            />
          </el-form-item>
          <el-form-item label="关怀频率" prop="frequency">
            <el-select  :disabled="ruleForm.scene=='调研'?true:false" v-model="ruleForm.frequency" placeholder="请选择">
              <el-option
                v-for="item in frequencyOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关怀周期" prop="cycle">
            <el-select  :disabled="ruleForm.scene=='调研'?true:false" v-model="ruleForm.cycle" placeholder="请选择">
              <el-option
                v-for="item in cycleOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <div class="secondpt">
            策略方式
          </div>
          <el-form-item label="策略方式" prop="strategy">
            <el-checkbox-group v-model="ruleForm.strategy">
              <el-checkbox key="短信" :disabled="ruleForm.scene=='修复'||ruleForm.scene=='排查'||ruleForm.scene=='关怀'||ruleForm.scene=='调研'?true:false" label="短信" name="strategyOpt">短信</el-checkbox>
              <el-checkbox key="电话" :disabled="ruleForm.scene=='关怀'||ruleForm.scene=='调研'?true:false" label="电话" name="strategyOpt">电话</el-checkbox>
              <el-checkbox key="上门" :disabled="ruleForm.scene=='关怀'||ruleForm.scene=='调研'?true:false" label="上门" name="strategyOpt">上门</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="secondpt" v-if="ruleForm.scene=='调研'">
            问卷有效时间
          </div>
          <el-form-item label="问卷有效时间" prop="duration" label-width="130px" v-if="ruleForm.scene=='调研'">
            <el-input style="width: 80px;" v-model="ruleForm.duration" :min="0" :max="999" :precision="0"
              oninput="value = value.replace(/[^\d]/g, '')
              if(isNaN(value)) {value = ''}
              if(value.charAt(0) === '0' && value.charAt(1) !== '' ) { value = '0'}
              if(value.length === 4) {value = value.substr(0,3)}
            " type="number" :controls="false"></el-input>
            <span style="margin-left: 10px;">天(不填或填0为永久有效)</span>
          </el-form-item>
        </div>

        <div v-if="ruleForm.strategy[0]!='短信'" class="box">
          <div style="margin-bottom:20px">
            <div class="pt">服务管理</div>
            <div>
              <div class="liner" />
            </div>
          </div>
          <el-form-item label="服务管理" prop="channels">
            <el-checkbox-group v-model="ruleForm.channels">
              <!-- eoms grid zw man ivr phone -->
              <el-checkbox key="1" label="eoms" name="channelsOpt">EOMS</el-checkbox>
              <el-checkbox key="2" label="grid" name="channelsOpt">网格通</el-checkbox>
              <el-checkbox key="3" label="zw" name="channelsOpt">装维</el-checkbox>
              <el-checkbox key="4" :disabled="ruleForm.scene=='修复'?true:false" label="online" name="channelsOpt">在线</el-checkbox>
              <el-checkbox key="6" :disabled="ruleForm.scene=='修复'?true:false" label="phone" name="channelsOpt">电渠</el-checkbox>

            </el-checkbox-group>
          </el-form-item>
        </div>

         <div v-if="ruleForm.scene == '修复'" class="box">
          <div style="margin-bottom:20px">
            <div class="pt">关怀资源</div>
            <div>
              <div class="liner" />
            </div>
          </div>
          <el-form-item label="关怀资源" prop="caringResources" >

             <el-radio-group v-model="ruleForm.caringResources" >
              <el-radio label="赠送流量">赠送流量</el-radio>
              <el-radio label="赠送通话分钟数">赠送通话分钟数</el-radio>

            </el-radio-group>
          </el-form-item>
        </div>

        <div class="box" v-show="this.ruleForm.scene=='调研'">
          <div>
            <!-- <div class="pt">短信编辑</div> -->
            <!-- <div>
              <div class="liner" />
            </div> -->
              <el-form-item label="短信内容" prop="msgContent" >

      <!-- // warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期', -->
      <!-- #orderAndPhoneNum# -->

      <div class="message-edit">
        <el-button type="primary" size="mini" @click="setDefaultState">恢复默认</el-button>
        <el-button size="mini" class="upUrlBtn" :disabled="disabledUrlBtn"  @click="addToContent('ur')">{{ msgName.ur || `问卷链接` }}</el-button>
        <!-- <span style="color:#e6a23c;font-size:12px;"> (短信内容中只能插入一个问卷链接,如果要改变问卷链接在短信中的位置，请先删除后再重新插入！不支持插入图片！)</span> -->
        <div
          id="editable"
          ref="message-edit"
          class="content"
          contenteditable
          @drop="handleDrop"
          @dragover="handleDragOver"
          @keyup="handleEditChange"
          v-html="decodeMSG(msgContent)"

        />
      </div>
      <div style="text-align:right">{{msgCount}}/200</div>
    </el-form-item>

          </div>
         </div>


        <div style="margin-top:20px">
          <el-form-item>
            <el-button type="primary" size="mini" @click="submitFormBefore('ruleForm')">提交</el-button>
            <el-button size="mini" @click="cancel">取消</el-button>
          </el-form-item>
        </div>

      </el-form>
    </div>
    <div v-show="dialogFormVisible" style="width:65%;background:#fff;padding:30px 0 15px 0;" class="sendmsg">
      <msgAdd :check-row="checkRow" :pre-type="customertype" @cancel="cancelHandler" />
    </div>
  </div>

</template>
<script>
import { pushCustomer, queryByParamPage } from '@/api/customer/index'
import moment from 'moment'
import msgAdd from './../../../components/common/msgAdd2.vue'

export default {
  components: {
    msgAdd
  },
  props: {
    checkRow: {
      type: Object,
      required: true
    }
  },
  watch:{
    'ruleForm.scene'(v,oldv){
      if(v=='调研'){
        this.rules.datetime[0].required =false;
        this.rules.frequency[0].required =false;
        this.rules.cycle[0].required =false;
      }else{
         this.rules.datetime[0].required =true;
         this.rules.frequency[0].required =true;
         this.rules.cycle[0].required =true;
      }
      if(v=='修复'){
        this.rules.caringResources[0].required = true;
      }else{
         this.rules.caringResources[0].required = false;
      }

    }
  },
  data() {
    return {
      disabledUrlBtn:true,
       msgName: {
        ur: '问卷链接'
      },

      msgContent: '尊敬的客户您好！为了改善中国移动业务服务质量，诚邀您点击#ur#参与客户调查。本次调查预计将占用您2分钟时间，感谢您的支持！',
      msgCount:62,
      fenye: {
        currentPage: 1, // 分页  (全部问卷)
        pageSize: 10, // 每页条数  (全部问卷)
        totalCount: 0, // 数据总数  (全部问卷)
      },
      templateSelection:'',
      templateRadio:null,
      items:[],
      customertype: '',
      dialogFormVisible: false,
      submitLoading: false,
      ruleForm: {
        datetime: [],
        frequency: '',
        cycle: '',
        strategy: [],
        scene: '',
        channels: [],
        caringResources: '', //
        duration: undefined
      },
      frequencyOpts: [
        { label: '每季度', value: '每季度' },
        { label: '每月', value: '每月' },
        { label: '每日', value: '每日' }

      ], // 关怀频率的选项
      cycleOpts: [
        { label: '一次性', value: '一次性' },
        { label: '周期性', value: '周期性' }

      ], // 关怀周期的下拉选项 datetime frequency cycle
      rules: {
        datetime: [{ required: true, message: '请选择关怀时间', trigger: 'change' }],
        frequency: [{ required: true, message: '请选择关怀频率', trigger: 'change' }],
        cycle: [{ required: true, message: '请选择关怀周期', trigger: 'change' }],
        strategy: [{ required: true, message: '请选择策略方式', trigger: 'change' }],
        scene: [{ required: true, message: '请选择', trigger: 'change' }],
        channels: [{ required: false, message: '请选择', trigger: 'change' }],
        caringResources: [{ required: false, message: '请选择', trigger: 'change' }],
      }
    }
  },

  mounted() {

  const el = document.querySelector('#editable');
   const self = this;
    el.addEventListener('paste', (e) => {

       let msg = self.encodeMSG();
       let hasUr = false;
       if(msg.indexOf('%ur%')){
        hasUr = true;
       }

      console.log('msg=>',msg);
      console.log('e=>',e)
      // Get user's pasted data
      let data = e.clipboardData.getData('text/html') ||
          e.clipboardData.getData('text/plain');

          if(hasUr){
            if(data.indexOf(`>问卷链接<`)!=-1){
              this.$message.error('短信内容中已经有了问卷链接了，粘贴内容中重复包含了问卷链接');

              e.preventDefault();

              return;
            }
          }
          try {


          document.execCommand('insertHTML', false, data);


          } catch (error) {

          }


      // Prevent the standard paste behavior
      e.preventDefault();
    })
    this.getAllQus()
  },
  methods: {

     singleElection(row) {
      this.templateSelection = this.tableData&&this.tableData.indexOf(row);
      this.templateRadio = row.id;
    },

    handleCurrentChange() {
      // 全部问卷当前页变化时触发
      this.getAllQus();
    },

    handleSizeChange(val) {
      // 每页数据条数变化时触发
      console.log(`每页 ${val} 条`);
    },
     // 恢复默认
    setDefaultState() {
      // yjjb: '预警级别',

      this.msgContent = '尊敬的客户您好！为了改善中国移动业务服务质量，诚邀您点击#ur#参与客户调查。本次调查预计将占用您2分钟时间，感谢您的支持！'
      this.msgCount = this.msgContent.length;
      this.disabledUrlBtn = true;
      this.$refs['message-edit'].innerHTML = this.decodeMSG(this.msgContent)
    },
    // 判断最底层是否有元素#editable
     addToContent(type) {
       let msg = this.encodeMSG()||''
      if(msg){
      let reg = /%ur%/g;
      let temp =  msg.match(reg);
      if(temp && temp.length>=1){
        this.$message.error('短信内容中只能插入一个问卷链接');
        this.disabledUrlBtn = true;
        return;
      }
    }else {
       this.disabledUrlBtn = false;
    }

      // 获取光标位置
      const selection = window.getSelection()
      const range = selection.getRangeAt(0)
      console.log(selection)
      console.log(range)

      let pNode = this.$refs['message-edit'];


      if(selection.baseNode.parentNode.id=='editable'||selection.baseNode.id=='editable'|| pNode.contains(selection.baseNode)){

      }else{
        this.$message.warning('请将光标移到编辑区')
           this.$nextTick(() => {
        this.keepLastIndex(document.getElementById('editable'))
      })
        return;
      }
      // if (range.startContainer.parentNode.className !== 'content') {
      //   let ht = this.$refs['message-edit'].innerHTML;
      //   if(!ht) this.$refs['message-edit'].innerHTML = ' '
      //    return;
      // }

      range.setStart(range.endContainer, range.endOffset)
      const button = document.createElement('button')
      button.name = type
      button.disabled = true
      button.className = 'el-button el-button--default el-button--mini is-disabled'
      button.contentEditable = false
      button.draggable = true
      button.type = 'button'
      button.innerHTML = `<span>${this.msgName[type]}</span>`
      range.insertNode(button)

      this.$nextTick(() => {
        this.keepLastIndex(document.getElementById('editable'))
      })

    },
      keepLastIndex(obj) {
      var range = null
      if (window.getSelection) { // ie11 10 9 ff safari
        obj.focus() // 解决ff不获取焦点无法定位问题
        range = window.getSelection()// 创建range
        range.selectAllChildren(obj)// range 选择obj下所有子内容
        range.collapseToEnd()// 光标移至最后
      } else if (document.selection) { // ie10 9 8 7 6 5
        range = document.selection.createRange()// 创建选择对象
        // var range = document.body.createTextRange();
        range.moveToElementText(obj)// range定位到obj
        range.collapse(false)// 光标移至最后
        range.select()
      }
    let msg = this.encodeMSG()
    if(msg){
      let reg = /%ur%/g;
      let temp =  msg.match(reg);
      if(temp && temp.length>=1){
        this.disabledUrlBtn = true;
      }
    }else {
       this.disabledUrlBtn = false;
    }

    this.msgCount = msg.length

    },

    // 解析标识符为html
    decodeMSG(content) {
      const { msgName } = this

      //   warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期',

      content = content.replace(new RegExp('#ur#', 'g'), this.templateFactory('ur', msgName.ur))

      return content
    },
    templateFactory(name, val, dval) {
      // eslint-disable-next-line max-len
      return `<button name="${name}" disabled="disabled" class="el-button el-button--default el-button--mini is-disabled" contenteditable="false" draggable="true" type="button"><span contenteditable="false">${val || dval}</span></button>`
    },
      handleDrop(event) {},
    handleDragOver(event) {
      event.preventDefault()
    },
    handleEditChange(e) {
    console.log('1111')
    let msg = this.encodeMSG()
    console.log('msg=>',msg)
    if(msg){
      let reg = /%ur%/g;
      let temp =  msg.match(reg);
      if(temp && temp.length>1){
        this.$message.error('短信内容中只能插入1个问卷链接');
        this.disabledUrlBtn = true;

      }else if(temp&&temp.length==0){
        this.disabledUrlBtn = false;
      }else if(!temp){
        this.disabledUrlBtn = false;
      }
    }else{
     this.disabledUrlBtn = false;
    }

    this.msgCount = msg.length;
       if(msg.length>200){
        this.$message.error('短信内容不能超过200字')
       }
       if(msg=='%ur%'){
        console.log('2222')
          this.$nextTick(() => {
            console.log(window.getSelection())
            this.keepLastIndex(document.getElementById('editable'))
          })
       }

    },

    encodeMSG() {
      const contentDom = this.$refs['message-edit']
      let contentString = contentDom.innerHTML.trim()

      const doms = contentDom.querySelectorAll('button')
      for (let i = 0; i < doms.length; i++) {
        const item = doms[i]
        switch (item.name) {

          case 'ur':
            contentString = contentString.replace(item.outerHTML, '%ur%')
            break

          default:break
        }
      }
      const temDiv = document.createElement('div')

      temDiv.innerHTML = contentString
      return temDiv.innerText
    },
    // 获取所有问卷
    getAllQus() {
      const params = {
        pagination: {
          pageSize: this.fenye.pageSize,
          currentPage: this.fenye.currentPage
        },
        'uuid': '',
        'key': '',
        'startTime': '',
        'endTime': '',
        'sortQuestionCount': '',
        'sortReleaseDate': '',
        'quesquestionnaireState': 1,
        'type': '3',
        'paramUserInfo': {
          'userAccount': ''
        }
      }
       this.templateSelection = '';
      queryByParamPage(params).then(res => {
        let {currentPage,totalCount,objectBean} = res.data;
         objectBean.forEach((i,j) => {
            if(this.templateRadio == i.id) {
              this.templateSelection = j;
            }
          });
        this.fenye.currentPage = currentPage;
        this.items = objectBean;
        this.fenye.totalCount = totalCount;


      })
    },
    cancelHandler(data) {
      this.dialogFormVisible = false
      if (data && data == '1') {
        this.submitForm('ruleForm')
      }
    },
    submitFormBefore(formName) {
      let msg = this.encodeMSG()
      console.log('msg=>',msg)

        let {templateRadio} =this;
      let {scene} = this.ruleForm

      if(scene=='调研'){
        if(!msg) {
           this.$message.error('短信内容不能为空')
          return;
        }else if(msg.length>200){
          this.$message.error('短信内容不能超过200字')
          return
        }else if(msg.indexOf('%ur%')==-1){
           this.$message.error('短信内容中必须包含问卷链接')
          return
        }

          this.ruleForm.datetime = ''
          this.ruleForm.frequency = ''
          this.ruleForm.cycle = ''

          if(!templateRadio){
            this.$message.error('请选择问卷');
            return;
          }

      }

    if(scene!='修复'){
        this.ruleForm.caringResources = ''
      }


      // if(scene=='调研'){
      //     this.ruleForm.datetime = ''
      //     this.ruleForm.frequency = ''
      //     this.ruleForm.cycle = ''

      //     if(!templateRadio){
      //       this.$message.error('请选择问卷');
      //       return;
      //     }
      // }



      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleForm.strategy.indexOf('短信') != -1 && scene!='调研') {
            this.dialogFormVisible = true// 打开发送短信的窗口
          } else {
            this.submitForm(formName)
          }
        }
      })
    },
    submitForm(formName) {

      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { checkRow } = this
          const params = { ...this.ruleForm}
          if(this.ruleForm.scene=='调研'){
            params.questionId = this.templateRadio ;//绑定问卷
            params.content = this.encodeMSG();//短信内容
          }
          const { datetime } = params
          if (datetime.length) {
            params.startTime = moment(datetime[0]).format('YYYY-MM-DD HH:mm:ss')
            params.endTime = moment(datetime[1]).format('YYYY-MM-DD HH:mm:ss')
            delete params.datetime
          }
          console.log(params)
          if (params.strategy.indexOf('短信') != -1) {
            delete params.channels
          }
          params.strategy = params.strategy.join(',')

          params.code = checkRow.code
          this.submitLoading = true
          if(params.scene !='修复'){
            delete params.caringResources;
          }
          pushCustomer(params).then(res => {
            const { code } = res
            if (code == 200) {
              this.$message.success('提交成功！')

              this.$emit('closeSendDialog')
            }
          }).finally(() => {
            this.submitLoading = false
          }).catch(err=>{
            this.$message.error(err)
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    cancel() {
      this.$emit('closeSendDialog')
    },
    sceneChange(v) {
      if (v == '修复') {
        const chanelsnew = []
        if (this.ruleForm.channels.length) {
          this.ruleForm.channels.forEach(i => {
            if (i != 'online' && i != 'phone') {
              chanelsnew.push(i)
            }
          })
          this.ruleForm.channels = chanelsnew
        }
        if (this.ruleForm.strategy.indexOf('短信') != -1) {
          const tarIndx = this.ruleForm.strategy.indexOf('短信')
          this.ruleForm.strategy.splice(tarIndx, 1)
        }
      }
      if (v == '排查') {
        if (this.ruleForm.strategy.indexOf('短信') != -1) {
          const tarIndx = this.ruleForm.strategy.indexOf('短信')
          this.ruleForm.strategy.splice(tarIndx, 1)
        }
      }
      if (v == '关怀' || v == '调研') {
        this.ruleForm.strategy = ['短信']
      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.cell .el-radio__label) {
  display: none;
}
.sendmsg .el-table--border{
  border: 1px solid #e6e6e6;
}
.sendForm{
    padding:20px;
}
.box{
    background:#fff;
    padding:10px 20px;
    margin-top:15px;
}
.pt{
    font-weight: bold;
    font-size: 18px;
    line-height: 22px;

}
.liner{
    width:200px;
    height:8px;
    background:linear-gradient(90deg,rgba(253,151,33,0.9),rgba(253,151,33,0.6) , rgba(253,151,33,0.2),rgba(253,151,33,0));
}
.secondpt{
  line-height:22px;
  padding-left:10px;
  border-left: 5px solid rgb(253,151,33);
  margin-top:20px;
  font-size: 14px;
  margin-bottom: 10px;
}

:deep( textarea ) {
  font-family: inherit;
}
.message-edit {
      text-align: left;
      .tag {
        color: orange;
      }
      :deep( .el-dialog__body) {
        text-align: left;
      }
      .content {
        background-color: #efeff4;
        padding: 8px;
        border-radius: 3px;
        margin: 10px 0;
        text-align: left;
        line-height: 30px;
        min-height: 140px;
        &:focus {
          outline: none;
        }
        .el-button.is-disabled span{
          background-color: red;
        }
      }

      :deep( .el-button.is-disabled),
      :deep( .el-button.is-disabled:focus),
      :deep( .el-button.is-disabled:hover ) {
        color: #1C88DC;
        // cursor: move;
        cursor:not-allowed;

      }
       :deep( .el-button.is-disabled:hover ) {
        color: #1C88DC;
        // cursor: move;
        cursor:not-allowed;

      }
       :deep( .upUrlBtn.el-button.is-disabled:hover) {
        color: gray !important;
       }
       :deep( .upUrlBtn.el-button.is-disabled) {
         color: gray !important;
         background:#e6e6e6;
       }
        :deep( .upUrlBtn.el-button) {
           color: #1C88DC!important;
        }
    }
    :deep( .el-input ) {
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
          -webkit-appearance: none;
      }
    }

</style>
