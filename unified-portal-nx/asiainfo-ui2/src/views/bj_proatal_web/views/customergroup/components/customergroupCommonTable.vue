<template>
  <div>
    <CommonTable
      ref="zbGroupTable"
      style="padding-top:10px;"
      :conditions="conditions"
      :columns="tableColumns"
      show-overflow-tooltip
      :max-height="560"
      highlight-current-row
      row-class-name="row-class-name"
      :pagination="true"
      @row-click="testRowClick"
      @queryData="(params)=>queryData(params,'zbGroupTable')"
    >

      <div slot="customerId" slot-scope="scope" style="text-align:center">
        <span style="color:red">{{ scope.data.row['customerId'] }}</span>
      </div>
       <div slot="type" slot-scope="scope" style="text-align:center">
        <span>{{ scope.data.row['type']=='1'?'修复客群':scope.data.row['type']=='0'?'关怀客群':'' }}</span>
      </div>
      <div slot="userStatus" slot-scope="scope" style="text-align:center">

        <span :style="{color:'white',borderRadius:'4px',display:'inline-block',padding:'2px 5px',background:scope.data.row['userStatus']=='已评测'?'pink':'gray'}">{{ scope.data.row['userStatus'] }}</span>

      </div>
      <div slot="repairStatus" slot-scope="scope" style="text-align:center">
        <span> {{ scope.data.row['repairStatus']==3?'待修复':scope.data.row['repairStatus']==2?'已修复':scope.data.row['repairStatus']==1?'已接触未修复':scope.data.row['repairStatus']==0?'下发未接触成功':'' }}</span>
      </div>
       <div slot="isComplaint" slot-scope="scope" style="text-align:center">
        <span> {{ scope.data.row['isComplaint']=='Y'?'是':scope.data.row['isComplaint']=='N'?'否':'' }}</span>
      </div>

      <!-- <div slot="isResult" slot-scope="scope" style="text-align:center">
        <span> {{ scope.data.row['type']=='0'&&scope.data.row['isResult']=='0'?'未关怀':scope.data.row['type']=='0'&&scope.data.row['isResult']=='1'?'已关怀':scope.data.row['type']=='1'&&scope.data.row['isResult']=='0'?'未修复':scope.data.row['type']=='1'&&scope.data.row['isResult']=='1'?'已修复':''}}</span>
      </div> -->
      <div slot="isPush" slot-scope="scope" style="text-align:center">
        <!-- 1-未推送 2-已推送 3-推送结果已返回 -->
        <span> {{ scope.data.row['isPush']==3?'推送结果已返回':scope.data.row['isPush']==2?'已推送未返回':scope.data.row['isPush']==1?'未推送':'' }}</span>
      </div>


      <div slot="isSend" slot-scope="scope" style="text-align:center">
        <span >{{ scope.data.row['isSend']=='0'?'已发送':scope.data.row['isSend']=='1'?'未发送':'' }}</span>
      </div>

        <!-- 不满指标 低于6分显示红  -->
        <div slot="csmcustomerlableVisibleTxt" slot-scope="scope">
               <span v-for="(x,ix) in scope.data.row['csmcustomerlableVisibleTxt']" :key="ix">
                        <div v-if="x.point>-1">
                          <!-- <span>{{ scope.row[item.key].length }}</span> -->
                          <span :style="{color:0<x.point<7?'':''}">{{ x.txt }}</span>
                          <span v-show="ix!==scope.data.row['csmcustomerlableVisibleTxt'].length-1">{{ '、' }}</span>
                    </div>
              </span>
      </div>
      <div slot="actions" slot-scope="scope">

        <div class="optionbtnbox">
          <div>
            <span class="coloryellow" @click="clickCheck(scope.data.row)">查看</span>
          </div>
          <div>
             <span class="coloryellow" @click="down(scope.data.row)">下载</span>
          </div>
          <div v-if="scope.data.row['isPush']==1">
             <span class="coloryellow"  @click="send(scope.data.row)">推送</span>
          </div>
          <div v-else>
             <span class="coloryellow"  >&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </div>

          <!-- <div>
            <span v-if="scope.data.notifyStatus=='0'&&scope.data.isSend==1" class="coloryellow" @click="sendMsg(scope.data)">短信回访</span>
            <span v-if="scope.data.notifyStatus=='1'" class="coloryellow" style="color:#8C8C8C;cursor:not-allowed">短信回访</span>
          </div> -->
          <div>

            <el-popconfirm
              confirm-button-text="删除"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="确定删除本条数据？"
              @confirm="dele(scope.data.row)"
            >
              <span slot="reference" class="coloryellow">删除</span>
            </el-popconfirm>

          </div>
        </div>
      </div>

      <div slot="additionBtn" style="position:absolute;right:0;top:-48px">
        <el-button type="default" size="small" @click="drawer=true">生成客群</el-button>
        <el-button size="small" type="default" @click="uploadDialog=true">导入客户</el-button>
      </div>
    </CommonTable>
    <el-dialog title="" :visible.sync="checkDialogVis" fullscreen :append-to-body="true"  custom-class="detaildialog" class="detaildialog">
      <Check @close="checkDialogVis=false" v-if="checkDialogVis" :check-row="checkRow"  />
    </el-dialog>

    <el-dialog title="" :show-close="false" :visible.sync="sendDialogVis" fullscreen :append-to-body="true"  custom-class="detaildialog" class="detaildialog">
      <Send v-if="sendDialogVis" @closeSendDialog="closeSendDialog" :checkRow="checkRow"/>
    </el-dialog>

    <el-drawer
      :visible.sync="drawer"
      size="60%"
      :append-to-body="true"
    >
      <div slot="title">
        <span style="font-weight:bold;font-size:18px;color:black;">生成客群</span>

      </div>
      <customerAddForm v-if="drawer" :csmcustomerlable-opt="csmcustomerlableOpt" :tree-data="treeLabelData" :options-cascader="optionsCascader" @closedrawer="closeDrawer" />
    </el-drawer>
    <el-dialog
      title="文件导入新建客群"
      :visible.sync="uploadDialog"
      width="500px"
      center
      append-to-body
      @closed="closeDialog"
    >

      <el-row v-loading="uploadingpage">
        <el-col>
          <el-row>
            <el-col :span="6" class="up-label-txt text-right">客户群名称：</el-col>
            <el-col :span="18">
              <el-input v-model="name" size="small" />
            </el-col>
          </el-row>
          <el-row style="margin-top:10px">
            <el-col :span="6" class="text-right">
              <span style="opacity:0">opacity0</span>
            </el-col>
            <el-col :span="18" style="position:relative;text-algin:left">
              <el-upload
                text="提交数据中..."
                class="upload-demo"
                :action="actionUrl"
                :before-upload="beforeFileUpload"
                :on-success="upsuccess"
                :on-error="uperror"
                :headers="headers"
                :with-credentials="true"
                :data="{
                  createUserId:createUserId,
                  createUserName:createUserName,
                  name:name,
                }"
                multiple
                :limit="1"
                :file-list="fileList"
              >
                <el-button size="mini" type="primary">点击上传</el-button>

                <div slot="tip" class="el-upload__tip">请下载模板，根据模板上传文件，不超过5M</div>
              </el-upload>

              <a id="spea" class="inner" :href="downUrl"><span>下载模版</span></a>

            </el-col>
          </el-row>

        </el-col>
      </el-row>

    </el-dialog>
  </div>

</template>

<script>
import CommonTable from '@/views/bj_proatal_web/nx-components/commonTable/index'
import Check from '@/views/bj_proatal_web/views/customergroup/components/check.vue'
import customerAddForm from '@/views/bj_proatal_web/views/customer/component/createV2'
import Send from './send'
import { zbGroupPage, getColumNames, labelLists, getAllGrides,getColumScore,removeById,zbexportDoc } from '@/api/customer/index'
import { getToken } from '@/utils/auth'


export default {
  components: {
    Send,
    CommonTable,
    Check,
    customerAddForm
  },
  data() {
    return {
      sendDialogVis:false,
      // 客群生成start******* */
      optionsCascaderExpand: [],
      drawer: false,
      csmcustomerlableOpt: [
        { label: '怎么样', value: '怎么样' },
        { label: '怎么样1', value: '怎么样1' }
      ],
      treeLabelData: [],
      optionsCascader: [],
      // 客群生成 over******* */

      checkRow: {},
      columNamesMap: {},
      columNamesPointMap: {},
      checkDialogVis: false,//查看的弹窗
      cachePrams:{"pagination":{"pageSize":10,"currentPage":1}},
      // 条件配置
      conditions: [
        // {
        //   type: 'cascader',
        //   key: 'gridId', // 字段名 根据客户群名称来搜索
        //   label: '网格',
        //   width:'150px',
        //   props:{multiple: false,checkStrictly: true },
        //   options:[]
        // },
         {
          type: 'input', // 输入框
          key: 'code', // 字段名 根据客户群名称来搜索
          label: '客群编码',
          width:'150px'
        },

        // {
        //    type: 'select',
        //   key: 'type',
        //   label: '类型',
        //   width:'100px',
        //   options: [{ label: '关怀客群', value: '0' }, { label: '修复客群', value: '1' }]
        // },

        // {
        //   type: 'select',
        //   key: 'customerPurpose',
        //   label: '客户群用途',
        //   options: [{ type: '用后即评', value: '用后即评' }] // {type:'',value:''}
        // //   getOpts: () => {}
        // },
        {
         type: 'input', // 输入框
          key: 'name', // 字段名 根据客户群名称来搜索
          label: '客群名称',
           width:'150px'
        },
        {
          type: 'daterange', // 'date' 日期范围 日期
          key: 'createTime',
          label: '创建日期',
          width: '250px',
          parmsName: ['beginTime', 'endTime'],
           pickerOptions:{
             disabledDate(time) {
            return time.getTime() > Date.now();
          }
          }
        },
        // {
        //   type: 'cascader', // 输入框
        //   key: 'x', // 字段名 根据客户群名称来搜索
        //   label: '级联',
        //   options: [
        //     {
        //       value: 'zhinan',
        //       label: '指南',
        //       children: [
        //         {
        //           value: 'zhinan1',
        //           label: '指南1',
        //           children: [
        //             {
        //               value: 'zhinan2',
        //               label: '指南2'
        //             }
        //           ]
        //         }
        //       ]
        //     },
        //     {
        //       value: 'zhinan3',
        //       label: 'p3',
        //       children: [
        //         {
        //           value: 'zhinan4',
        //           label: '指南5',
        //           children: [
        //             {
        //               value: 'zhinan6',
        //               label: '指南6'
        //             }
        //           ]
        //         }
        //       ]
        //     }
        //   ]
        // }
        //  {
        //   type:'date',// 'date' 日期范围 日期
        //   key:'time',
        //   label:'时间',
        // },
        // {
        //   type: 'select',
        //   key: 'customerPurpose',
        //   label: '客户群用途',
        //   options: [{ type: '用后即评', value: '用后即评' }] // {type:'',value:''}
        // //   getOpts: () => {}
        // }
      ],
      tableColumns: {
        code: '客户群编号',
        name:'客户群名称',
        //  type:{
        //   label:'类型'
        // },
        number:'当前数量',
        // isResult:{
        //   label:'状态'
        // },
        isPush:{
          label:'状态'
        },

        createTime:'创建时间',
        // csmcustomerlableVisibleTxt: {
        //   label:'不满指标',
        //   width:250
        // },
        // phoneNo: '手机号码',

        // isSend: {label:'是否下发短信'},
        // lastContactTime: '最近接触时间',
        // isSlot 配置可以省 操作列通过增加一列进来 不用单独插槽 动态改变列的配置 表格是否有问题测试 格式调整
        // repairStatus: {
        //   label: '修复状态'
        // },
        actions: { label: '操作' }
      },
      uploadingpage: false,
      uploadDialog: false,
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      name: '',
      createUserId: '',
      createUserName: '',
      fileList: [],
      actionUrl: process.env.VUE_APP_BASE_API + '/zb/customer/createCustomerByExcel', // 上传的图片服务器地址
      downUrl: process.env.VUE_APP_BASE_API + '/zb/customer/getTemplate',
    }
  },
  created(){
     // 获取每个指标对应的不满意的值
    getColumScore().then(res => {
      const { code, data } = res
      if (code == 200) {
        this.columNamesPointMap = data
        this.init()
      }
    })
  },
  mounted() {
    // 上传需要用户信息
    let userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      userInfo = JSON.parse(userInfo)
      this.createUserId = userInfo.id
      this.createUserName = userInfo.name
    }
  },
  methods: {
    // sendDialogVis=false
    closeSendDialog(){
      this.queryData(this.cachePrams,'zbGroupTable');
      this.sendDialogVis=false;

    },
    closeDrawer(){
      this.queryData(this.cachePrams,'zbGroupTable');
      this.drawer=false
    },
    send(row){
      this.checkRow = row;
      this.sendDialogVis = true;
    },
    down(row) {
      zbexportDoc({ code: row.code }).then(response => {
        // 兼容ie
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '客群明细.xls'
          )
          return false
        }

        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '客群明细' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    init() {
      const p0 = labelLists()// 获取所有标签tree
      const p1 = getColumNames()// 获取与标签对应的map

      Promise.all([p0, p1]).then(values => {
        const res0 = values[0]
        const res1 = values[1]

        let { code, data } = res0
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
            this.$store.commit('UPDATE_TREE_LABEL_DATA', data)
          }
        }

        if (res1.code == 200) {
          this.columNamesMap = res1.data
          this.$store.commit('UPDATE_COLUMNAMESMAP', res1.data)
        }
      })

      // 获取所有网格1
      getAllGrides().then(res => {
        const { data } = res
        if (Array.isArray(data) && data.length) {
          const arr = this.handlerCascaderData(data, 'cityName', 'cityId')
          this.optionsCascader = arr

          this.conditions[0].options = arr||[]
          this.handlerCascaderDataExpand(arr)
        }
      })
    },
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    // 获取不满意指标map
    getColumNames() {
      getColumNames().then(res => {
        const { code, data } = res
        if (code == 200) this.columNamesMap = data
      })
    },
    // 获取所有标签
    labelLists() {
      labelLists().then(res => {
        let { code, data } = res
        if (code == 200) {
          if (Array.isArray(data)) {
            data = this.handlerCascaderData(data, 'lableName', 'lableId')
            this.treeLabelData = data
          }
        }
      })
    },

    sendMsg() {},
    dele(row) {
        removeById(row.id).then(res => {
        console.log(row)
        const { code, data } = res
        if (code == 200) {
          this.queryData(this.cachePrams,'zbGroupTable');
        }
      })
    },
    testRowClick() {
      // this.$refs.zbGroupTable.getList({ a: 1 })
      // console.log('rowClick....')
    },
    testSortChange() {
      // console.log('sortChange...')
    },
    checkDetail() {
      console.log('check-btn is click...')
    },
    clickCheck(row) {
      this.checkDialogVis = true
      this.checkRow = row
      console.log('row:', row)
    },
    queryData(p, refKey) {

     if(p.gridId && p.gridId.length){
      p.gridId = p.gridId[p.gridId.length-1]
     }
      if(p.beginTime) {
        p.beginTime = `${p.beginTime} 00:00:00`
       }
       if(p.endTime){
        p.endTime = `${p.endTime} 23:59:59`
       }

      zbGroupPage(p).then(res => {
        const { code, data } = res
        const { records, total, size, current } = data
         // 处理不满意指标
          const { columNamesMap = {}, columNamesPointMap = {}} = this
          console.log('columNamesMap=>',columNamesMap)
          console.log('columNamesPointMap=>',columNamesPointMap)

          records.forEach(i => {
            i.csmcustomerlableVisibleTxt = []
            for (const ke in i) {
              if (columNamesMap.hasOwnProperty(ke) && columNamesPointMap.hasOwnProperty(ke) && i[ke] && i[ke] < columNamesPointMap[ke] && i[ke] != -1) {
                let strobj = { txt: '', point: '' }
                strobj = {
                  txt: `${columNamesMap[ke]}(${i[ke]}) `,
                  point: i[ke] }

                i.csmcustomerlableVisibleTxt.push(strobj)
              }
            }
          })



        this.$refs[refKey].tableData = records
        this.$refs[refKey].form.pagination.totalCount = total
        this.$refs[refKey].form.pagination.currentPage = current || 1
        this.$refs[refKey].form.pagination.pageSize = size || 10
      }).finally(() => {
        this.cachePrams = p;
        if(this.$refs[refKey]){
            this.$refs[refKey].tableLoading = false
            this.$refs[refKey].btnLoading = false
        }

      })
    },
    // 处理级联数据 为每一级添加label value
    handlerCascaderData(arr, labelkey, valuekey) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          i.label = i[labelkey]
          i.value = i[valuekey]
          if (i.hasOwnProperty('lastStage')) {
            i.disabled = i.lastStage == 'N'
          }
          if (i.children && i.children.length) {
            this.handlerCascaderData(i.children, labelkey, valuekey)
          } else {
            delete i.children
          }
        })
      }
      return arr
    },
    closeDialog() {
      this.uploadDialog = false;
      this.name = '';
      this.fileList = [];
    },
    beforeFileUpload(file) {
      // 文本导入前判断文件类型
      const msg = file.name.substring(file.name.lastIndexOf('.') + 1)
      console.log('msg:', msg)
      if (!(msg === 'xlsx')) {
        this.$message.error('上传文件只能是 xlsx')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 10M!')
        return false // 必须返回false
      }
      // 客户群名称
      if (!this.name) {
        this.$message.error('输入客户群名称')
        return false
      }
      this.uploadingpage = true
      return true
    },
    upsuccess(response, file, fileList) {
      console.log('response:', response)
      this.uploadDialog = false;
      this.fileList = [];
      this.name = '';
      if (response && response.code == 200) {
        this.$message.success('创建客群成功')
        this.queryData(this.cachePrams,'zbGroupTable');
        this.uploadingpage = false;
      } else {
        this.uploadingpage = false;
        this.$message.error(response.msg)
      }
      this.uploadingpage = false
    },
    uperror(response, file, fileList) {
      console.log(response)
      this.$message.error('创建客群失败')
      this.uploadingpage = false
    },

  }
}
</script>

<style lang="scss" scoped>

.optionbtnbox{
  display: flex;
  >div{
    flex:1;
    // width:33.3%;
    span{
      cursor: pointer;
      &.coloryellow{
        color:#FF9900;
      }
    }
  }
}
// :deep(.el-dialog__header) {
//   display: none;
// }
:deep(.el-dialog__body) {
  padding-top:0;
}
:deep(.el-dialog.is-fullscreen) {
     background:rgb(239,239,239);

}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  font-size:20px;
}
a#spea{
  color:#fff!important;
  background:#00bbcf;
  width:80px;
  text-align: center;
  height:28px;
  border-radius: 3px;
  margin-left: 10px;
  text-decoration:none!important;
  display:inline-block;
  &.inner{
    position: relative;
    left: 100px;
    font-size: 12px;
    top:-52px;
    line-height: 28px;
  }

  }
</style>
