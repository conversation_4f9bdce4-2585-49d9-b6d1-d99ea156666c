<template>
  <div v-loading="loading" class="customergroupCheck" style="position:relative;">
    <el-button icon="el-icon-download" type="primary" class="download-btn" size="small" @click="openDownloadDiaolog" v-if="scene=='调研'">明细下载</el-button>
    <el-button style="position:absolute;right:0;top:-40px" type="default" size="small" @click="$emit('close')">返回</el-button>
    <!-- 测试客群 -->
    <div style="margin-top:30px;">
      <div class="coma" style="background:white;">
        <div class="comaitem comaLeft">
          <div class="cusName">
            <div class="flabel" style="font-size:20px;">{{ customergroupInfo.name?customergroupInfo.name:'--' }}</div>
            <div><span>创建时间：</span>{{ customergroupInfo.createTime?customergroupInfo.createTime:'--' }}</div>
          </div>
          <div style="margin-top:10px" v-if="scene!=='调研'">
            <div>
              <div class="viewflex">
                <span class="view"><span class="viewflexLabel">本客群覆盖客户数：</span>{{ customergroupInfo.number?customergroupInfo.number:'--' }}人</span>
              </div>
              <div class="viewflex">
                 <span class="view"><span class="viewflexLabel">潜在不满意客户数：</span>{{ customergroupInfo.unsatisfiedReasonCount?customergroupInfo.unsatisfiedReasonCount:'--' }}人</span>
                 <span class="view"><span class="viewflexLabel">潜在不满意客户占比：</span>{{ customergroupInfo.unsatisfiedReasonCountPercent?customergroupInfo.unsatisfiedReasonCountPercent:'--' }}</span>
              </div>
              <div class="viewflex">
                 <span class="view"><span class="viewflexLabel">客户不满意率：</span> 9.43%</span>
                 <span class="view"><span class="viewflexLabel">客户不满意占比：</span>0.17%</span>
                 <span class="view"><span class="viewflexLabel">客群排名：</span>12</span>
              </div>
            </div>

          </div>
          <div style="margin-top:15px;" v-if="scene!=='调研'">
            <div class="flabel">推送信息</div>
            <div class="viewflex">
              <span class="view"><span class="viewflexLabel">开始关怀时间：</span>{{ customergroupInfo.startTime?customergroupInfo.startTime:'--' }}</span>
              <span class="view"><span class="viewflexLabel">关怀结束时间：</span>{{ customergroupInfo.endTime?customergroupInfo.endTime:'--' }}</span>
            </div>
            <div class="viewflex">
              <span class="view"><span class="viewflexLabel">关怀频率：</span>{{ customergroupInfo.frequency?customergroupInfo.frequency:'--' }}</span>
              <span class="view"><span class="viewflexLabel">关怀周期：</span>{{ customergroupInfo.cycle?customergroupInfo.cycle:'--' }}</span>

            </div>
            <div class="viewflex">
              <span class="view"><span class="viewflexLabel">策略方式：</span>{{ customergroupInfo.strategy?customergroupInfo.strategy:'--' }}</span>
              <span class="view"><span class="viewflexLabel">渠道：</span>{{ customergroupInfo.channel?customergroupInfo.channel:'--' }}</span>
            </div>
          </div>
          <div style="margin-top:15px;" v-if="scene!=='调研'">
            <div class="flabel">客群公共画像</div>
            <div>
              <el-tag v-for="i in labelList" :key="i" class="taginfo" type="info">{{ i }}</el-tag>

            </div>
          </div>
          <div style="margin-top: 10px;" v-if="scene==='调研'">
            <div>
              <div class="viewflex">
                <span class="view"><span class="viewflexLabel">下发问卷总量：</span>{{ map.total?map.total:'--' }}</span>
              </div>
              <div class="viewflex">
                  <span class="view"><span class="viewflexLabel">参与量：</span>{{ map.amount?map.amount:'--' }}</span>
                  <span class="view"><span class="viewflexLabel">参与率：</span>{{ map.rate?map.rate:'--' }}</span>
              </div>
              <div class="viewflex">
                <span class="view"><span class="viewflexLabel">问卷下发时间：</span>{{ map.time?map.time:'--' }}</span>
              </div>
            </div>
            <div class="questionnaire-table">
              <div class="questionnaire-item" v-if="tableData.length">
                <div class="title"></div>
                <div class="num">答题数（人）</div>
                <div class="num">平均分</div>
              </div>
              <div class="questionnaire-item" v-for="item in tableData" :key="item.index">
                <div class="title">第{{ item.titleNo }}题</div>
                <div class="num">{{ item.num==null||item.num==undefined?0:item.num }}</div>
                <div class="num">{{ !item.avg||item.avg==null||item.avg==undefined?'--':item.avg }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="comaitem comaRight">
          <div class="flabel">{{scene=='调研'?'地市分布':'营业厅业务受理量趋势'}}</div>
          <div style="height:200px">
            <LineChart
              v-if="!LineChartLoading"
              :all-data="LineChartData"
              :legend-data="[scene=='调研'?'答题用户量':'用户量']"
              :keymap="LineChartKeyMap"
            />
          </div>
        </div>
      </div>
      <div v-if="scene!='调研'" class="comb" style="background:white;margin-top:20px">
        <div class="resultTypes">
          <div>
            <div :class="{active: activeType==1,imgbox:true}" @click="()=>resultTypeChange(1)">
              <img src="../../../../../assets/images/customer/7.png" alt="">
            </div>
            <br><span :class="{accolor: activeType==1}">客群分析</span>
          </div>
          <div>
            <div :class="{active: activeType==2,imgbox:true}" @click="()=>resultTypeChange(2)">
              <img src="../../../../../assets/images/customer/7.png" alt="">
            </div>
            <br><span :class="{accolor: activeType==2}">调研分析</span>
          </div>
          <div>
            <div :class="{active: activeType==3,imgbox:true}" @click="()=>resultTypeChange(3)">
              <img src="../../../../../assets/images/customer/7.png" alt="">
            </div>
            <br><span :class="{accolor: activeType==3}">修复分析</span>
          </div>
        </div>
        <div v-loading="pieLoading" class="piesbox">
          <div v-for="(i,j) in pieResultArr" :key="j" class="pieitem">
            <div v-if="i.pieData.length">
              <div style="font-weight:bold;padding:10px 10px;color:black">{{ i.title }}</div>
              <div style="height:280px;position:relative">
                <PieView :data="i.pieData" :option="i.pieOption" />
              </div>
            </div>
            <div v-else style="height:300px;position:relative">
              <div style="position:absolute;left:0px;top:0px;font-weight:bold;padding:10px 10px;color:black">{{ i.title }}</div>
              <Blank2 />
            </div>

          </div>
        </div>
        <div v-if="activeType==2" style="height:430px;margin-top:30px;padding:20px;">
          <div class="flabel">
            需上门用户数情况
          </div>

          <GridChart :data-map="barEchartData" v-if="barEchartData.xData.length"/>
          <div v-else style="height:300px;position:relative">
              <Blank2 />
            </div>
        </div>
      </div>
      <!-- 选择问卷的时候的分析页面 -->
      <div v-if="scene=='调研'"  class="comb" style="background:white;margin-top:20px">
        <Any :questionId="questionId" :code="checkRow.code"/>
      </div>

    </div>
    <detail-download-dialog ref="detailDownloadDialog"></detail-download-dialog>
  </div>
</template>

<script>
import LineChart from './line.vue'
import PieView from './pieview.vue'
import GridChart from './grid.vue'
import Any from './any.vue'
import detailDownloadDialog from './detailDownloadDialog.vue'
import { listCustomerHistogramByCode, listCustomerCountByType, getQuestionIdByCode,getCustomerByCode,queryCustomType,getCustomerByCodeV1 } from '@/api/customer/index'

export default {
  components: { LineChart, PieView, GridChart,Any,detailDownloadDialog },
  props: {
    checkRow: {
      required: false,
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      questionId:'',
      scene:'',//调研渠道
      // 客群基本信息
      customergroupInfo: {},
      loading: false,
      pieLoading: false,
      LineChartLoading: false,
      LineChartData: [
        // { label: '银川', value: '89' },

      ],

      channelMap: {
        emos: 'EMOS',
        zw: '装维',
        online: '在线',
        phone: '电渠',
        grid: '网络'
      },

      barEchartData: {
        xData: [],
        dataList: [],
        typeNames: ['家宽需上门用户数', '网络需上门用户数']
      },
      LineChartKeyMap: {
        xData: 'label',
        seriesData: ['value']
      },
      labelList: [],
      activeType: 1,
      pieData: [], // { name: 'x', value: '10', partrate: '20' }
      pieResultArr: [
        { title: '客群类别构成', pieOption: { title: { text: '客户' }}, pieData: [
          // { name: 'x', value: '10', partrate: '20' },
          // { name: 'y', value: '20', partrate: '10' }
        ] }, // 左边度饼图
        { title: '上报年份重点客群构成', pieData: [], pieOption: { title: { text: '客户' }}}, // 中间的度饼图
        { title: '上报业务重点客群构成', pieData: [], pieOption: { title: { text: '客户' }}} // 右边边度饼图
      
      ],
      allPieData: [],
      tableData:[],
      map:{}
    }
  },
  mounted() {
    this.queryCustomerGroupInfo()
    // 获取饼图信息
    this.getPiesData()
    // 获取柱状图
    this.getbardata()
  },
  methods: {
    // 根据客群code获取问卷code
    async getQuestionIdByCode(code){
      let res = await getQuestionIdByCode({code});
      if(res && res.data ){
        this.questionId = res.data
      }
     
    },
    // 获取调研分析柱状图数据
    getbardata() {
      const p = { code: this.checkRow.code }

      listCustomerHistogramByCode(p).then(res => {
        const { code, data } = res
        let xData = []
        const dataList = []
        if (code == 200 && data && Array.isArray(data)) {
          const isHome1CountDatas = []
          const isHome2CountDatas = []

          xData = data.map(i => {
            const { histogramData } = i
            const { isHome1Count, isHome2Count } = histogramData
            isHome1CountDatas.push(isHome1Count)
            isHome2CountDatas.push(isHome2Count)
            return i.cityName
          })
          dataList.push(isHome1CountDatas, isHome2CountDatas)
        }
        this.barEchartData.xData = xData
        this.barEchartData.dataList = dataList || []
      }).finally(() => {

      })
    },
    // 获取饼图信息
    getPiesData() {
      const { activeType } = this
      const p = { code: this.checkRow.code, type: activeType }
      this.pieLoading = true
      listCustomerCountByType(Object.assign(p)).then(res => {
        const { code, data } = res
        let pieResultArr = []
        if (code == 200 && Array.isArray(data)) {
          data.forEach((i, idx) => {
            const { name, dataList } = i
            const count = this.arrCount(dataList)
            dataList.forEach(x => {
              x.name = x.label
              x.partrate = (x.value / count * 100).toFixed(2)
            })

            

            pieResultArr[idx] = {};
            pieResultArr[idx].title = name
            pieResultArr[idx].pieData = dataList

            this.pieResultArr = pieResultArr

          })

          

        }
      }).finally(() => {
        this.pieLoading = false
      })
    },
    async queryCustomType(){
      try{
        let {code,data}=await queryCustomType(this.checkRow.code)
        if(code==200){
          this.scene=data?'调研':''
        }else{
          this.scene=''
        }
      }catch(e){  
      }
    },
    // 获取客群信息
    queryCustomerGroupInfo() {
      const p = { code: this.checkRow.code }
      this.loading = true
      Promise.all([this.queryCustomType()]).then(() => {
        if(this.scene=='调研'){
          getCustomerByCodeV1(this.checkRow.code).then((res)=>{
            const { data, code } = res
            if (code == 200) {
              this.tableData=data.questionlist||[]
              this.map=JSON.parse(JSON.stringify(data.map))||{}
              if (data && data.channel) {
                let tempstr = ''
                let arr = data.channel.split(',');
                arr.forEach(i=>{
                  i = i.trim();
                  tempstr += `${this.channelMap[i]} `
                })
                data.channel = tempstr;
              }
              if(data.unsatisfiedReasonCount === 0 || data.unsatisfiedReasonCount === '0'){
                data.unsatisfiedReasonCountPercent = '0%'
              }else if(data.unsatisfiedReasonCount){
                let p = Number(data.unsatisfiedReasonCount) / (data.number) 
                if(p){
                  p = p * 100;
                  p = p.toFixed(2)
                  data.unsatisfiedReasonCountPercent =`${p}%` 
                }
              }
              this.getQuestionIdByCode(this.checkRow.code);
              this.customergroupInfo = data || {}
              this.LineChartData = data.dataList || []
              this.labelList = data.labelList || []
            }
          }).finally(() => {
            this.loading = false
          })
        }else{
          getCustomerByCode(p).then(res => {
            const { data, code } = res
            if (code == 200) {
              if (data && data.channel) {
                let tempstr = ''
                let arr = data.channel.split(',');
                arr.forEach(i=>{
                  i = i.trim();
                  tempstr += `${this.channelMap[i]} `
                })
                data.channel = tempstr;
              }
              if(data.unsatisfiedReasonCount === 0 || data.unsatisfiedReasonCount === '0'){
                data.unsatisfiedReasonCountPercent = '0%'
              }else if(data.unsatisfiedReasonCount){
                let p = Number(data.unsatisfiedReasonCount) / (data.number) 
                if(p){
                  p = p * 100;
                  p = p.toFixed(2)
                  data.unsatisfiedReasonCountPercent =`${p}%` 
                }
              }
              this.customergroupInfo = data || {}
              this.LineChartData = data.dataList || []
              this.labelList = data.labelList || []
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    arrCount(arr) {
      let count = 0
      arr.forEach(item => {
        count = count + Number(item.value)
      })
      return count
    },

    resultTypeChange(v) {
      this.activeType = v
      this.getPiesData()
    },
    openDownloadDiaolog(){
      this.$refs.detailDownloadDialog.open(this.customergroupInfo,this.checkRow.code)
    },
  }
}
</script>

<style lang="scss" scoped>
.viewflexLabel{
  display: inline-block;
  text-align: left;
  // width:140px;
}
.viewflex{
  display: flex;
  justify-content: flex-start;
  margin-bottom: 5px;
  span{
    flex:1;
  }
}
.customergroupCheck{
    height: 90vh;
    .download-btn{
      position:absolute;
      right:70px;
      top:-40px;
    }
}
.coma{
    min-height:200px;
    display: flex;
    padding:20px 30px;
    .comaitem{
        width:50%;
        color: #999;
        &.comaLeft{
            border-right:1px solid #e6e6e6;
            padding-right:30px;
        }

    }
    .comaRight{
      padding-left:30px;
    }
}
.flabel{
    color:#262626;
    font-weight: bold;
}
.cusName{
    display: flex;
    justify-content: space-between;
}
.taginfo{
  margin-right:20px;
  margin-top:20px;
}
.view{
  margin-right:20px;
  // margin-left:10px;
}
.resultTypes{
  padding:20px 0 10px 0;
  display: flex;
  justify-content: center;
  >div{
    width:28%;
    text-align: center;

  }
}
.imgbox{
  margin-bottom: 10px;
  display: inline-block;
  border: 1px solid rgba(111,111,111,0);
  cursor: pointer;
  img{
    width:80px;
  }
}
.active{
  background:rgba(254,234,203,0.8);
  border:1px dashed rgba(253,140,9,0.8);
  border-radius: 5px;

}
.accolor{
color:rgb(253,140,9);
}
.piesbox{
  display:flex;
  margin-top: 50px;
   border-top:1px dashed #e6e6e6;
   
   flex-wrap:  wrap;

  >div{
    width:33.3%;
    padding-left:10px;
    border-bottom:1px dashed #e6e6e6 ;
    &:nth-child(2n){
      border-right:1px dashed #e6e6e6;
      border-left:1px dashed #e6e6e6;
    }

  }
}
.questionnaire-table{
  margin-top: 20px;
  width: 100%;
  height: 135px;
  // border-top: 1px solid #E5E5E5;
  border-radius: 5px;
  display: flex;
  overflow-x: auto;
  .questionnaire-item{
    width: 150px;
    flex-shrink: 0;
    height: 117px;
    border-top: 1px solid #E5E5E5;
    .title{
      height: 35px;
      line-height: 35px;
      border-bottom: 1px solid #E5E5E5;
      text-align: center;
      background: #F9F9F9;
    }
    .num{
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #E5E5E5;
      text-align: center;
    }
  }
  .questionnaire-item:first-child{
    border-left: 1px solid #E5E5E5;
    border-bottom-left-radius: 5px;
  }
  .questionnaire-item:last-child{
    border-right: 1px solid #E5E5E5;
    border-bottom-right-radius: 5px;
  }
}
</style>
