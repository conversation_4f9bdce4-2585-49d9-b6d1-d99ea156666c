<template>
  <el-dialog title="明细下载" :visible.sync="dialogVisible" width="35%" append-to-body class="detail-dialog">
    <el-form ref="form" :model="form" label-width="120px" size="small" label-suffix="：" :rules="rules">
      <el-form-item label="数据时间" required prop="dateRange">
        <el-date-picker v-model="form.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd" :clearable="false">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="明细下载文件">
        <el-input v-model="form.fileName" readonly></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="exportFile" :loading="btnLoading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { exportDetailExcel } from '@/api/customer/index'
export default {
  data() {
    return {
      dialogVisible: false,
      form: {
        dateRange: '',
        fileName: ''
      },
      pickerOptions: this.beginTime(),
      time: '',
      code: '',
      rules: {
        dateRange: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
      },
      btnLoading: false
    };
  },
  methods: {
    open(info, code) {
      this.time = info.map.time || info.createTime
      this.code = code
      this.form.fileName = info.name + '-明细.xlsx'
      setTimeout(() => {
        let startDate = this.changeTime(new Date(this.time).getTime())
        let endDate = this.changeTime(new Date().getTime())
        this.form["dateRange"] = [startDate, endDate]
        this.dialogVisible = true
      }, 100)
    },
    changeTime(value) {
      let nowY = new Date(value).getFullYear();
      let nowM = new Date(value).getMonth() + 1;
      let nowD = new Date(value).getDate();
      let date =
        nowY +
        "-" +
        (nowM < 10 ? "0" + nowM : nowM) +
        "-" +
        (nowD < 10 ? "0" + nowD : nowD); //当前日期
      return date;
    },
    beginTime() {
      const self = this
      return {
        disabledDate(time) {
          return new Date(self.time).getTime() - 24 * 3600 * 1000 > time.getTime()
        }
      }
    },
    exportFile() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            startTime: this.form.dateRange[0],
            endTime: this.form.dateRange[1],
            code: this.code
          }
          this.btnLoading = true
          exportDetailExcel(params).then(response => {
            // 兼容ie
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
              window.navigator.msSaveOrOpenBlob(
                response,
                this.form.fileName
              )
              return false
            }
            const url = URL.createObjectURL(response)
            const aLink = document.createElement('a')
            aLink.href = url
            aLink.setAttribute('download', this.form.fileName)
            document.body.appendChild(aLink)
            aLink.click()
            document.body.removeChild(aLink)
          }).finally(() => {
            this.btnLoading = false
            this.dialogVisible = false
          })
        } else {
          this.$message.error('请选择日期')
          return false;
        }
      });

    },
  }
}
</script>

<style lang="scss" scoped>
.detail-dialog {
  border: 1px solid #7E7E7E;
  border-radius: 4px;

  :deep( .el-dialog__header ) {
    border-bottom: 1px solid #D8D8D8;

    .el-dialog__title {
      color: #848585;
    }
  }

  :deep( .el-dialog__headerbtn .el-dialog__close ) {
    font-weight: 700;
  }

  :deep( .el-dialog__footer ) {
    border-top: 1px solid #D8D8D8;
    text-align: center;

    .dialog-footer {
      .el-button {
        width: 25%;
      }
    }
  }

  :deep( .el-dialog__body ) {
    .el-form-item__label {
      color: #000;
      font-weight: 500;
    }

    .el-input {
      width: 350px;
    }
  }
}
</style>
