<template>
  <div class="DefaultPage">
    <div class="topDate">
      数据日期:
      <el-date-picker
        v-model="activeTimeSolt"
        size="mini"
        :editable="false"
        :clearable="false"
        type="daterange"
        range-separator="至"
        :picker-options="pickerOptions"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="this.analy"
      />
    </div>
    <div class="title">
      <img class="imgicon" src="../../../assets/img/icon0.png">
      显示设置
      <span class="collapse" @click="isConditionOpen = !isConditionOpen">
        {{ isConditionOpen ? "收起" : "展开" }}
        <el-icon :name="isConditionOpen ? 'arrow-up' : 'arrow-down'" />
      </span>
      <el-button
        v-if="false"
        class="downbtn"
        type="primary"
        round
        icon="el-icon-download"
        :loading="downloadLoading"
        @click="downLoad('DefaultPage')"
      >下载报告</el-button>
    </div>
    <div v-if="isConditionOpen" class="conditionArea">
      <el-checkbox-group v-model="checkList" data-lable="数据表格:">
        <el-checkbox label="显示表格" />
        <el-checkbox label="表格条图形" />
      </el-checkbox-group>
      <el-radio-group v-model="echartType" data-lable="其它图表:">
        <el-radio label="pie">饼状</el-radio>
        <!-- <el-radio label="circle">圆环</el-radio>
        <el-radio label="bar">柱状</el-radio>
        <el-radio label="line">折线</el-radio> -->
      </el-radio-group>
      <!-- <el-checkbox-group v-model="checkList" data-lable="隐藏选项:">
        <el-checkbox label="隐藏空选项" />
        <el-checkbox label="隐藏跳过项" />
      </el-checkbox-group> -->
    </div>
    <div class="title">
      <img class="imgicon" src="../../../assets/img/icon2.png">
      问卷题目
    </div>
    <div v-loading="loading" class="questiArea">
      <div
        v-for="(item, index) in questionList"
        :key="index"
        class="questionCell"
      >
        <div class="title">
          第{{ index + 1 }}题：{{ item.title }}
          <span>{{ item.name }}</span>
        </div>
        <el-row v-if="item.data.length" :gutter="20" center>
          <el-col :span="12">
            <el-table
              v-if="checkList.indexOf('显示表格') !== -1"
              class="table"
              :data="item.data"
              border
              :show-summary="false"
              sum-text="本题有效填写人次"
              :summary-method="sumMethod"
              :header-cell-style="{ backgroundColor: '#F4FAFF' }"
            >
              <el-table-column prop="name" label="选项" />
              <el-table-column
                prop="count"
                width="100"
                label="小计"
              />
              <el-table-column prop="percent" label="比例">
                <template slot-scope="data">
                  <el-progress
                    v-if="checkList.indexOf('表格条图形') !== -1"
                    :percentage="data.row.percent"
                  />
                  <div v-else>{{ data.row.percent }}%</div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="12" class="echarCol">
            <div class="ctlArea">
              <el-button
                size="small"
                icon="el-icon-pie"
                :type="item.echartType === 'pie' ? 'primary' : ''"
                @click="item.echartType = 'pie'"
              >饼状</el-button>
              <el-button
                size="small"
                icon="el-icon-circle"
                :type="item.echartType === 'circle' ? 'primary' : ''"
                @click="item.echartType = 'circle'"
              >环形</el-button>
              <el-button
                size="small"
                icon="el-icon-bar"
                :type="item.echartType === 'bar' ? 'primary' : ''"
                @click="item.echartType = 'bar'"
              >柱状</el-button>
              <el-button
                size="small"
                icon="el-icon-line"
                :type="item.echartType === 'line' ? 'primary' : ''"
                @click="item.echartType = 'line'"
              >折线</el-button>
            </div>
            <div class="echartArea" style="height:300px">
              <Pie v-if="item.echartType === 'pie'" :data="item.data" />
              <Circle1
                v-else-if="item.echartType === 'circle'"
                :data="item.data"
              />
              <Bar v-else-if="item.echartType === 'bar'" :data="item.data" />
              <BarLine
                v-else-if="item.echartType === 'line'"
                :data="item.data"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- <Download :base64="base64" :show.sync="showDonload" :fileName="fileName" /> -->
  </div>
</template>
<script>
import { queryQuestionnaireInfo, queryAnalysisQuestionFault } from '@/api/customer/index'

import Vue from 'vue'
import {
  Tabs,
  TabPane,
  Icon,
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Button,
  Row,
  Col,
  Table,
  TableColumn,
  Progress
} from 'element-ui'
import Pie from '../anyCom/Pie.vue'
import Circle1 from '../anyCom/Circle.vue'
import Bar from '../anyCom/Bar1.vue'
import BarLine from '../anyCom/Line.vue'
import html2canvas from 'html2canvas'
// import Download from "../anyCom/Download.vue";
import jsPDF from 'jspdf'

Vue.use(Tabs)
  .use(TabPane)
  .use(Checkbox)
  .use(CheckboxGroup)
  .use(Radio)
  .use(RadioGroup)
  .use(Button)
  .use(Row)
  .use(Col)
  .use(Table)
  .use(TableColumn)
  .use(Progress)
  .use(Icon)
export default {
  name: 'Default',
  components: {
    Pie,
    Circle1,
    Bar,
    BarLine
    // Download,
  },
  props: {
    questionId: {
      require: true,
      default: () => ''
    },
    code: {
      require: true,
      default: () => ''
    }
  },
  data() {
    return {
      loading: false,
      checkList: ['显示表格'],
      isConditionOpen: true,
      echartType: 'pie',
      questionList: [],
      base64: '',
      showDonload: false,
      fileName: '',
      allcount: 10,
      questionTest: [],
      downloadLoading: false,
      titlesArr: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      },
      activeTimeSolt: [
        new Date(this.$moment(new Date()).subtract(30, 'day')),
        new Date()
      ],
      analysIndex: '',
      nameObj: '',
      // *****
      questionnaireId: '',
      scenceId: ''
    }
  },
  watch: {
    echartType(val) {
      this.questionList.filter((it) => {
        it.echartType = val
        return it
      })
    },
    questionId(v, oldv) {
      this.questionnaireId = v
      this.questionnaireId = v
      // this.scenceId = this.$route.params.id;
      this.loading = true
      queryQuestionnaireInfo({ id: v }).then((res) => {
        this.loading = false

        const questionTest = []
        if (res.code == 0 && res.data) {
          JSON.parse(res.data.questionJson).forEach((it, i) => {
            it.index = i + 1
            if (
              it.type == 'radio' ||
              it.type == 'rate' ||
              it.type == 'comment' ||
              it.type == 'checkbox'
            ) {
              questionTest.push(it)
            // 测试
            // if(i<2){
            // questionTest.push(it);
            // }
            }
          })
          this.questionTest = questionTest

          this.analy()
          // 测试通过后把queryAnalysisQuestionFault 函数都删了 没有用都
          // this.questionTest.forEach((it,i) => {
          //   this.analysIndex = it.index;
          //   this.nameObj = {name:it.name,title:it.title};
          //   this.queryAnalysisQuestionFault();
          //   // this.queryAnalysisQuestionFault(it.index,{name:it.name,title:it.title});
          //   // this.queryAnalysisQuestionFault(i + 1,{name:it.name,title:it.title});
          // });
        }
      })
    }
  },

  mounted() {

  },
  methods: {

    // 分析问题
    analy() {
      if (Array.isArray(this.questionTest) && this.questionTest.length) {
        const pAll = []
        const questionList = []
        this.questionTest.forEach((it, i) => {
          // this.analysIndex = it.index;
          // this.nameObj = {name:it.name,title:it.title};
          const params = {
            code: this.code,
            questionnaireId: this.questionnaireId,
            scenceId: this.scenceId,
            titleNo: it.index,
            startTime: this.$moment(this.activeTimeSolt[0]).format('YYYY-MM-DD'),
            endTime: this.$moment(this.activeTimeSolt[1]).format('YYYY-MM-DD')
          }

          const p = queryAnalysisQuestionFault(params)
          pAll.push(p)
        })

        Promise.all(pAll).then((res) => {
          res.forEach((re, idx) => {
            const tempItem = {
              echartType: this.echartType,
              title: this.questionTest[idx].title,
              name: this.questionTest[idx].name
            }

            const tableList = []
            Object.keys(re.data.restCsmAnalysisQuestionDefaults).forEach((key, idx) => {
              const tempRowData = re.data.restCsmAnalysisQuestionDefaults[key]
              const tempRow = {
                name: tempRowData.columnVal,
                count: tempRowData.pickNum,
                percent: parseFloat(tempRowData.rate, 10),
                total: re.data.total
              }

              // const tempRow = {
              //   name: tempRowData.columnVal,
              //   count: idx == 2 ? 7 : idx + 1,
              //   percent: parseFloat(idx == 2 ? 7 * 10 : (idx + 1) * 10, 10),
              //   total: 10
              // }

              tableList.push(tempRow)
            })
            tempItem.data = tableList
            questionList.push(tempItem)
            // console.log('questionList=>',  questionList)
          })
          // this.questionList = questionList
        }).finally(() => {
          this.questionList = questionList
        })

        // this.questionList = questionList;
      }
    },

    // 获取问题分析
    // queryAnalysisQuestionFault(it) {
    // // queryAnalysisQuestionFault(index,nameObj) {
    //   const obj = {
    //     questionnaireId:this.$route.params.questionnaireId,
    //     scenceId:this.$route.params.id,
    //     titleNo:it.analysIndex,
    //     endTime: '',
    //     startTime: '',
    //   };
    //   obj.startTime = this.$moment(this.activeTimeSolt[0]).format('YYYY-MM-DD');
    //   obj.endTime = this.$moment(this.activeTimeSolt[1]).format('YYYY-MM-DD');
    //   queryAnalysisQuestionFault(obj).then((res) => {
    //     // this.allcount = res.data.total;
    //     // res = {"code":"0","data":{"restCsmAnalysisQuestionDefaults":{"满意":{"columnVal":"满意","columnName":null,"pickNum":2,"rate":"50.00"},"不满意":{"columnVal":"不满意","columnName":null,"pickNum":2,"rate":"50.00"}},"total":4},"msg":"成功"}
    //     this.packageData(res.data.restCsmAnalysisQuestionDefaults,this.nameObj,res.data.total);
    //   });
    // },
    // packageData(data,nameObj,total) {
    //   let index = 0;
    //   const tableList = [];
    //   Object.keys(data).forEach((key) => {
    //     const tempRowData = data[key];
    //     index++;
    //     const tempRow = {
    //       name: tempRowData.columnVal,
    //       count: tempRowData.pickNum,
    //       percent: parseFloat(tempRowData.rate, 10),
    //       total
    //     };
    //     tableList.push(tempRow);
    //   });

    //   this.questionList.push({data:tableList,name:nameObj.name,title:nameObj.title, echartType: this.echartType});

    // },
    downLoad(cls) {
      const scale = 2
      html2canvas(document.getElementsByClassName(cls)[0], {
        allowTaint: true, // Whether to allow cross-origin images to taint the canvas
        scale // The scale to use for rendering. Defaults to the browsers device pixel ratio.
      }).then((canvas) => {
        // const contentWidth = canvas.width / scale
        // const contentHeight = canvas.height / scale
        // const pdf = new jsPDF('', 'pt', [contentWidth, contentHeight])
        // const pageData = canvas.toDataURL('image/jpeg', 1.0)
        // pdf.addImage(pageData, 'JPEG', 0, 0, contentWidth, contentHeight)
        const contentWidth = canvas.width
        const contentHeight = canvas.height
        const pageHeight = contentWidth / 592.28 * 841.89
        // 未生成pdf的html页面高度
        let leftHeight = contentHeight
        // 页面偏移
        let position = 0
        // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
        const imgWidth = 595.28
        const imgHeight = 592.28 / contentWidth * contentHeight

        const pageData = canvas.toDataURL('image/jpeg', 1.0)

        const pdf = new jsPDF('', 'pt', 'a4')

        // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
        // 当内容未超过pdf一页显示的范围，无需分页
        if (leftHeight < pageHeight) {
          pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
        } else {
          while (leftHeight > 0) {
            pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
            leftHeight -= pageHeight
            position -= 841.89
            // 避免添加空白页
            if (leftHeight > 0) {
              pdf.addPage()
            }
          }
        }
        pdf.save('评估报告.pdf')
      })
    },
    sumMethod({ columns, data }) {
      const sum = data[0].total
      // const tempPercent = (sum / this.allcount) * 100;
      // let percent = `${((sum / this.allcount) * 100).toFixed(2)}%`;
      // if (parseInt(tempPercent, 10) === tempPercent) {
      //   percent = `${tempPercent}%`;
      // }

      return ['本题有效填写人次', sum, '']
    },

    changeTimeSlot(v) {
      const timeArr = []
      v.forEach((it, i) => {
        timeArr.push(this.$moment(it).format('YYYY-MM-DD'))
      })
      this.activeTimeSolt = timeArr
      this.queryAnalysisQuestionFault()
    }
  }
}
</script>

<style lang="less">
.conditionArea {
  .el-checkbox,
  .el-radio {
    width: 90px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #4be27c;
    background: #4be27c;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label,
  .el-radio__input.is-checked + .el-radio__label {
    color: #575c61;
  }
  .el-checkbox__inner:hover,
  .el-radio__inner:hover {
    border-color: #4be27c;
  }
}
.questiArea .el-progress-bar {
  margin-right: -60px;
  padding-right: 60px;
}
</style>
<style lang="less" scoped>
.DefaultPage {
  background-color: white;
  padding: 38px;
  text-align: left;
  font-size: 14px;
  .topDate{
    padding-bottom: 20px;
  }
  .collapse {
    margin-left: 15px;
    font-size: 12px;
    color: #2da1fa;
    cursor: pointer;
    i {
      font-weight: bold;
    }
  }
  .title {
    margin-bottom: 10px;
    position: relative;
    .downbtn {
      position: absolute;
      z-index: 2;
      right: 0;
      bottom: 0;
      border: none;
      vertical-align: top;
      padding: 10px 20px;
      background-color: #3a97f6;
      background-image: linear-gradient(-90deg, #3a97f6 0%, #69e1ff 100%);
      box-shadow: 0 3px 6px 0 #ddeaf1;
    }
  }
  .imgicon {
    width: 16px;
    margin-right: 3px;
    vertical-align: middle;
  }
  .conditionArea {
    font-size: 12px;
    background: #f5f8fa;
    padding: 16px;
    margin-bottom: 20px;
    .el-radio-group,
    .el-checkbox-group {
      position: relative;
      padding-left: 70px;
      margin-bottom: 10px;
      &::before {
        position: absolute;
        content: attr(data-lable);
        color: #8d949d;
        font-size: 12px;
        left: 0;
        top: 0;
        width: 70px;
        height: 20px;
        line-height: 20px;
      }
    }
    .el-checkbox,
    .el-radio {
      width: 90px;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-radio__input.is-checked .el-radio__inner {
      border-color: #4be27c;
      background: #4be27c;
    }
    .el-checkbox__input.is-checked + .el-checkbox__label,
    .el-radio__input.is-checked + .el-radio__label {
      color: #575c61;
    }
    .el-checkbox__inner:hover,
    .el-radio__inner:hover {
      border-color: #4be27c;
    }
  }
  .questiArea {
    margin-top: 20px;
    .title {
      margin-bottom: 20px;
    }
    .questionCell {
      margin-bottom: 30px;
      .table {
        font-size: 12px;
      }
      .el-table__footer-wrapper tbody td {
        background-color: #f4faff;
      }
      .cell {
        text-align: center;
      }
      .echarCol {
        height: 100%;
        min-height: 200px;
        .ctlArea {
          .el-button {
            background-color: #eaeef0;
            border: none;
            color: #969da2;
            i {
              font-size: 18px;
              line-height: 12px;
              vertical-align: top;
            }
            &.el-button--primary {
              background-color: #3ab0ff;
              color: white;
            }
          }
        }
      }
      .echartArea {
        position: absolute;
        height: 100%;
        width: 50%;
        margin-top: 10px;
      }
    }
  }
}
</style>
