<template>
  <div v-loading="echartLoading" class="ana-chart" />
</template>

<script>
export default {
  name: 'GridEchart',
  props: {
    dataMap: {
      type: Object,
      default: () => ({
        xData: [], // 横坐标
        dataList: [], // 数据
        typeNames: []
      })
    }
  },
  data() {
    return {
      echartLoading: false
    }
  },
  watch: {
    dataMap: {
      deep: true,
      handler() {
        this.setOption()
      }

    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el)
      this.setOption()
    },
    setOption() {
      const xData = this.dataMap.xData || []
      var dataName = this.dataMap.typeNames || []
      const zoomShow = xData.length > 9
      const dataList = this.dataMap.dataList || []
      const colorList = ['#FF8000', '#FEFF00', '#00EA86', '#0083FF']
      const seriousList = []
      dataName.map((item, index) => {
        seriousList.push({
          name: dataName[index],
          label: {
            show: false
          },
          color: colorList[index],
          itemStyle: {
            barBorderRadius: [2, 2, 0, 0]
          },
          type: 'bar',
          barWidth: '12', // 柱型宽度
          data: dataList[index]
        })
      })

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          textStyle: {
            fontSize: 14
          }
        },
        dataZoom: [{
          show: zoomShow, // flase直接隐藏图形
          type: 'slider',
          backgroundColor: 'transparent',
          brushSelect: false, // 是否开启刷选功能
          zoomLock: false, // 是否锁定选择区域大小
          height: 7,
          // left: 'center', //滚动条靠左侧的百分比
          bottom: 0,
          startValue: 0, // 滚动条的起始位置
          endValue: 8, // 滚动条的截止位置（按比例分割你的柱状图x轴长度）
          handleStyle: {
            color: '#E8E8E8',
            borderColor: '#E8E8E8'
          },
          fillerColor: '#E8E8E8',
          borderColor: 'transparent',
          showDetail: false,
          dataBackground: {
            areaStyle: {
              opacity: 0
            },
            lineStyle: {
              color: 'transparent'
            }
          }
        }],
        legend: {
          data: dataName,
          right: 'center',
          top: '25',
          // icon: "circle",
          itemWidth: 15, // 设置宽度
          itemHeight: 15, // 设置高度
          itemGap: 32,
          textStyle: {
            color: 'rgba(0, 0, 0, 0.85)',
            fontFamily: 'Source Han Sans CN-Regular',
            fontSize: 14,
            padding: [0, 0, 0, 5]
          }
        },
        grid: {
          left: '24',
          right: '24',
          bottom: zoomShow ? '50' : '22', // 下边距,
          top: '78',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#cccccc'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.85)',
            textStyle: {
              fontSize: 14,
              fontFamily: 'Source Han Sans CN-Regular'
            }
          },
          data: xData
        }],
        yAxis: [{
          name: '单位：用户数',
          nameTextStyle: {
            color: 'rgba(0, 0, 0, 0.45)',
            fontSize: 14,
            fontFamily: 'Source Han Sans CN-Regular',
            align: 'left',
            padding: [0, 0, -5, 0]
          },
          type: 'value',
          splitLine: { // y轴网格线
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.1)',
              type: 'dashed'
            }
          },
          axisLabel: { // y轴文本
            // showMinLabel: false, //y轴是否设置最小刻度0
            color: 'rgba(0, 0, 0, 0.85)',
            textStyle: {
              fontSize: 14,
              fontFamily: 'Source Han Sans CN-Regular'
            }
          },
          axisTick: {
            show: false
          },
          axisLine: { // y轴线
            show: false,
            lineStyle: {
              color: '#D1D1D1'
            }
          }
        }],
        series: seriousList
      }
      this.chart && option && this.chart.setOption(option)
    }
  }
}
</script>
<style lang="scss" scoped>
.ana-chart {
  width: 100%;
  height: 100%;
}
</style>
