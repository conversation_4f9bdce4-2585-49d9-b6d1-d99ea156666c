<template>
<div class="service">
<div class="service-header">
  <number-title num="01" text="服务整体情况" />
  <div class="service-date">
     <date-picker @change="handleDateChange" :default-date="defaultDate"
                  :date-types="childKPI.dateTypes || topTarget.dateTypes"/>
     <el-button  type="primary" @click="queryDate" size="small" >查询</el-button>
  </div>
</div>
<div>
  <!-- 顶部服务指标 -->
  <div class="service-kpi">
     <div v-for="item in KPI" :key="item.targetId"
          :class="{'service-kpi-item': true ,'service-kpi-selected': item.targetId == selectedKpiId}"
          @click="changeKPI(item)">
        <img :src="item.img" />
        <span class="kpi-label">{{item.name}}</span>
     </div>
  </div>
  <!-- 服务指标地图和图表 -->
  <div class="service-kpi-detail">
    <!-- 服务指标地图 -->
    <service-map class="service-kpi-map"
                 ref="map"
                 :key="selectedKpiId" style="height: 550px;"
                 :title="mapConfig.title" :map-down="mapConfig.mapDown"
                 :map-data="mapData" @map-click="onMapClick"
                 :active-name="mapActiveName" v-loading="mapLoading"
                 :map-max-level="mapConfig.mapMaxLevel"/>
    <!-- 服务指标右侧部分 -->
    <div class="service-kpi-charts" v-loading="barLineLoading">
      <!-- 二级指标的标题 -->
      <div class="service-title">
        <span v-for="item in KPIChildren"
             :class="{'active': childKPI.targetId == item.targetId}"
             :key="item.targetId"
             @click="changeKPIChild(item)">{{item.name}}</span>
      </div>
      <div class="service-child">
        <!-- 是否有趋势图，地市tab的切换 -->
        <div class="service-tabs" v-if="childKPI.tabs">
          <span v-for="tb in childKPI.tabs" :key="tb.tabId"
               :class="{'active': tb.tabId == childTabActive}"
               @click="changeTab(tb)">
               {{tb.name}}
          </span>
        </div>
        <!-- 是否有下级的指标 -->
        <div class="service-splits" v-if="childKPI.children">
          <template v-for="(tb,index) in childKPI.children">
            <span :key="tb.targetId"
                  :class="{'active': tb.targetId == childSplitActive, 'disable': tb.disable}"
                  @click="changeSplit(tb)">
                  {{tb.name}}
            </span>
            <template v-if="index < childKPI.children.length-1">|</template>
          </template>
        </div>
      </div>
      <!-- 指标具体某些选项展示 -->
      <service-table :table-data="filterKPITable" :key="childSplitActive || childKPI.targetId"/>
      <!-- echarts图表 -->
      <!-- 工信部申诉--TOP10申诉问题改善 -->
      <service-top-switch-chart
           ref="topEcharts"
           v-if="KPI_ECHARTS[echartsKey] && KPI_ECHARTS[echartsKey].echartComponent == 'ServiceTopSwitchChart'"
           class="service-echart"
           @switch-type="switchTOP10Table">
      </service-top-switch-chart>
      <!-- 其余模块的echarts图表 -->
      <service-bar-line v-else class="service-echart" :key="echartsKey"
        ref="serviceBarLine"
        :key-map="KPI_ECHARTS[echartsKey] ? KPI_ECHARTS[echartsKey].keyMap: {}"
        :chart-data="barLineData"
        :chart-option ="KPI_ECHARTS[echartsKey] ? KPI_ECHARTS[echartsKey].option : {}"
        :data-zoom-end="barLineData.length > 9 ? Math.floor(8 / barLineData.length * 100) : 0"/>
    </div>
  </div>
</div>
</div>
</template>

<script>
import NumberTitle from '../../../components/common/numberTitle';
import ServiceMap from './ServiceMap.vue';
import ServiceBarLine from './ServiceBarLine.vue';
import ServiceTopSwitchChart from './ServiceTopSwitchChart.vue';
import ServiceTable from './ServiceTable.vue';
import DatePicker from 'bj_src/nx-components/date-picker/DatePicker.vue';
import {KPI, KPI_ECHARTS} from './service-kpi';
import utils from 'bj_src/utils/utils';
import ajaxRequest  from "@/api/complain-warning";
import {getTopAppeal} from '@/api/satisfaction'


export default {
  name: 'ServiceOverview',
  components:{
    NumberTitle,
    ServiceMap,
    ServiceBarLine,
    ServiceTopSwitchChart,
    ServiceTable,
    DatePicker
  },
  data() {
    return {
      dateType: {
       '日': '1',
       '周': '2',
       '月': '3',
       '季度': '4',
       '日累计': '5'
      },
      pickDate: [],
      date: [],
      defaultDate:{},
      selectedKpiId: 'KPI01',
      KPI, //一级大类指标
      KPI_ECHARTS,// KPI图标相关配置
      KPIChildren: [], // 二级指标数据数组
      childKPI: {}, // 选中的二级指标
      childTabActive: '', // 二级指标tab选中
      childSplitActive: '', // 二级指标下级选中指标
      originKPITable: [], // 保存初始化的表格配置
      KPITable: [],// 二级指标下的表格展示配置

      barLineData: [],// 右侧柱状折线图
      barLineLoading: false, // 右侧柱状折线图加载
      mapData: [], // 地图数据
      mapLoading: false,// 地图数据加载
      mapCity:{ cityid:'640000', cityname:'宁夏', mapLevel:1, mapAction:'query' }
    }
  },
  computed:{
    topTarget() {
      const [{restSerialNo,dateTypes}] = KPI.filter(({targetId})=> targetId == this.selectedKpiId);
      return { restSerialNo, dateTypes };
    },
    mapConfig() {
      const [{name,mapDown,mapMaxLevel}] = KPI.filter(({targetId})=> targetId == this.selectedKpiId);
      return {
        title: `${name}地图`,
        mapDown: this.childKPI.hasOwnProperty('mapDown') ? this.childKPI.mapDown : mapDown,
        mapMaxLevel: this.childKPI.mapMaxLevel || mapMaxLevel
      };
    },
    mapActiveName() {
      switch(this.childSplitActive){
        case '1060601': return '工信部综合满意度（固定电话）';
        case '1060602': return '工信部综合满意度（固定上网）';
        case '1060603': return '工信部综合满意度（移动业务）';
        default: return this.childKPI.name;
      }
    },
    filterKPITable() {
      // 部分指标只有在特定的split下才显示，添加筛选逻辑
      return this.KPITable.map((item)=>{
          return item.hasOwnProperty('targetSplitIds') ? item.targetSplitIds.includes(this.childSplitActive) ? item : false :item
      }).filter((item)=>{
         return !(item === false)
      })
    },
    echartsKey() {
      return [this.selectedKpiId,this.childKPI.targetId,this.childTabActive].filter((item)=>{
        return item ? true : false;
      }).join('_')
    },
    ajaxParams() {
      const [statType,statDate] = this.date;
      return {
        statType: this.dateType[statType],
        opTime: statDate,
        targetId: this.childSplitActive || this.childKPI.targetId,
        dimType: this.childTabActive || '1',
      }
    }
  },
  methods:{
    handleDateChange(date) {
      const [type,value] = date;
      // 处理季度格式为2022-Q1
      this.pickDate = type === '季度' ? [type, value.replace('-0','-Q')] : date;
    },
    queryDate() {
      this.date = [].concat(this.pickDate);
      this.getData();
      // 日期改变，触发change-mode事件，关联服务分项模块
      this.triggerChangeMode();
    },
    changeKPI(kpi) {
      // 改变KPI会触发日期组件更新，触发change事件
      this.selectedKpiId = kpi.targetId;
      this.mapCity = { cityid:'640000', cityname:'宁夏', mapLevel:1, mapAction:'query' };
      this.getKPIChildren();
      this.getData();// 获取数据
      // 一级指标改变，触发change-mode事件，关联服务分项模块
      this.triggerChangeMode();
    },
    // 获取第二级指标数据
    getKPIChildren() {
      const [{children}] = KPI.filter(({targetId})=> targetId == this.selectedKpiId);
      this.KPIChildren = children || [];
      [this.childKPI] = children;
      if(this.childKPI.table) {
        this.originKPITable = JSON.parse(JSON.stringify(this.childKPI.table));
        this.KPITable = JSON.parse(JSON.stringify(this.childKPI.table));
      }
      this.setKPIChild(this.childKPI);
      this.getDataDate();
    },
    changeKPIChild(kpi) {
      this.childKPI = kpi;
      this.setKPIChild(this.childKPI);
      if(this.mapCity.mapLevel > this.mapConfig.mapMaxLevel){
        this.mapCity = this.$refs.map.loadMapLevel(this.mapConfig.mapMaxLevel)
      }
      this.getData();// 获取数据
      // 二级指标改变，触发change-mode事件，关联服务分项模块
      this.triggerChangeMode();
    },
    // 获取下一级指标的相关数据和信息
    setKPIChild(kpi) {
      const {tabs,children: childrenAry} = kpi;
      // 是否有children，默认选中第一个
      if(childrenAry) {
        const [{targetId}] = childrenAry;
        this.childSplitActive = targetId;
      }else{
        this.childSplitActive = '';
      }
      // 是否有tab判断，如趋势图，地市等，默认选中第一个
      if(tabs) {
        const [{tabId,table}] = tabs;
        this.childTabActive = tabId;
        if(table) {
           this.originKPITable = JSON.parse(JSON.stringify(table));
           this.KPITable =  JSON.parse(JSON.stringify(table));
        }
      }else {
        this.childTabActive = '';
      }
    },
    // 切换tab
    changeTab(tab) {
      this.childTabActive = tab.tabId;
      if(tab.table) {
        this.originKPITable =  JSON.parse(JSON.stringify(tab.table));
        this.KPITable =  JSON.parse(JSON.stringify(tab.table));
      }
      this.$nextTick(()=>{
        const action = this.getMapAction();
        this.mapCity['mapAction'] =  action
        console.log(action);
        // 获取图表数据
        this.queryEchartsData();
      })
    },
    // 切换更下一级的指标
    changeSplit(child) {
      this.childSplitActive = child.targetId;
      this.getData();
      // 二级指标下级指标改变，触发change-mode事件，关联服务分项模块
      this.triggerChangeMode();
    },
    onMapClick(map) {
      const {mapSelected,parentCityId} = map;
      try{
       this.mapCity = mapSelected ? {...map} : {...map, cityid: parentCityId || '640000'};
       this.queryMap();
      }catch(e){
        console.log(e)
      }

    },
    getMapAction() {
      const {mapLevel} = this.mapCity;
      // 是否配置linkMapAction
      const linkMapAction = KPI_ECHARTS[this.echartsKey] && KPI_ECHARTS[this.echartsKey].linkMapAction ? KPI_ECHARTS[this.echartsKey].linkMapAction : 'query';
      // 第3级直接选中
      return mapLevel == 3 ? linkMapAction : this.mapConfig.mapDown ? 'query' : linkMapAction;
    },
    queryMap() {
      const {mapLevel,name} = this.mapCity;
      const action = this.getMapAction();
      this.mapCity['mapAction'] = action
      // 下转获取地图数据
      if(mapLevel < 3 && this.mapConfig.mapDown) {
         this.getMapData();
      }
      if(action == 'select'){
        this.barLineData = this.barLineData.map((item)=>{
          if(item.cityname == name){
            return {
              ...item,
              itemStyle:{ color:'#FFDF55'}
            }
          }
          delete item.itemStyle;
          return item;
        })
      }else{
        this.queryEchartsData();
      }
    },
    // 关联服务分项
    triggerChangeMode() {
      const selectedKpiId = {'KPI01': '3','KPI04': '2', 'KPI03': '1'};
      let targetIds = [selectedKpiId[this.selectedKpiId] || this.selectedKpiId];
      // 工信部满意度id匹配
      if(this.selectedKpiId === 'KPI01'){
        const slit = {'1060603': '10401', '1060602': '10402','1060601':'10403'};
        if(this.childSplitActive){
          targetIds.push(slit[this.childSplitActive]);
        }
      }else if(this.selectedKpiId === 'KPI03'){ // 自测满意度id匹配
        const tab = {'KPI0301': '101','KPI0302': '102','KPI0303': '103'}
        const slit = {
          '101': '1', '105060201': '2','KPI030103': '3',
          '102':'1','105060101':'2','KPI030203': '3'
        };
        targetIds.push(tab[this.childKPI.targetId]);
        if(this.childSplitActive){
          targetIds.push(slit[this.childSplitActive]);
        }
      }else{
         targetIds.push(this.childKPI.targetId);
      }
      this.$emit('change-mode',[targetIds,this.date]);
    },
    // 获取页面数据
    getData() {
     // 获取图表数据
     this.getMapData();
     this.queryEchartsData();
    },
    // 获取地图数据
    async getMapData() {
      this.mapData = [];
      this.mapLoading = true;
      const [statType,statDate] = this.date;
      try{
        const {code, data} = await ajaxRequest('qTZ1XTlD',{
            statType: this.dateType[statType],
            statDate,
            targetId: this.childSplitActive || this.childKPI.targetId,
            cityId: this.mapCity.cityid
        })
        if(code == 200 && data && data.data && data.data.info) {
          if(Array.isArray(data.data.info)){
            this.mapData = this.processMapField(data.data.info);
          }else{
            this.mapData =  Object.values(data.data.info).reduce((ary,cur,index)=>{
              if(index === 0) {
                return this.processMapField(cur);
              }else{
                cur.forEach(({targetname,score},idx)=>{
                  ary[idx] = {
                    ...ary[idx],
                    ['targetname'+index]: targetname,
                    ['value'+index]: score,
                  }
                })
                return ary;
              }
            },[])
          }
        }
        this.mapLoading = false;
      }catch(e){
        this.mapLoading = false;
      }
    },
    // 处理地图字段的对应
    processMapField(ary) {
      return ary.map((item)=>{
         return {
          ...item,
          name: item.cityname,
          value: item.score,
          parentCityId: this.mapCity.cityid,
          selected: this.mapCity.mapSelected ? item.cityname == this.mapCity.name : false
         }
      })
    },
    // echarts地图数据判断获取
    queryEchartsData() {
     if(this.childKPI.targetId =='303'){
        this.$nextTick(()=>{
          this.childTabActive == '1' ?  this.getTOP10EchartHorData() : this.getAppealTOPUnit();
        })
     // 工信部申诉：工信部百万申诉率，工信部携号转网申诉率，工信部营销宣传申诉率 需要查询申诉量拼接数据
     }else if(this.childKPI.hasOwnProperty('ajaxIds')){
       this.getAppealData(this.childKPI.ajaxIds)
     }else{
       this.getEchartsData()
     }
    },
    // 获取工信部申诉的数据
    getAppealData([appealAmount,appealRate]) {
      this.barLineLoading = true;
      this.barLineData = [];
      const params = {
        ...this.ajaxParams,
        cityId: this.mapCity.cityid
      };
      Promise.all([
        ajaxRequest( this.childKPI.restSerialNo || this.topTarget.restSerialNo,{
         ...params,
         targetId:appealAmount
        }),
        ajaxRequest( this.childKPI.restSerialNo || this.topTarget.restSerialNo,{
         ...params,
         targetId:appealRate
        })
      ]).then((all)=>{
        const [amount, rate] = all;
        let anountData = [];
        let rateData = [];
        const table =JSON.parse(JSON.stringify(this.originKPITable));

        if(amount.code == 200 && amount.data && amount.data.data){
           const {chart,data:{first:[item]}} = amount.data.data;
           anountData = this.processEchartsData(Object.values(chart));
           table[0].score = item ? item.score : null;
        }
        if(rate.code == 200 && rate.data && rate.data.data){
           const {chart, data: tableData} = rate.data.data;
           rateData = this.processEchartsData(Object.values(chart), this.childTabActive === '1' ? 3 : 1);
           if(tableData.first){
            table[1].score = tableData.first[0] ? tableData.first[0].score : null;
           }
           // 归责单位无领先者计算
           if( table[2]){
            const leading = this.calLeadingValue(Object.values(tableData));
             table[2].score = leading;
           }
        }
        this.KPITable = table;
        this.barLineData = anountData.map((item,index)=>{
           return {...item,...rateData[index]}
        })
      }).catch((e)=>{
        console.log(e)
      }).finally(()=>{
        this.barLineLoading = false;
      })
    },
    // 工信部申诉-- TOP10申诉问题改善--归责单位
    async getAppealTOPUnit() {
      this.barLineData = [];
      this.barLineLoading = true;
      try{
        const {code,data} = await getTopAppeal({
          ...this.ajaxParams,
          cityId: this.mapCity.cityid
        });
        if(code == 200 && data){
          this.barLineData =  utils.handlerMomrateAndYoyrate((data.chart || [])).map((it)=>{
            return {...it,[`value${start+index+1}`]: it.score, [`momrate${start+index+1}`]: it.momrate}
          });
         // 处理表格显示内容
         const table =JSON.parse(JSON.stringify(this.originKPITable));
         table[0].score = data.scoreSum;
         table[0].rate = data.totalScoreYearBasisSum;
         table[1].score = data.appealRateSum;
         table[1].rate = data.totalAppealYearBasisSum;
         this.KPITable = table;
        }
        this.barLineLoading = false;
      }catch(e){
        this.barLineLoading = false;
      }
    },
    // 获取echarts图表数据
    async getEchartsData() {
      this.barLineData = [];
      if(!this.mapCity.cityid) return;
      this.barLineLoading = true;
      try{
        const cityId = this.mapCity.mapAction === 'select' && this.mapCity.mapSelected ? this.mapCity.parentCityId : this.mapCity.cityid;
        const {code, data} = await ajaxRequest( this.childKPI.restSerialNo || this.topTarget.restSerialNo,{
          ...this.ajaxParams,
          cityId
        })
        if(code == 200 && data && data.data){
           const {chart, data: tableData} = data.data;
           this.barLineData = this.processEchartsData(Object.values(chart));
           this.KPITable = this.processTableData(Object.values(tableData))
        }
        this.barLineLoading = false;
      }catch(e){
        this.barLineLoading = false;
      }
    },
    // 处理echarts数据
    processEchartsData(dataAry, start = 0) {
     let data = [];
     const {mapSelected,name,mapAction} = this.mapCity;
     dataAry.forEach((item,index)=>{
      const ary = utils.handlerMomrateAndYoyrate(item).map((it)=>{
        return {
          ...it,
          [`value${start+index+1}`]: it.score,
          [`momrate${start+index+1}`]: it.momrate,
          ...(mapSelected  && mapAction =='select' ? (it.cityname == name ? {itemStyle:{ color:'#FFDF55'}} : {}) : {})
        }
      });
      if(index == 0){
        data = ary;
      }else{
        for(let i = 0; i< data.length; i++){
          data[i] = {...data[i],...ary[i]}
        }
      }
     })
     return data;
    },
    // 处理table数据
    processTableData(tableAry) {
      let ary = tableAry.reduce((pre,cur)=>{
         return pre.concat(cur);
      },[]);
      ary = utils.handlerMomrateAndYoyrate(ary);
      const table =JSON.parse(JSON.stringify(this.originKPITable));
      if(ary.length){
        table.forEach((item,index)=>{
          if(!item.isCal){
            item.score = ary[index] ? ary[index].score || null : null;
          }else{
            item.score = this.calLeadingValue(ary);
          }
          // 比率
          if(item.hasOwnProperty('rate')) {
             item.rate =  ary[index] ? ary[index].momrate ? (isNaN(Number(ary[index].momrate)) ? null : ary[index].momrate) : null : null;
          }
        })
      }
      return table;
    },
    // 计算领先值
    calLeadingValue(ary) {
     if(!ary.length) return null;
     const [first,second,third] = ary;
     if(!second && !third) return null;
     let secleading,thileading;
     if(second){
      secleading = (Number(first.score) - Number(second.score)).toFixed(2);
     }
     if(third){
      thileading = (Number(first.score) - Number(third.score)).toFixed(2);
     }
     if(secleading == undefined) return thileading;
     if(thileading == undefined) return secleading;
     return Math.min(secleading,thileading);
    },
    // 工信部 - TOP10申诉问题改善-趋势图
    async getTOP10EchartHorData() {
     this.barLineLoading = true;
     try{
      this.$refs.topEcharts  && await this.$refs.topEcharts.getData({
        ...this.ajaxParams,
        cityId: this.mapCity.cityid
      });
      this.barLineLoading = false;
     }catch(e){
      this.barLineLoading = false;
     }
    },
    // 工信部 - TOP10申诉问题改善 - 趋势图
    // 点击原因下转，展示的table表格内容不一致
    switchTOP10Table(type = 'HorizontalBar', data) {
      const table = type == 'BarLine' ? [
        {label:'总申诉量',score: null,rate: null, unit: '%'},
        {label:'总申诉率',score: null,rate: null, unit: '%'}
      ] : JSON.parse(JSON.stringify(this.originKPITable));
      if(data) {
        table.forEach((item,index)=>{
           item.score = data[index].score;
           item.rate = data[index].rate;
        })
      }
      this.KPITable = table;
    },
    // 获取最新有数据的时间
    getDataDate() {
      ajaxRequest('Sp6IGTY6',{
        type:'1'
      }).then(()=>{
      })
      if(this.selectedKpiId === 'KPI01') {
        this.date = ['季度', '2022-Q2'];
      }else {
         this.date = ['月', '2022-05'];
      }
      const [type,date] = this.date;
      this.defaultDate = {[type]: date.replace('-Q','-0')};
    }
  },
  created() {
    this.getDataDate();
    this.getKPIChildren();
    this.getData();
    this.triggerChangeMode();
  }
}
</script>

<style lang="scss" scoped>
.service{
 .service-header{
  display: flex;
  justify-content: space-between;
  align-items: center;

  :deep( ) {
    .header{
      .text{
        white-space: nowrap;
      }
    }
  }
  .service-date{
    display: flex;

    .el-button{
      margin-left: 10px;
    }
  }
 }
 .service-kpi {
  display: flex;
  background: #ffffff;
  padding: 0 10px;
  margin-bottom: 1px;

  .service-kpi-item {
    display: flex;
    align-items: center;
    padding: 15px 10px 10px 0;
    margin: 0 30px;
    cursor: pointer;
    border-bottom: 4px solid transparent;

    & + .service-kpi-item {
      margin-left: 50px;
    }
    &.service-kpi-selected{
      border-bottom: 4px solid #ff9900;
      span{
        color: #ff9900;
      }
    }
    span{
      display: block;
      padding-left: 10px;
    }
    .kpi-label{
      font-size: 14px;
      color: #595959;
    }
    .kpi-number{
      font-size: 24px;
      color: #262626;
    }
  }
 }
 .service-kpi-detail{
  background: #ffffff;
  display: flex;

  .service-kpi-map{
    flex: 0.85;
  }
  .service-kpi-charts {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;

    &::before{
      position: absolute;
      content: ' ';
      width: 1px;
      top: 20px;
      bottom: 20px;
      background: #F0F0F0;
    }
    .service-echart{
      width: 100%;
      flex: 1;
      position: relative;
    }
  }
 }
 .service-title {
  color: #8c8c8c;
  font-size: 14px;
  padding: 15px 15px 10px;

  span{
    padding: 0 2px;
    cursor: pointer;
    &:not(:first-child){
     margin-left: 10px;
    }
    &.active{
      color:#262626;
      font-weight: bold;
    }
  }

  &.service-title-border{
    // border-bottom: 1px solid rgba(140,140,140,0.35);

    span{
      &.active{
        padding-bottom: 6px;
        border-bottom: 2px solid #FFBD01;
      }
    }
  }
 }
 .service-child{
  display: flex;
  align-items: center;
 }
 .service-tabs{
  padding: 0 15px;
  display: inline-block;
  span{
   font-size: 12px;
   background: #F0F0F0;
   display: inline-block;
   padding: 2px 8px;
   cursor: pointer;

   &:first-child{
    border-radius: 2px 0 0 2px;
   }
   &:last-child{
    border-radius: 0 2px 2px 0;
   }
   &.active{
    background: #FEC002;
   }
  }
 }
 .service-splits{
  padding: 0 15px;
  display: inline-block;
  font-size: 12px;
  color: #8c8c8c;

  span{
  //  font-size: 12px;
  //  color: #8c8c8c;
   display: inline-block;
   padding: 2px 4px;
   cursor: pointer;

   &.active{
    color: #FEC002;
   }

   &.disable {
    opacity: 0.45;
    cursor: not-allowed;
    pointer-events: none;
   }
  }
 }
}
</style>
