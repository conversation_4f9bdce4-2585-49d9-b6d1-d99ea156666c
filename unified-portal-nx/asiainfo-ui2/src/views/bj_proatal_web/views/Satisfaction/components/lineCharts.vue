<template>
  <div ref="chart" class="charts" />
</template>

<script>
import Vue from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'LineCharts',
  props: {
    data: Array
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    xAxisData() {
      const arr = []
      this.data.forEach(item => {
        arr.push(item.statDate)
      })
      return arr
    },
    yAxisData() {
      const arr = []
      this.data.forEach(item => {
        arr.push(item.score)
      })
      return arr
    },
    maxNum() {
      let max = 0
      this.data.forEach((item, index) => {
        if (index == 0) {
          max = Math.ceil(item.score)
        }
        if (item.score > max) {
          max = Math.ceil(item.score)
        }

        max = (Math.ceil(max / 2) * 2) > 100 ? 100 : (Math.ceil(max / 2) * 2)
      })
      return max
    },
    minNum() {
      let min = 0
      this.data.forEach((item, index) => {
        if (index == 0) {
          min = Math.floor(item.score)
        }
        if (item.score < min) {
          min = Math.floor(item.score)
        }

        min = parseInt(min / 2) * 2
      })
      return min
    }
  },
  watch: {
    data() {
      this.initChart()
    }

  },
  created() {

  },
  mounted() {
    this.initChart()
  },
  methods: {
        	insert_flg(str, flg, sn) {
		        let newstr = ''
		        let len = 0
			      len = str.length
		        for (let i = 0; i < len; i += sn) {
		          const tmp = str.substring(i, i + sn)
		          newstr += tmp + flg
		        }

		        return newstr
		      },

        	initChart() {
				    if (this.chart !== null && this.chart !== '' && this.chart !== undefined) {
				    	this.chart.dispose()
				    }
	          this.chart = echarts.init(this.$refs.chart, null, {
	              render: 'svg'
	          })
	          this.updateOption()
	          window.addEventListener('resize', () => {
	              this.chart.resize()
	          })
	        },

	        updateOption() {
	        	const option = {
        title: {
						    text: '满意度变化趋势图',
	              show: true,
	              textStyle: {
	                  fontSize: 15,
	                  fontWeight: 'normal',
	                  lineHeight: 40,
            color: '#6B6B6B',
	                  fontFamily: 'SourceHanSansSC-Regular'
	              },
	              left: -5,
	              top: -15,
          subtext: '单位：分',
	              subtextStyle: {
	              	fontWeight: 'normal',
	              	color: 'rgb(140,140,140)',
	              	fontSize: 13,
	              	lineHeight: 1,
	              	fontFamily: 'SourceHanSansSC-Normal'
	              }
	            },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return params[0].name + '<br />' + params[0].marker + params[0].value
          }
        },
        legend: {
          data: ['满意度'],
          right: 30,
          top: 25
        },
        grid: {
					    	left: 0,
					    	right: 0,
					    	top: 70,
					    	bottom: 0,
					    	containLabel: true
					    },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.xAxisData,
          axisLine: {
					        show: true,
					        lineStyle: {
					        	color: 'rgb(157, 157, 157)'
					        }
					      },
          axisTick: {
					      	show: false,
					      	alignWithLabel: true
					      },
          axisLabel: {
					      	interval: 0,
					      	color: 'rgb(157, 157, 157)',
					      	lineHeight: 12,
					      	formatter: (value) => value.slice(5, 7) > 9 ? value.slice(5, 7) + '月' : value.slice(6, 7) + '月'
					      }
        },
        yAxis: {
          type: 'value',
          // max:this.maxNum,
          max: (this.maxNum - this.minNum > 8) ? this.maxNum : (this.minNum + 8),
          // min:(this.maxNum - this.minNum > 8) ? this.minNum : ((this.maxNum - 8) >= 0 ? (this.maxNum - 8) : 0),
          min: this.minNum,
          minInterval: 2,
          boundaryGap: ['100%', '100%'],
          splitLine: {
            lineStyle: {
              color: '#ccc',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '满意度',
            data: this.yAxisData,
            type: 'line',
            smooth: true,
            itemStyle: {
              color: 'rgb(246, 175, 56)'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(246, 175, 56)'
                },
                {
                  offset: 1,
                  color: 'rgb(255, 255, 255)'
                }
              ])
            },
            symbol: 'circle',
            symbolSize: 9,
            label: {
    			    		  show: true,
    			    		  position: 'top'
    			    		}
          }
        ]
      }
      this.chart.clear()
	        	this.chart.setOption(option, true)
	        }
  }
}
</script>

<style lang="less" scoped>
	// .bar {
	// 	// width:100%;
	// 	height:100%;
	// }

</style>
