<template>
	<div class="charts" ref="chart">
	</div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";

    export default {
        name:"Bar",
        props:{
            data:Array
        },
        data(){
            return {
                chart:null
            }
        },
        watch: {
            data(){
              this.initChart()
            }
			
        },
        computed:{
            xAxisData(){
                const arr = []
                this.data.forEach(item => {
                  arr.push(item.cityName)
                })
                return arr
            },
            yAxisData(){
                const arr = this.data.map(item=>{
					return {
						name: item.cityName,
						value:item.score,
						rank:item.rank
					}
				})
                return arr
            }
        },
        created(){
            
        },
        mounted(){
            this.initChart()
        },
        methods:{
        	insert_flg(str, flg, sn) {
		      let newstr = '';
		      let len = 0;
			  len = str.length;
		      for (let i = 0; i < len; i += sn) {
		        const tmp = str.substring(i, i + sn);
		        newstr += tmp + flg;
		      }
			
		      return newstr;
		    },

        	initChart(){
				if(this.chart !== null && this.chart !== '' && this.chart !== undefined){
					this.chart.dispose()
				}
	            this.chart = echarts.init(this.$refs.chart, null, {
	                render: "svg"
	            });

	            this.updateOption();

	            window.addEventListener("resize", ()=>{
	                this.chart.resize();
	            });
	        },

	        updateOption(){
				const _this = this
	        	let option = {
                    title: {
						text: '',
	                    show: true,
	                    textStyle:{
	                        fontSize:13,
	                        fontWeight:'normal',
	                        lineHeight: 40,
	                        fontFamily: 'SourceHanSansSC-Regular'
	                    },
	                    left:10,
	                    top:5,
	                    subtext:'单位：分',
	                    subtextStyle:{
	                    	fontWeight:'normal',
	                    	color:'rgb(140,140,140)',
	                    	fontSize:13,
	                    	lineHeight:20,
	                    	fontFamily: 'SourceHanSansSC-Normal'
	                    }
	                },
					grid:{
						left:0,
						right:0,
						top:50,
						bottom:0,
						containLabel: true
					},
                    tooltip: {
                        trigger: 'axis',
						axisPointer: {
    					  type: 'shadow',
						  color:'rgb(224, 224, 224)'
    					},
                        symbol:'5',
                        formatter: function(params){
                          return  params[0].name + '<br />' + params[0].marker + '   '  + params[0].value
                        },
						
                    },

	        		xAxis: {
                      type: 'category',
                      data: this.xAxisData,
                      axisLine:{
					    show: true,
					    lineStyle:{
					    	color: 'rgb(157, 157, 157)'
					    }
					  },
                      axisTick: {
					  	show:false,
					  	alignWithLabel: true
					  },
                      axisLabel: {
					  	interval: 0,
					  	color: 'rgb(157, 157, 157)',
					  	lineHeight:12,								
					  	formatter: (value) =>  this.insert_flg(value, '\n', 2),
					  },
                    },
                    yAxis: {
                      type: 'value',
					  show:false,
					  splitLine: {
                	    lineStyle: {
                	      color: '#ccc',
                	      type: 'dashed',
                	    },
                	  },
                      
                    },
                    series: [
                      {
                        data: this.yAxisData,
                        type: 'bar',
                        barWidth: '12',
                        itemStyle: {
						    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						      { offset: 0, color:'#FFC165'},
						      { offset: 1, color:'#FF9901'}
						    ])
				    	},
						label: {
    					  show: false,
    					  position: 'top',
						  formatter: function(params){
								return  '排名:' +params.data.rank
                        	  
                        	}
    					},

						emphasis:{
							label:{
								show:true
							}
						}
						
                      }
                      
                    ]
				};
				this.chart.clear()
	        	this.chart.setOption(option,true);
	        }
        }
    };
</script>

<style lang="less" scoped>
	// .bar {
	// 	// width:100%; 
	// 	height:100%;
	// }

</style>