<template>
  <div class="echart-box" :style="{ height: height, width: width }">
    <div
      ref="pieChart"
      :style="{ width: '100%', minWidth: '500px', height: '100%' }"
    />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: tableData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
    <!--<div
      class="legend-right"
      :style="{ width: 'calc(100% - 300px)', height: '100%' }"
    >
      <div class="table-all">
        <div class="table-line table-headers">
          <div class="table-cells"></div>
          <div class="table-cells">业务名称</div>
          <div class="table-cells">投诉量</div>
          <div class="table-cells">占比</div>
        </div>
        <div class="table-line" v-for="(item, index) in tableData">
          <div class="table-cells">
            <span :style="{ background: colorMap[index] }"></span>
          </div>
          <div class="table-cells">{{ item.target_name }}:</div>
          <div class="table-cells">{{ item.score }}</div>
          <div class="table-cells">{{ item.partrate + "%" }}</div>
        </div>
      </div>
    </div>-->
  </div>
</template>
<script>
export default {
  props: {
    width: String,
    height: String,
    tableData: Array,
  },
  data() {
    return {
      colorMap: [
        "#F98781",
        "#FFC06F",
        "#84C285",
        "#3499FF",
        "#42ABFF",
        "#FDAC15",
        "#FF4248",
        "#ED73EA",
        "#A25FF5",
        "#F98782",
        "#FFC063",
        "#84C284",
        "#3499F5",
        "#42ABF6",
        "#FDAC17",
        "#FF4249",
        "#ED73E1",
        "#A25FF2",
        "#F98731",
        "#FFC04F",
        "#84C255",
        "#34996F",
        "#42AB7F",
        "#FDAC85",
        "#FF4298",
        "#ED73AA",
        "#A25FB5",
      ],
    };
  },
  watch: {
    tableData() {
      this.drawChart();
    },
  },
  mounted() {
    this.drawChart();
    // console.log(this.datas);
  },
  methods: {
    drawChart() {
      const that = this;
      const myChart = this.$echarts.init(this.$refs.pieChart);
      let totalCount = 0;
      this.tableData.forEach((e) => {
        totalCount += Number(e.score);
        if (e.partrate && !e.partrate.includes("-")) {
          e.partrate = (Number(e.partrate) * 100).toFixed(2);
        }
      });
      const option = {
        tooltip: {
          show: false,
        },
        legend: {
          show: this.tableData.length > 0,
          type: "scroll",
          orient: "vertical",
          top: "center",
          bottom: 40,
          right: 40,
          icon: "circle",
          itemGap: 18,
          itemWidth: 8,
          itemHeight: 8,
          pageButtonItemGap: 5,
          textStyle: {
            fontSize: 13,
            color: "#D9D9D9",
            rich: {
              name: {
                color: "#595959",
                width: 87,
                padding: [0, 0, 0, 0],
              },
              val: {
                width: 70,
                color: "#8c8c8c",
                padding: [0, 10, 0, 6],
              },
              numNum: {
                width: 70,
                color: "#8c8c8c",
              },
            },
          },
          // 控制legend的间距
          padding: [10, 0, 20, 20],
          // 这里设置箭头的路径
          pageButtonPosition: "end", // 翻页的位置。'start'：控制块在左或上,end控制块在右或下。

          pageIconSize: 14, // 这当然就是按钮的大小

          formatter: function (name) {
            let showNum = "--";
            let percentage = "--";
            for (var i = 0; i < that.tableData.length; i++) {
              if (that.tableData[i].target_name == name) {
                showNum = that.tableData[i].score;
                percentage = that.tableData[i].partrate + "%";
              }
            }
            return `{name| ${
              name.length > 10 ? name.slice(0, 5) + "..." : name
            }}   {numNum| ${showNum}}  {val| ${percentage}}`;
          },
        },
        series: [
          {
            name: "--",
            type: "pie",
            radius: ["60%", "80%"],
            center: ["25%", "50%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "14",
                fontWeight: "bold",
                formatter: ["{a|{b}}", "{b|{c}}"].join("\n"),
                rich: {
                  a: {
                    fontSize: 14,
                    fontWeight: 500,
                    color: "#595959",
                    lineHeight: 28,
                  },
                  b: {
                    fontSize: 24,
                    fontWeight: 500,
                    color: "#262626",
                    lineHeight: 24,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: this.tableData.map((el, index) => {
              return {
                value: el.score,
                name: el.target_name,
                itemStyle: { color: this.colorMap[index] },
              };
            }),
          },
        ],
      };
      myChart.setOption(option);
      setTimeout(function () {
        window.onresize = function () {
          myChart.resize();
        };
      }, 200);
      // 高亮第一条数据
      myChart.dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: 0,
      });
      // 鼠标移动到其他item高亮,取消默认高亮
      myChart.on("mouseover", (v) => {
        if (v.dataIndex != 0) {
          myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: 0,
          });
        }
      });
      // 鼠标移开再次默认高亮
      myChart.on("mouseout", (v) => {
        myChart.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          dataIndex: 0,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.echart-box {
  width: 100%;
  display: flex;
  position: relative;
}
.legend-right {
  position: relative;
}
.table-all {
  display: flex;
  flex-direction: column;
  position: relative;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  max-height: 295px;
  overflow: auto;
  max-width: 80%;
  .table-line {
    display: flex;
    align-items: center;
    margin-bottom: 7px;
    .table-cells {
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;
      &:nth-child(1) {
        width: 15px;
      }
      &:nth-child(2) {
        min-width: 120px;
      }
      &:nth-child(3) {
        width: 50px;
      }
      &:nth-child(4) {
        width: 50px;
      }
      span {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: #f98781;
        border-radius: 50%;
      }
    }
  }
  .table-headers {
    .table-cells {
      color: #8c8c8c !important;
    }
  }
}
</style>
