<template>
  <!-- 手机用户 -->
  <div class="cbh-complian">
    <time-select
      class="modes-select"
      :compainMode="true"
      @checkDate="checkDate"
    ></time-select>
    <div class="nav-box">
      <div class="content">
        <div class="content-type">
          <div class="content-heads">
            <div class="content-title">CHBN投诉总量</div>
          </div>
          <div class="mapChart">
            <LineBarChart
              width="100%"
              height="270px"
              :xName="xName"
              :legendsBar="legendsBar"
              :legendsLine="legendsLine"
              :colors="chartColors"
              :colors2="chartColors2"
              :barValue="barValue"
              :lineValue="lineValue"
            ></LineBarChart>
          </div>
        </div>

        <div class="content-type long-width">
          <div class="content-heads">
            <div class="content-title">CHBN业务占比</div>
          </div>
          <div class="mapChart">
            <PieChart
              width="100%"
              height="270px"
              :tableData="activeData1"
            ></PieChart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getPieNums } from "@/api/teleservice/api";
import LineBarChart from "./LineBarChart.vue";
import PieChart from "./pieChart.vue";
import TimeSelect from "../../Satisfaction/components/timeSelect";
export default {
  components: {
    LineBarChart,
    PieChart,
    TimeSelect,
  },
  data() {
    return {
      activeType: "",
      show: true,
      restNo: "XYpuKzcr",
      chartColors: ["#5FAEFF,#0682FF", "#FFBE4D"],
      chartColors2: ["#FFBE4D", "#4CD1C3"],
      xName: [],
      legends: [],
      chartDatas: [],
      tableData: [],
      headData: [],
      timeArr: [],
      barValue: [],
      lineValue: [],
      legendsLine: [],
      legendsBar: [],
      areaId: "",
      loading: false,
      monthValue: "",
      typeMode: "",
      activeData: {},
      activeData1: [],
    };
  },
  mounted() {},
  methods: {
    checkDate(mode, day, month, week, season) {
      this.monthValue =
        mode == 1 ? day : mode == 2 ? month : mode == 3 ? week : season;
      this.typeMode = mode;
      if (this.activeType) {
        this.getDatas();
      }
    },
    tranferTarget(childTarget, activeType) {
      this.activeType = activeType;
      this.getDatas();
    },
    async getDatas() {
      if (!this.activeType) {
        return;
      }
      let { data } = await getPieNums({
        restSerialNo: "SCIPuzOB",
        opTime: this.monthValue,
        statType: this.typeMode + "",
        type: this.activeType,
      });
      let arrays = Object.values(data.data || {});
      this.activeData = arrays[0];
      this.barValue = [];
      this.lineValue = [];
      this.legendsBar = [];
      this.legendsLine = [];
      this.xName = [];
      if (this.activeData.data && this.activeData.data.length) {
        this.legendsBar.push("投诉量");
        this.legendsLine.push("环比");
        this.barValue.push(this.activeData.data.map((item) => item.score));
        this.lineValue.push(
          this.activeData.data.map((item) =>
            item.momratetype && item.momratetype == 1
              ? "-" + item.momrate
              : item.momrate
          )
        );
        this.xName = this.activeData.data.map((e) => e.statdate);
      }
      this.activeData1 = arrays[1].data || [];
    },
  },
};
</script>
<style lang="less" scoped>
.mapChart {
  width: 100%;
  height: 100%;
}
.cbh-complian {
  position: relative;
  .modes-select {
    position: absolute;
    right: 0;
    top: -53px;
    z-index: 22;
  }
}
.nav-box {
  display: flex;
  width: 100%;
  height: 355px;
  .SubTitle {
    margin-top: 26px;
    width: auto;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0px;
    li {
      display: inline-block;
      width: auto;
      max-width: 200px;
      font-family: SourceHanSansSC-Regular;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.7);
      cursor: pointer;
      line-height: 40px;
      transition: all 0.3s linear;
      position: relative;
      transition: all 0.3s linear;
      &::after {
        content: "";
        height: 3px;
        width: 0px;
        background: #ff9900;
        position: absolute;
        bottom: 0px;
        left: 0px;
        transition: all 0.3s linear;
      }
    }
    .active {
      font-family: SourceHanSansSC-Medium;
      font-size: 20px;
      color: #262626;
      border-bottom: 3px solid #ff9900;
      &::after {
        width: 90px;
      }
    }
  }
  .content {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    justify-content: space-between;
    background-color: white;
    .content-type {
      width: 50%;
      height: 100%;
      padding: 10px 20px;
      box-sizing: border-box;
      .content-top {
        width: 100%;
        height: 50%;
      }
      .content-heads {
        width: 100%;
        display: flex;
        // padding: 20px;
        padding-right: 20px;
        align-items: baseline;
        .content-title {
          font-size: 18px;
          font-weight: 500;
          font-family: SourceHanSansSC-Regular;
          font-size: 20px;
          color: #262626;
        }
      }
      .content-head {
        width: 100%;
        display: flex;
        // padding: 20px;
        padding-right: 20px;
        justify-content: space-between;
        .content-title {
          font-size: 18px;
          font-weight: 500;
          font-family: SourceHanSansSC-Regular;
          font-size: 20px;
          color: #262626;
        }
      }
    }
    .long-width {
      width: 50%;
      margin-top: 10px;
      height: calc(100% - 20px);
    }
  }
}
</style>
