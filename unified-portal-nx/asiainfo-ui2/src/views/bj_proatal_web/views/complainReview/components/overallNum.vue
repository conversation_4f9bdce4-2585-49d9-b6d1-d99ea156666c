<template>
  <!-- 手机用户 -->
  <div class="jiakuanInstall-content">
    <div class="jiakuanInstall-charts">
      <LineBarChart
        width="100%"
        height="265px"
        :xName="xName1"
        :legendsBar="legendsBar1"
        :legendsLine="legendsLine1"
        :colors="chartColors"
        :colors2="chartColors2"
        :barValue="barValue1"
        :lineValue="lineValue1"
      ></LineBarChart>
    </div>
    <div class="split-title">{{ secondTitle }}</div>
    <div class="jiakuanInstall-charts">
      <LineBarChart
        width="100%"
        height="267px"
        :xName="xName2"
        :legendsBar="legendsBar2"
        :legendsLine="legendsLine2"
        :colors="chartColors"
        :colors2="chartColors2"
        :barValue="barValue2"
        :lineValue="lineValue2"
      ></LineBarChart>
    </div>
  </div>
</template>
<script>
import LineBarChart from "./LineBarChart.vue";
export default {
  components: {
    LineBarChart,
  },
  props: {
    secondTitle: String,
    activeData: Array,
    activeData1: Object,
  },
  data() {
    return {
      show: true,
      restNo: "XYpuKzcr",
      chartColors: ["#5FAEFF,#0682FF", "#FFBE4D"],
      chartColors2: ["#FFBE4D", "#4CD1C3"],
      xName1: [],
      legends: [],
      barValue1: [],
      lineValue1: [],
      legendsLine1: [],
      legendsBar1: [],
      xName2: [],
      barValue2: [],
      lineValue2: [],
      legendsLine2: [],
      legendsBar2: [],
      areaId: "",
      loading: false,
    };
  },
  watch: {
    activeData(val) {
      this.getData();
    },
    activeData1(val) {
      this.getData1();
    },
  },
  mounted() {
    // this.timeArr = this.timeValue;
    // this.areaId = this.permissionSetting ? this.cityId : "999";
  },
  methods: {
    handleChange() {
      this.getData();
      this.getData1();
    },
    getData1(data1) {
      if (data1) {
        this.activeData1 = data1;
      }
      this.barValue2 = [];
      this.lineValue2 = [];
      this.legendsBar2 = [];
      this.legendsLine2 = [];
      let el = this.activeData1;
      if (el.name.includes("量")) {
        this.barValue2.push(el.data.map((item) => item.score));
        this.legendsBar2.push(el.name);
      }
      if (el.name.includes("比") || el.name.includes("值")) {
        this.lineValue2.push(
          el.data.map(
            (item) =>
              (item.momratetype && item.momratetype == 1
                ? "-" + item.momrate
                : item.momrate) ||
              (item.yearBasistype && item.yearBasistype == 1
                ? "-" + item.yearbasis
                : item.yearbasis) ||
              item.scorediff
          )
        );
        this.legendsLine2.push(el.name);
      }

      this.xName2 = this.activeData1.data.map((e) => e.statdate);
    },
    getData(data) {
      if (data) {
        this.activeData = data;
      }

      if (this.activeData.length) {
        this.barValue1 = [];
        this.lineValue1 = [];
        this.legendsBar1 = [];
        this.legendsLine1 = [];
        this.activeData.forEach((el) => {
          if (el.name.includes("量")) {
            this.barValue1.push(el.data.map((item) => item.score));
            this.legendsBar1.push(el.name);
          }
          if (el.name.includes("比") || el.name.includes("值")) {
            this.lineValue1.push(
              el.data.map(
                (item) =>
                  (item.momratetype && item.momratetype == 1
                    ? "-" + item.momrate
                    : item.momrate) ||
                  (item.yearBasistype && item.yearBasistype == 1
                    ? "-" + item.yearbasis
                    : item.yearbasis) ||
                  item.score
              )
            );
            this.legendsLine1.push(el.name);
          }
        });
        this.xName1 = this.activeData[0].data.map((e) => e.statdate);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.title {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .title-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    column-gap: 20px;
  }
}
.jiakuanInstall-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  min-height: 100%;

  .jiakuanInstall-charts {
    width: 100%;
  }
  .jiakuanInstall-table {
    width: 45%;
    margin-top: 20px;
  }
}
.split-title {
  margin: 10 0px;
  font-size: 14px;
  font-family: SourceHanSansSC, SourceHanSansSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #262626;
  padding-left: 10px;
  position: relative;
  &::before {
    content: "";
    display: inline-block;
    width: 2px;
    height: 12px;
    background: #fe9900;
    position: absolute;
    left: 0px;
    top: 5px;
    z-index: 21;
  }
}
</style>
