<template>
  <!-- 手机用户 -->
  <div class="effect-complian">
    <time-select
      class="modes-select"
      :compainMode="true"
      @checkDate="checkDate"
    ></time-select>
    <el-tabs
      class="tab-spare"
      v-model="activeName"
      key="1"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item, index) in tabList"
        :label="item.name"
        :name="item.name"
        :key="item.name"
      ></el-tab-pane>
    </el-tabs>
    <div class="nav-box">
      <div class="content">
        <div class="content-type" v-if="activeTab == '省内口径概览'">
          <div class="content-heads">
            <div class="split-title">{{ activeName }}</div>
          </div>
          <div class="mapChart">
            <LineBarChart
              width="100%"
              height="270px"
              :xName="xName1"
              :legendsBar="legendsBar1"
              :legendsLine="legendsLine1"
              :colors="chartColors"
              :colors2="chartColors2"
              :barValue="barValue1"
              :lineValue="lineValue1"
            ></LineBarChart>
          </div>
        </div>
        <div class="content-type" v-if="activeTab == '集团口径概览'">
          <div class="content-heads">
            <el-tabs
              class="tab-spare"
              v-model="activeNames"
              key="1"
              @tab-click="handleClicks"
            >
              <el-tab-pane
                v-for="(item, index) in tabLists"
                :label="item.name"
                :name="item.name"
                :key="item.name"
              ></el-tab-pane>
            </el-tabs>
          </div>
          <div class="mapChart">
            <LineBarChart
              width="100%"
              height="270px"
              :xName="xName1"
              :legendsBar="legendsBar1"
              :legendsLine="legendsLine1"
              :colors="chartColors"
              :colors2="chartColors2"
              :barValue="barValue1"
              :lineValue="lineValue1"
            ></LineBarChart>
          </div>
        </div>
        <div class="content-type long-width">
          <div class="content-heads">
            <div class="split-title">{{ secondTitle }}</div>
          </div>
          <div class="mapChart">
            <LineBarChart
              width="100%"
              height="270px"
              :xName="xName2"
              :legendsBar="legendsBar2"
              :legendsLine="legendsLine2"
              :colors="chartColors"
              :colors2="chartColors2"
              :barValue="barValue2"
              :lineValue="lineValue2"
            ></LineBarChart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getEffectNums } from "@/api/teleservice/api";
import LineBarChart from "./LineBarChart.vue";
import TimeSelect from "../../Satisfaction/components/timeSelect";
export default {
  components: {
    LineBarChart,
    TimeSelect,
  },
  data() {
    return {
      activeTab: "",
      nameList: [],
      activeData: [],
      activeName: "投诉处理满意度",
      tabList: [],
      activeNames: "投诉处理满意度",
      tabLists: [],
      show: true,
      restNo: "XYpuKzcr",
      chartColors: ["#5FAEFF,#0682FF", "#FFBE4D"],
      chartColors2: ["#FFBE4D", "#4CD1C3"],
      option1: ["整体投诉量", "整体万投比"],
      option2: ["家宽报障投诉量", "万客报障率"],
      xName1: [],
      xName2: [],
      legends: [],
      tableData: [],
      barValue1: [],
      lineValue1: [],
      legendsLine1: [],
      legendsBar1: [],
      barValue2: [],
      lineValue2: [],
      legendsLine2: [],
      legendsBar2: [],
      loading: false,
      monthValue: "",
      typeMode: "",
      secondTitle: "投诉量趋势变化",
      activeType: "401",
    };
  },
  mounted() {
    // this.timeArr = this.timeValue;
    // this.areaId = this.permissionSetting ? this.cityId : "999";
  },
  methods: {
    async changeTotals(childTarget, activeTab, activeType) {
      this.activeData1 = [];
      this.activeData = [];
      if (activeType) {
        this.activeType = activeType;
      }
      if (activeTab) {
        this.activeTab = activeTab;
      }
      let _this = this;
      const { data } = await getEffectNums({
        restSerialNo: "WiQfjJeB",
        opTime: this.monthValue,
        statType: this.typeMode,
        type: this.activeType,
        targetId:
          (this.tabList.find((el) => el.name == this.activeName) || {})
            .targetId || "401040101",
      });
      if (!_this.nameList.length) {
        _this.nameList = data.name;
      }
      _this.tabList =
        _this.nameList.find(
          (el) =>
            el.name ==
            (this.activeTab == "集团口径概览"
              ? "灭灯行动治理情况"
              : "效能指标情况")
        ).child || [];
      _this.activeName = this.tabList[0].name;
      _this.handleClick();
    },
    checkDate(mode, day, month, week, season) {
      this.monthValue =
        mode == 1 ? day : mode == 2 ? month : mode == 3 ? week : season;
      this.typeMode = mode;
      if (this.activeTab) {
        this.changeTotals();
      }
      //  this.initDate(null, null, null, 1);
    },
    async handleClick() {
      this.activeData1 = [];
      this.activeData = [];
      if (this.activeTab == "集团口径概览") {
        this.chartColors2 = ["#FFBE4D", "#4CD1C3"];

        let tabLists =
          this.tabList.find((el) => el.name == this.activeName).child || [];
        let array = [];
        tabLists.forEach((item) => {
          if (
            item.name.includes("整体投诉") ||
            item.name.includes("整体万投")
          ) {
            array.push(item);
          }
        });
        this.tabLists = JSON.parse(JSON.stringify(array));
        this.activeNames = this.tabLists[0].name;
        if (this.activeNames.includes("整体投诉")) {
          this.secondTitle = "投诉量趋势变化";
        }
        if (this.activeNames.includes("整体万投")) {
          this.secondTitle = "万投比趋势变化";
        }
        this.handleClicks();
      } else {
        this.chartColors2 = ["#4CD1C3", "#FFBE4D"];
        this.secondTitle = "趋势变化值";
        const { data } = await getEffectNums({
          restSerialNo: "WiQfjJeB",
          opTime: this.monthValue,
          statType: this.typeMode,
          type: this.activeType,
          targetId:
            (this.tabList.find((el) => el.name == this.activeName) || {})
              .targetId || "401040101",
        });
        if (data && data.data) {
          this.activeData = Object.values(data.data)[0];
          if (this.activeType == "401") {
            this.activeData1 = Object.values(data.data)[0];
          } else {
            if (Object.values(data.data)[1]) {
              this.activeData1 = Object.values(data.data)[1];
            }
          }
        }
        this.getData();
        this.getData1();
      }
    },
    getData1(data1) {
      if (data1) {
        this.activeData1 = data1;
      }
      this.barValue2 = [];
      this.lineValue2 = [];
      this.legendsBar2 = [];
      this.legendsLine2 = [];
      this.legendsLine2.push("变化值");
      this.lineValue2.push(this.activeData1.map((item) => item.scorediff));
      this.xName2 = this.activeData1.map((e) => e.statdate);
    },
    getData(data) {
      if (data) {
        this.activeData = data;
      }
      this.barValue1 = [];
      this.lineValue1 = [];
      this.legendsBar1 = [];
      this.legendsLine1 = [];

      if (this.activeTab == "集团口径概览") {
        this.legendsLine1.push("环比");
        this.lineValue1.push(
          this.activeData.map((item) =>
            item.momratetype && item.momratetype == 1
              ? "-" + item.momrate
              : item.momrate
          )
        );
        if (this.activeNames.includes("整体投诉")) {
          this.legendsBar1.push("投诉量");
        }
        if (this.activeNames.includes("整体万投")) {
          this.legendsBar1.push("万投比");
          this.legendsLine1.push("目标值");
          this.lineValue1.push(this.activeData.map((item) => item.targetnum));
        }

        this.barValue1.push(this.activeData.map((item) => item.score));
      } else {
        this.legendsBar1.push(this.activeName);
        this.legendsLine1.push("同比");
        this.legendsLine1.push("环比");
        this.barValue1.push(this.activeData.map((item) => item.score));
        this.lineValue1.push(
          this.activeData.map((item) =>
            item.yearBasistype && item.yearBasistype == 1
              ? "-" + item.yearbasis
              : item.yearbasis
          )
        );
        this.lineValue1.push(
          this.activeData.map((item) =>
            item.momratetype && item.momratetype == 1
              ? "-" + item.momrate
              : item.momrate
          )
        );
      }
      this.xName1 = this.activeData.map((e) =>
        this.activeType == "402" ? e.cityname : e.statdate
      );
    },
    async handleClicks() {
      let _this = this;
      this.activeData1 = [];
      this.activeData = [];
      if (this.activeNames.includes("整体投诉")) {
        this.secondTitle = "投诉量趋势变化";
      }
      if (this.activeNames.includes("整体万投")) {
        this.secondTitle = "万投比趋势变化";
      }
      const { data } = await getEffectNums({
        restSerialNo: "WiQfjJeB",
        opTime: this.monthValue,
        statType: this.typeMode,
        type: this.activeType,
        targetId:
          (this.tabLists.find((el) => el.name == this.activeNames) || {})
            .targetId || "401040101",
      });
      if (data && data.data) {
        this.activeData = Object.values(data.data)[0];
        if (this.activeType == "401") {
          this.activeData1 = Object.values(data.data)[0];
        } else {
          if (Object.values(data.data)[1]) {
            this.activeData1 = Object.values(data.data)[1];
          }
        }
      }
      this.getData();
      this.getData1();
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep {
  .el-tabs__item:hover {
    color: rgb(241, 151, 51);
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__active-bar {
    background-color: rgb(241, 151, 51);
    box-sizing: border-box;
  }
}
::v-deep.el-tabs__nav-next,
::v-deep.el-tabs__nav-prev {
  line-height: 56px;
  font-size: 30px;
}
::v-deep .el-tabs__item {
  padding: 0 10px !important;
  font-size: 14px !important;
  font-family: SourceHanSansSC, SourceHanSansSC-Regular !important;
  font-weight: 400 !important;
  color: #595959 !important;
}
::v-deep .el-tabs__header {
  border-bottom: 1px solid #e6e6e6;
}
::v-deep .is-active {
  color: #262626 !important;
}
.mapChart {
  width: 100%;
  height: 100%;
}
.split-title {
  margin: 10 0px;
  margin-bottom: 10px;
  font-size: 14px;
  font-family: SourceHanSansSC, SourceHanSansSC-Regular;
  font-weight: 400;
  text-align: left;
  color: #262626;
  padding-left: 10px;
  position: relative;
  &::before {
    content: "";
    display: inline-block;
    width: 2px;
    height: 12px;
    background: #fe9900;
    position: absolute;
    left: 0px;
    top: 5px;
    z-index: 21;
  }
}
.effect-complian {
  position: relative;
  .modes-select {
    position: absolute;
    right: 0;
    top: -53px;
    z-index: 22;
  }
}
.nav-box {
  display: flex;
  width: 100%;
  height: 355px;
  .SubTitle {
    margin-top: 26px;
    width: auto;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0px;
    li {
      display: inline-block;
      width: auto;
      max-width: 200px;
      font-family: SourceHanSansSC-Regular;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.7);
      cursor: pointer;
      line-height: 40px;
      transition: all 0.3s linear;
      position: relative;
      transition: all 0.3s linear;
      &::after {
        content: "";
        height: 3px;
        width: 0px;
        background: #ff9900;
        position: absolute;
        bottom: 0px;
        left: 0px;
        transition: all 0.3s linear;
      }
    }
    .active {
      font-family: SourceHanSansSC-Medium;
      font-size: 20px;
      color: #262626;
      border-bottom: 3px solid #ff9900;
      &::after {
        width: 90px;
      }
    }
  }
  .content {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    justify-content: space-between;
    background-color: white;
    .content-type {
      width: 50%;
      height: 100%;
      padding: 10px 20px;
      box-sizing: border-box;
      .content-top {
        width: 100%;
        height: 50%;
      }
      .content-heads {
        width: 100%;
        display: flex;
        // padding: 20px;
        padding-right: 20px;
        align-items: baseline;
        ::v-deep .el-tabs__header {
          margin-bottom: 0px !important;
        }
        .content-title {
          font-size: 18px;
          font-weight: 500;
          font-family: SourceHanSansSC-Regular;
          font-size: 20px;
          color: #262626;
        }
      }
      .content-head {
        width: 100%;
        display: flex;
        // padding: 20px;
        padding-right: 20px;
        justify-content: space-between;
        .content-title {
          font-size: 18px;
          font-weight: 500;
          font-family: SourceHanSansSC-Regular;
          font-size: 20px;
          color: #262626;
        }
      }
    }
    .long-width {
      width: 50%;
      margin-top: 10px;
      height: calc(100% - 20px);
    }
  }
}
</style>
