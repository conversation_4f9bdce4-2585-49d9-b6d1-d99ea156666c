<template>
  <div class="teleserve">
    <Banner title="端到端过程质量归集展示" desc="展示端到端服务质量指标情况"/>

    <div class="container">
      <div style="width:100%; margin-bottom: 30px; display:flex; justify-content: flex-end;" >

        <el-cascader
          v-model="filiale"
          :options="filialeOptions"
          ref="refHandle01"
          placeholder="请选择公司"
          class="partment-select cascader"
          popper-class="cascaderPopper"
          @focus="inputFocus"
          size="small"
        ></el-cascader>

        <DateChooseQi
          v-if="date"
          ref="date"
          :defaultDate="date"
          :defaultDateTypeList="defaultDateTypeList"
          @dateChange="dateChange"
        />

        <el-button type='primary' size="small" style="color: #000" @click="onQuery">查询</el-button>
      </div>
      <!-- 归集展示 -->
      <div class="echarts-wrap">
        <div 
        class="echarts-inner-wrap-left" 
        style="width:260px;min-width:260px;height:100%; background:#f0f0f0;">
            <ul class="businessList">
                <li 
                style="display:flex;" 
                v-for="(item,endToEndListindex) in this.endToEndList" 
                :key="endToEndListindex" 
                @click="getbendToEnd(endToEndListindex)" 
                :class="endToEndListindex == endToEndListActive ? 'businessListactive' : '' ">
                    <img :src="require('../assets/img/teleserve.png')" 
                    style="position:relative; top:5px; margin-right: 10px;" 
                    alt="icon" height="50" 
                    v-if="endToEndListindex == endToEndListActive">
                    {{item.name}}
                </li>
            </ul>
        </div>
        <div class="echarts-inner-wrap-right"  style="flex-grow:2; height:720px;"  v-loading="loading" >
            <el-carousel height="720px" direction="vertical" :autoplay="false" ref="carousel" v-if='tableTitle'>
              <el-carousel-item 
              v-for="(item,index) in endToEndList" 
              :key="index" 
              style="color:#fff;" 
              :name="'carousel'+index">
                <el-tabs v-model="activeName" @tab-click="handleClick" v-if="tabLists.length && index==0">
                    <el-tab-pane 
                    v-for="(item,index) in tabLists" 
                    :label="item.name" 
                    :name="item.code" 
                    :key="index" 
                    class="CHoperation">
                        <Table :tableData="item.tableData" :code="item.code" :tableTitle="tableTitle"></Table>
                    </el-tab-pane>
                </el-tabs>

                <div style="padding:20px; box-sizing:border-box;" v-else>
                  <Table :tableData="item.tableData" :code="item.code" :tableTitle="tableTitle"></Table>
                </div>
              </el-carousel-item>
            </el-carousel>
        </div>
      </div>

    </div>

  </div>
</template>
<script>
import * as echarts from "echarts";

import DateChooseQi from '../components/common/DateChooseQi.vue';
import Arrow from '../components/common/arrow.vue';
import Table from '../components/teleservice/Table.vue';
import Banner from '../components/common/Banner';

import {listApi} from '@/api/teleservice/api.js';
const fuhao = require("../assets/img/fuhao.png");


export default {
  name: 'HomePage',
  components: {
    DateChooseQi,
    Arrow,
    Table,
    Banner
  },
  data() {

    return {
        activeName: 'first',
        tableData: [],
        // 端到端高亮显示
        endToEndListActive:0,
        //端到端菜单
        endToEndList:[],
        filiale: [10001],
        filialeOptions: [{
          label:'北京公司',
          value:10001
        }],
        date:'',
        tabLists:[],
        loading:false,
        defaultDateTypeList:[],
        sendData:null,
        tableTitle:null

    };
  },
  computed: {
  },
  mounted() {
    this.getMenue();
  },
  methods: {

    handleClick(tab, event) {
      this.getTableData('endtoend-'+this.firstCode+'-'+tab.name, this.tabLists[parseInt(tab.index)], false);  
    },

    //   端到端菜单高亮显示
    getbendToEnd(endToEndListindex){
      this.endToEndListActive = endToEndListindex;
      this.tabLists = this.endToEndList[endToEndListindex].children;
      this.firstCode = this.endToEndList[endToEndListindex].code;
      this.$refs.carousel.setActiveItem('carousel'+endToEndListindex);
      if(endToEndListindex!=0){
        this.getTableData('endtoend-'+this.firstCode, undefined, false,this.endToEndList[endToEndListindex]);  
      } else {
        this.getTableData('endtoend-'+this.firstCode+'-'+this.activeName, undefined, false,this.endToEndList[endToEndListindex]);
      }
    },

    inputFocus() {
      setTimeout(() => {
        const elMain = document.querySelector('.el-main');
        elMain.addEventListener('scroll', () => {
          if (this.$refs.refHandle01) {
            this.$refs.refHandle01.dropDownVisible = false; // 监听值发生变化就关闭它
          }
        });
      }, 10);
    },

    onQuery(){
      this.getTableData(this.sendData.param,this.sendData.tab,this.sendData.flag,this.sendData.menue)
    },

    // 日期组件数据变化回调
    dateChange(val) {
      this.date = val;
      // this.getData();
    },

    // 获取视图
    getMenue(){
      this.loading = true;
      listApi({
        restSerialNo:'endtoend'
      }).then(res=>{
        console.log('caidan',res)
        if(res.code==200){
          this.endToEndList = res.data.children;
          this.firstCode = this.endToEndList[0].code;
          let tabLists = this.endToEndList[0].children;
          this.date = res.data.maxOpTime.split('-').join('');
          this.getTableData('endtoend-'+this.firstCode+'-'+tabLists[0].code, tabLists[0], true);
        }
      })
    },

    // 获取数据
    getTableData(param,tab,flag,menue){
      this.loading = true;
      this.sendData = {
        param,
        tab,
        flag,
        menue
      };
      listApi({
        restSerialNo:param,
        opTime:this.date.substring(0,4)+'-'+this.date.substring(4,6)
      }).then(res=>{
        if(res.code==200){
          let tableData = res.data.tableData;
          let obj = new Object();
          res.data.tableTitle.forEach(item=>{
              obj[item.code] = item.name;
          });
          this.tableTitle = obj;
          if(tab){
            tab.tableData = this.dealData(tableData);
          } else {
            menue.tableData = this.dealData(tableData);;
          }
          
          if(flag){
            this.tabLists = this.endToEndList[0].children;
            this.activeName = this.tabLists[0].code;
          }
          this.loading = false;
        }
      })
    },

    dealData(tableData) {
      let temArr = []
      tableData.forEach(item=>{
        item.children.forEach((item2,idx)=>{
          let arr = []
          if(item2.trend){
            arr = JSON.parse(item2.trend)
            arr.forEach((list)=>{
              list.unit = item2.unit;
            })
          }
          temArr.push({
            keyLink: item.name,
            id: item2.target_id,
            targetName: item2.target_name,
            thresholdValue: item2.threshold_value,
            targetValue: item2.target_value,
            sequential: item2.sequential,
            unit:item2.unit,
            trendList: arr,
            colNum: idx==0?item.children.length:0,
            parentCode: item.code,
            instruction: item2.instruction
          })
        })
      });
      return temArr;
    }
  },

  watch: {
    filiale() {
      if (this.filiale.length === 1) {
        // this.partment = this.filiale[0];
        [this.partment] = this.filiale;
      } else {
        // this.partment = this.filiale[1];
        [, this.partment] = this.filiale;
      }
      storage.sessionSave('filiale', this.filiale);
    },
  },
};
</script>

<style lang='less'>
.teleserve {

  display: flex;
  flex-direction: column;
  background: #fefefe;
  height: 1140px;


  .el-tabs__header{
      margin: 0;
  }
  .el-tabs__active-bar{
    position: absolute;
    bottom: 0;
    left: 10px;
    width: 50px !important;
    height: 3px;
    background-color: #FF9900;
    z-index: 1;
    -webkit-transition: -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), -webkit-transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    list-style: none;
  }
  .el-tabs__item.is-active{
      font-weight: bold;
      color: #757575;
  }
  .el-tabs__item {
    padding-left: 40px!important;
    height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    position: relative;
  }
  .el-tabs__content>.CHoperation{
      padding: 20px;
      box-sizing: border-box;
  }
  .el-table_10_column_50   .el-table__cell{
    padding: 0;
  }


  .cell{
    height: 32px;
    div{
      height: 100%;
      div{
        height: 100% !important;
        canvas{
              position: absolute;
              left: 0px;
              width: 213px;
              height: 22px;
              user-select: none;
              -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
              padding: 0px;
              margin: 0px;
              border-width: 0px;
        }
      }
    }
  }

  .echarts-inner-wrap-right {
      height: 100%;
      background-color: #fff;
  }
  .border-right-dashed {
    border-right: 1px dashed #e9e9e9;
  }
  .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    background: #fff;
  }

  .el-table td.el-table__cell {
    border-bottom: 1px dashed #e9e9e9;
  }

  .el-carousel__button {
    background-color: #f90;
    display: none;
  }

  .container {
    width:100%; 
    padding:30px 40px; 
    box-sizing:border-box; 
    background: #f0f0f0; 
    flex-grow: 2;
    display:flex; 
    flex-direction:column;
  }


  .el-table--medium .el-table__cell {
    padding: 0px;
  }

  .el-table .cell{
    height: auto;
  }


  .echarts-wrap {
      background: #f0f0f0;
      width: 100%;
      flex-grow:2;
      display: flex;
  }
  .echarts-inner-wrap-top {
      width: 100%;

      display: flex;

  }
  .echarts-inner-wrap-left {
      height: 100%;
      background-color: #fff;
      font-size: 16px;
      font-family: SourceHanSansSC-Regular;
  }
  .businessList {
    padding:0px;

    list-style: none;
    li{ 
        height: 60px;
        line-height: 60px;
        cursor: pointer;
        margin-bottom: 50px;
    }

   }
  .businessListactive{
      color: black;
      position: relative;
      font-family: SourceHanSansSC-Medium;
      box-sizing: border-box;

      &::before{
          content: '';
          position: absolute;
          left: 56px;
          bottom: 9px;
          right: auto;
          height: 4px;
          width: 45px;
          border-radius: 2px;
          background-color: #FF9900;
      }
  }


  .el-tabs__active-bar {
    height: 4px;
  }


  .partment-select {
    margin-right: 15px;
    input {
      background-color: #fff;
      border:1px solid #e6e6e6!important;
      color: #595959;
      &::placeholder {
        color: #595959;
      }
    }
    .el-select__caret {
      color: #595959 !important;
    }
  }

  .cascader {
    span {
      color: #595959;
    }
    .el-icon-arrow-down:before {
      content: "\E6E1";
    }
    .el-icon-arrow-down {
      transform: rotate(180deg);
    }
    .is-reverse.el-icon-arrow-down {
      transform: rotate(0deg);
    }
  }

  .el-tabs__nav.is-top {
    height: 60px;
    line-height: 60px;
  }

  .el-tabs__content {
    width:100%;
    background: #fff;
  }


  .el-table--enable-row-hover .el-table__body tr:hover>td {
      background-color: #ffebcc !important;
  }
  
}

</style>

