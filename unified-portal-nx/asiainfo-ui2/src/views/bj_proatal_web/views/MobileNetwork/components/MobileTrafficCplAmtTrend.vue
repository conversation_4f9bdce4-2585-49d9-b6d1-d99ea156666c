<template>
<div style="border-top: 1px solid rgba(140,140,140,0.2);">
  <div class="traffic-section scroll">
       <div class="traffic-section-scroll">
        <div v-for="(line,index) in 5" :key="line.targetId"
            :class="{'traffic-item':true,'border': index > 0}" >
          <!-- 报障原因折线图 -->
          <div class="traffic-title"><span>{{lineData[index] ? lineData[index].targetName || `焦点问题${index+1}` : `焦点问题${index+1}`}}</span></div>
          <line-smooth-chart style="height: 350px;" :data-zoom-end="0" :data-map="lineData[index]"/>
        </div>
      </div>
        <!-- 报障原因 -->
        <div class="traffic-section-scroll">
          <div v-for="(line,index) in 5" :key="line"
              :class="{'traffic-item':true,'border': index > 0}" >
            <!-- 报障原因折线图 -->
            <div class="traffic-title border-top"><span>网络责任原因焦点问题{{index+1}}TOP5</span></div>
            <horizontal-bar style="height: 220px;"  :data-map="netBarData[index] || {}"/>
          </div>
        </div>
        <div class="traffic-section-scroll">
          <div v-for="(line,index) in 5" :key="line"
              :class="{'traffic-item':true,'border': index > 0}" >
            <!-- 报障原因折线图 -->
            <div class="traffic-title border-top"><span>非网络责任原因焦点问题{{index+1}}TOP5</span></div>
            <horizontal-bar style="height: 220px;"  :data-map="notNetBarData[index] || {}"/>
          </div>
        </div>
        <!-- 报障原因集中地点 -->
        <div class="traffic-section-scroll">
          <div v-for="(line,index) in 5" :key="line"
              :class="{'traffic-item':true,'border': index > 0}" >
            <div class="traffic-title border-top"><span>焦点问题{{index+1}}集中地点TOP5</span></div>
            <el-table  border :data="tables[index] || []" height="221px" size="mini" style="border-right: none; border-bottom: none; width: 98%; margin: 0 auto;">
              <el-table-column label="区县" prop="innerCountyName" align="center"></el-table-column>
              <el-table-column label="小区名称" prop="villageName" show-overflow-tooltip></el-table-column>
              <el-table-column label="投诉量" prop="score" align="center"></el-table-column>
              <el-table-column label="占比" prop="proportion" align="center"></el-table-column>
              <el-table-column label="环比" prop="momrate" align="center"></el-table-column>
            </el-table>
          </div>
        </div>
  </div>
  <MobileTrafficAnaChart :param="anaParam" title="手机上网万投比"/>
</div>
</template>

<script>
import NoticePie from '../../HomeBroadband/components/NoticePie.vue'
import LineSmoothChart from './LineSmoothChart.vue'
import HorizontalBar from './HorizontalBar.vue'
import MobileTrafficAnaChart from './MobileTrafficAnaChart.vue'
import {getCentreGraphVOList,getDutyReasonInfoNetWork,getDutyReasonInfoNoNetwork,getCentralLocationInfo} from '@/api/insight/mobileApi.js'

export default {
  name:'MobileTrafficCplAmtTrend',
  props:{
    ajaxParam: Object,
    id: String
  },
  components:{
    LineSmoothChart,
    NoticePie,
    HorizontalBar,
    MobileTrafficAnaChart
  },
  data() {
    return {
      lineData:[],
      tables: [],
      net: [],
      notNet: [],
      netBarData: [],
      notNetBarData: [],
    }
  },
  computed:{
   anaParam() {
      const {statType,statDate} = this.ajaxParam;
      const [,targetId] = this.id.split('_');
      return {
        statType,
        statDate,
        targetId
      }
   }
  },
  methods:{
    getData() {
      if(!this.ajaxParam.statType || !this.ajaxParam.statDate) return;
      this.getCentreGraphVOList();
      this.getDutyReasonInfoNetWork();
      this.getDutyReasonInfoNoNetwork();
      this.getCentralLocationInfo();
    },
    // 5种曲线图查询
    async getCentreGraphVOList() {
      const {cityId,statType,startTime,statDate} = this.ajaxParam;
      const [parentTargetId] = this.id.split('_');
      const {code, data} = await getCentreGraphVOList({
        cityId,
        statType,
        startTime,
        statDate,
        parentTargetId
      })
      if(code == 200 && data){
        const ids = {};
        ['01','02','03','04','05'].forEach((item)=>{
          ids[`${parentTargetId}${item}`] = {};
        })
        data.forEach((item)=>{
          ids[item.targetId] = item;
        })
        this.lineData = Object.values(ids);
      }
    },
    processBarData(res) {
      const data = [[],[]];
      const aixs = [];
      res.forEach((item)=>{
        aixs.push(item.targetAlias);
        data[0].push(item.score);
        data[1].push((Number(item.proportion) * 100).toFixed(2));
      })

      return {
        yAxis:[
         [{name:'报障责任原因',data: aixs}],
         [
          {name:'投诉量',data:data[0]},
          {name:'占比',data:data[1],axisLabel:{formatter: '{value}%'}},
         ]
        ],
        data:data[0]
      }
    },
    getDutyReasonInfoNetWork() {
       const [parentTargetId] = this.id.split('_');
       const requestAry = Array.from({length:5}).map((r,index)=>{
         return getDutyReasonInfoNetWork({
          ...this.ajaxParam,
          targetId: `${parentTargetId}0${index+1}`
        })
       })
       Promise.all(requestAry).then((res)=>{
        const netBarData = [];
        res.forEach(({code,data},index)=>{
          if(code == 200){
            netBarData[index] = this.processBarData(data)
          }else{
            netBarData[index] = []
          }
        })
        this.netBarData = netBarData;
       }).catch(()=>{

       })
    },
    getDutyReasonInfoNoNetwork() {
       const [parentTargetId] = this.id.split('_');
       const requestAry = Array.from({length:5}).map((r,index)=>{
         return getDutyReasonInfoNoNetwork({
          ...this.ajaxParam,
          targetId: `${parentTargetId}0${index+1}`
        })
       })
      Promise.all(requestAry).then((res)=>{
        const notNetBarData = [];
        res.forEach(({code,data},index)=>{
          if(code == 200){
            notNetBarData[index] = this.processBarData(data)
          }else{
            notNetBarData[index] = []
          }
        })
        this.notNetBarData = notNetBarData;
       }).catch(()=>{

       })
    },
    getCentralLocationInfo() {
       const [parentTargetId] = this.id.split('_');
       const requestAry = Array.from({length:5}).map((r,index)=>{
         return getCentralLocationInfo({
          ...this.ajaxParam,
          targetId: `${parentTargetId}0${index+1}`
        })
       })
       Promise.all(requestAry).then((res)=>{
        res.forEach(({code,data},index)=>{
          if(code == 200){
            const dt = data.map((item)=>{
              item.momrate = item.momrate ? (Number(item.momrate) * 100).toFixed(2) + '%' : item.momrate;
              item.proportion = item.proportion ? (Number(item.proportion) * 100).toFixed(2) + '%' : item.proportion;
              return item;
            })
            this.tables[index] = dt
          }else{
            this.tables[index] = []
          }
        })
       }).catch(()=>{

       })
    }
  },
  created() {
    this.getData();
  },
  watch:{
    ajaxParam() {
      this.getData();
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./mobile-traffic.scss";
</style>
