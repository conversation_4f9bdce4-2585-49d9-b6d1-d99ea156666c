<template>
<div>
  <div class="traffic-top" style="align-items: flex-end;">
    <number-title num="02" text="重点投诉" />
    <DatePicker show-text @change="handleDateChange" :default-date="defaultDate"/>
  </div>
  <div class="traffic-title title-tabs">
    <span v-for="tb in tabs" :key="tb.key"
         :class="{'tab-active': activeTab == tb.key}"
         @click="handleChangeTab(tb.key)">{{tb.label}}</span>
  </div>
  <!-- 地图，万投比 -->
  <div class="traffic-section">
    <div class="traffic-item" style="flex: 0.85;" v-loading="mapLoading">
      <Map class="traffic-map-height" :title="mapTitle" :rank-colors="['#F59A23','#D7D7D7']"
           :map-data="mapData" :tooltip="[
             {labelField:'complainTargetName',field:'complainScore'},
             {labelField:'wanRatioTargetName',field:'wanRatioScore', decimal: 2}
           ]"
           @map-click="onMapClick"/>
    </div>
    <div class="traffic-item" style="display:flex; flex-direction: column;" v-loading="chartLoading">
      <div class="traffic-title title-tabs">
       <span v-for="tb in subTabs" :key="tb.key"
             :class="{'tab-active': activeSubTab == tb.key}"
             @click="handleChangeSubTab(tb)">{{tb.label}}</span>
      </div>
      <!-- 表格 -->
      <mobile-traffic-table :data="tableData" />
      <!-- 右侧柱状折线图 -->
      <line-bar-chart style="flex: 1;"
          :key="activeSubTab"
          :all-data="lineBarData"
          :legend-data="lineBarChartConfig.legend"
          :keymap="lineBarChartConfig.keyMap"
          :is-double-line="false"/>
    </div>
  </div>

  <component :is="component" :key="activeSubTab" :id="activeSubTab" :ajax-param="ajaxParam"/>
</div>
</template>

<script>
import NumberTitle from 'bj_src/components/common/numberTitle'
import DatePicker from 'bj_src/nx-components/date-picker/DatePicker.vue'
import Map from 'bj_src/nx-components/map/Map.vue'
import LineBarChart from "../../ComplainPrewarining/components/linebarChartsT.vue";
import MobileTrafficCplAmtTrend from './MobileTrafficCplAmtTrend.vue'
import MobileTrafficCplRepeat from './MobileTrafficCplRepeat.vue'
import MobileTrafficCplCover from './MobileTrafficCplCover.vue'
import {getComplainMapVO,getHistogramUpData,getHistogramList,getLatestStatDate} from '@/api/insight/mobileApi.js'
import utils from 'bj_src/utils/utils';
import MobileTrafficTable from './MobileTrafficTable.vue'
import Common from 'bj_src/lib/date'

export default {
  name:'MobileTrafficCplEmphasis',
  inject:['mapPermission'],
  components:{
    NumberTitle,
    DatePicker,
    Map,
    LineBarChart,
    MobileTrafficCplAmtTrend,
    MobileTrafficCplRepeat,
    MobileTrafficCplCover,
    MobileTrafficTable
  },
  data() {
    return {
      dateType :{
        '日': 1,
        '周': 2,
        '月': 3,
        '季度': 4
      },
      dates: [],
      defaultDate: {},
      map: {...this.mapPermission},
      mapTitle: '',
      mapData: [],
      tabs:[
        {label:'手机上网投诉分析', key: '201', subTabs:[
          {label:'手机上网投诉量和万投比趋势图',key:'20101_20102',component:'MobileTrafficCplAmtTrend',mapTitle:'手机上网投诉量地图'},
          {label:'重复投诉率分析',key:'20107_20103',component:'MobileTrafficCplRepeat',mapTitle:'重复投诉率地图'}
        ]},
        {label:'语音通话投诉分析', key: '202', subTabs:[
          {label:'语音通话投诉量和万投比趋势图',key:'20201_20202',component:'MobileTrafficCplAmtTrend',mapTitle:'语音通话投诉量地图'},
          {label:'重复投诉率分析',key:'20207_20203',component:'MobileTrafficCplRepeat',mapTitle:'重复投诉率地图'}
        ]},
        {label:'弱覆盖及公告', key: '203', subTabs:[
          {label:'弱覆盖引用量',key:'20301',component:'MobileTrafficCplCover',mapTitle:'弱覆盖引用量地图'},
          {label:'公告引用量',key:'20302',component:'MobileTrafficCplCover',mapTitle:'公告引用量地图'}
        ]},
      ],
      activeTab: '201',
      activeSubTab: '20101_20102',
      component:'MobileTrafficCplAmtTrend',
      lineBarData:[],
      tableData: [],
      mapLoading: false,
      chartLoading: false
    }
  },
  computed:{
    ajaxParam() {
      const [type,value] = this.dates;
      const {cityId} = this.map;
      const [startTime,statDate] = Array.isArray(value) ? value : type == '季度' ? [,value.replace('-0','-Q')] : [,value];
      return{
        cityId,
        statType: this.dateType[type],
        startTime,
        statDate
      }
    },
    subTabs() {
      const [{subTabs}] = this.tabs.filter(({key})=> key == this.activeTab);
      const [{key,component,mapTitle}] = subTabs;
      this.activeSubTab = key;
      this.mapTitle = mapTitle;
      this.component = component;
      return subTabs;
    },
    lineBarChartConfig() {
      switch(this.activeSubTab){
        case '20101_20102':
        case '20201_20202': return {legend:['手机上网投诉量','万投比'],keyMap:{ xData: 'statdate',seriesData: ['score', 'momrate']}}; break;
        case '20107_20103':
        case '20207_20203': return {legend:['重复投诉量','万投比'],keyMap:{ xData: 'statdate',seriesData: ['score', 'momrate']}}; break;
        case '20301': return {legend:['弱覆盖引用量'],keyMap:{ xData: 'statdate',seriesData: ['score']}}; break;
        case '20302': return {legend:['公告引用量'],keyMap:{ xData: 'statdate',seriesData: ['score']}}; break;
      }
    }
  },
  methods:{
    handleChangeTab(tab) {
      this.activeTab = tab;
      this.$nextTick(()=>{
         this.getData();
      })
    },
    handleChangeSubTab(tab) {
      this.activeSubTab = tab.key;
      this.component = tab.component;
      this.mapTitle = tab.mapTitle;
      this.getData();
    },
    handleDateChange(date) {
      this.dates = date;
      this.getData();
    },
    onMapClick(map) {
      this.map = {...map};
      this.getData();
    },
    getData() {
      this.getComplainMapVO();
      this.getHistogramList();
      this.getHistogramUpData();
    },
    // 地图数据
    async getComplainMapVO() {
      if(!this.ajaxParam.statType || !this.ajaxParam.statDate) return;
      this.mapData = [];
      this.mapLoading = true;
      try{
        const [complainNumTargetId,wanRatioTargetId] = this.activeSubTab.split('_');
        const {cityId,parentCityId,mapLevel} = this.map;
        const {code,data} = await getComplainMapVO({
          ...this.ajaxParam,
          complainNumTargetId,
          wanRatioTargetId,
          cityId: mapLevel < 3 ? cityId : parentCityId
        });
        if(code == 200 && data){
          this.mapData = data.map((item)=>{
            return {name: item.cityName, ...item}
          });
        }
        this.mapLoading = false;
      }catch(e){
        this.mapLoading = false;
      }
    },
     // 趋势图数据
    async getHistogramList() {
      if(!this.ajaxParam.statType || !this.ajaxParam.statDate) return;
      const [complainNumTargetId,wanRatioTargetId] = this.activeSubTab.split('_');
      try{
        this.chartLoading = true;
        const {code,data} = await getHistogramList({
          ...this.ajaxParam,
          complainNumTargetId,
          wanRatioTargetId,
        });
        if(code == 200 && data){
          const {complainNumList,wanRatioList} = data;
          const list = {};
          complainNumList.forEach(({statDate,score})=>{
            if(!list[statDate]) list[statDate] = {};
            list[statDate] ={score};
          })
          wanRatioList.forEach(({statDate,score})=>{
            if(!list[statDate]) list[statDate] = {};
            list[statDate]['momrate'] = score;
          })
          this.lineBarData = Object.entries(list).map(([key,values])=>{
            return {statdate: key, ...values}
          }).sort((a,b)=>{
            return a.statdate.localeCompare(b.statdate);
          })
        }
        this.chartLoading = false;
      }catch(e){
        this.chartLoading = false;
        this.lineBarData = [];
      }
    },
    async getHistogramUpData() {
      if(!this.ajaxParam.statType || !this.ajaxParam.statDate) return;
      const [complainNumTargetId,wanRatioTargetId] = this.activeSubTab.split('_');
      try{
        const {code,data} = await getHistogramUpData({
          ...this.ajaxParam,
          complainNumTargetId,
          wanRatioTargetId,
        });
        if(code == 200 && data){
          this.tableData = utils.handlerMomrateAndYoyrate(data);
        }
      }catch(e){
        this.tableData = [];
      }
    },
    async getLatestStatDate() {
      try{
        const {code,msg} = await getLatestStatDate();
        if(code == 200 && msg){
          this.dates = ['月',msg];
          this.defaultDate = {'月': msg+'-01'};
          this.$nextTick(()=>{
            this.getData();
          })
        }else{
          this.dates = ['月',Common.formatDate( new Date(), 'yyyy-MM')]
          this.$nextTick(()=>{
            this.getData();
          })
        }
      }catch(e){
        this.dates = ['月',Common.formatDate( new Date(), 'yyyy-MM')]
        this.$nextTick(()=>{
          this.getData();
        })
      }
    }
  },
  created() {
    this.getLatestStatDate();
  }
}
</script>

<style lang="scss" scoped>
@import "./mobile-traffic.scss";
</style>
