<template>
  <div style="width: 100%;">
    <div class="traffic-top">
      <number-title num="01" text="投诉分析" />
      <DatePicker show-text :default-date="defaultDate" @change="handleDateChange" />
    </div>
    <div class="traffic-section">
      <div v-loading="mapLoading" class="traffic-item" style="flex: 0.85;">
        <Map
          class="traffic-map-height"
          title="移动网络业务投诉量地图"
          :rank-colors="['#F59A23','#D7D7D7']"
          :map-data="mapData"
          :tooltip="[
            {labelField:'complainTargetName',field:'complainScore'},
            {labelField:'wanRatioTargetName',field:'wanRatioScore',decimal: 2}
          ]"
          @map-click="onMapClick"
        />
      </div>
      <div v-loading="chartLoading" class="traffic-item" style="display:flex; flex-direction: column;">
        <div class="traffic-title"><span>移动网络业务投诉量和万投比趋势图</span></div>
        <mobile-traffic-table :data="tableData" />
        <line-bar-chart
          style="flex: 1;"
          :all-data="lineBarData"
          :legend-data="['投诉量','万投比']"
          :keymap="{ xData: 'statdate',seriesData: ['score', 'momrate']}"
          :is-double-line="false"
        />
      </div>
    </div>
    <div class="traffic-section scroll">
      <div class="traffic-section-scroll" style="position:relative;">
        <div
          v-for="(line,index) in lineData"
          :key="`${line.targetId}`"
          :class="{'traffic-item':true,'border': index > 0}"
        >
          <div class="traffic-title"><span>{{ line.targetName || '' }}</span></div>
          <line-smooth-chart style="height: 350px;" :data-zoom-end="0" :data-map="line" />
        </div>
        <Blank2 v-if="!lineData.length" style="top:0; height: 350px;" />
      </div>
    </div>
    <mobile-traffic-ana-chart :param="anaParam" title="移动网络业务万投比" />
  </div>
</template>

<script>
import NumberTitle from 'bj_src/components/common/numberTitle'
import DatePicker from 'bj_src/nx-components/date-picker/DatePicker.vue'
import Map from 'bj_src/nx-components/map/Map.vue'
import LineSmoothChart from './LineSmoothChart.vue'
import LineBarChart from '../../ComplainPrewarining/components/linebarChartsT.vue'
import { getComplainMapVO, getHistogramUpData, getHistogramList, getCentreGraphVOList, getLatestStatDate } from '@/api/insight/mobileApi.js'
import utils from 'bj_src/utils/utils'
import MobileTrafficAnaChart from './MobileTrafficAnaChart.vue'
import MobileTrafficTable from './MobileTrafficTable.vue'
import Common from 'bj_src/lib/date'
import Blank from '../../../components/common/Blank.vue'

export default {
  name: 'MobileTrafficCplAna',
  components: {
    NumberTitle,
    DatePicker,
    Map,
    LineSmoothChart,
    LineBarChart,
    MobileTrafficAnaChart,
    MobileTrafficTable,
    Blank
  },
  inject: ['mapPermission'],
  data() {
    return {
      dateType: {
        '日': 1,
        '周': 2,
        '月': 3,
        '季度': 4
      },
      dates: [],
      defaultDate: {},
      map: { ...this.mapPermission },
      tableData: [],
      mapData: [],
      lineBarData: [],
      lineData: [],
      mapLoading: false,
      chartLoading: false
    }
  },
  computed: {
    ajaxParam() {
      const [type, value] = this.dates
      const { cityId } = this.map
      const [startTime, statDate] = Array.isArray(value) ? value : type == '季度' ? [, value.replace('-0', '-Q')] : [, value]
      return {
        cityId,
        statType: this.dateType[type],
        startTime,
        statDate,
        complainNumTargetId: '101', // 投诉量指标/引用量指标
        wanRatioTargetId: '102' // 万投比指标
      }
    },
    anaParam() {
      const { statType, statDate } = this.ajaxParam
      return {
        statType,
        statDate,
        targetId: '102'
      }
    }
  },
  created() {
    this.getLatestStatDate()
  },
  methods: {
    toFixDecimal(number) {
      if (!number) return
      return isNaN(Number(number)) ? number : Number(number).toFixed(2)
    },
    handleDateChange(date) {
      this.dates = [...date]
      this.getData()
    },
    onMapClick(map) {
      this.map = { ...map }
      this.getData()
    },
    getData() {
      this.getComplainMapVO()
      this.getHistogramList()
      this.getHistogramUpData()
      this.getCentreGraphVOList()
    },
    // 地图数据
    async getComplainMapVO() {
      if (!this.ajaxParam.statType || !this.ajaxParam.statDate) return
      this.mapData = []
      try {
        this.mapLoading = true
        const { cityId, parentCityId, mapLevel } = this.map
        const { code, data } = await getComplainMapVO({
          ...this.ajaxParam,
          cityId: mapLevel < 3 ? cityId : parentCityId
        })
        if (code == 200 && data) {
          this.mapData = data.map((item) => {
            return { name: item.cityName, ...item }
          })
        }
        this.mapLoading = false
      } catch (e) {
        this.mapLoading = false
      }
    },
    // 表格指标数据
    async getHistogramUpData() {
      if (!this.ajaxParam.statType || !this.ajaxParam.statDate) return
      const { code, data } = await getHistogramUpData({
        ...this.ajaxParam
      })
      if (code == 200 && data) {
        this.tableData = utils.handlerMomrateAndYoyrate(data)
      }
    },
    // 趋势图数据
    async getHistogramList() {
      if (!this.ajaxParam.statType || !this.ajaxParam.statDate) return
      try {
        this.chartLoading = true
        const { code, data } = await getHistogramList({
          ...this.ajaxParam
        })
        if (code == 200 && data) {
          const { complainNumList, wanRatioList } = data
          const list = {}
          complainNumList.forEach(({ statDate, score }) => {
            if (!list[statDate]) list[statDate] = {}
            list[statDate] = { score }
          })
          wanRatioList.forEach(({ statDate, score }) => {
            if (!list[statDate]) list[statDate] = {}
            list[statDate]['momrate'] = score
          })
          this.lineBarData = Object.entries(list).map(([key, values]) => {
            return { statdate: key, ...values }
          }).sort((a, b) => {
            return a.statdate.localeCompare(b.statdate)
          })
          console.log('this.lineBarData==>',this.lineBarData)
        }
        this.chartLoading = false
      } catch (e) {
        this.lineBarData = []
        this.chartLoading = false
      }
    },
    // 5种曲线图查询
    async getCentreGraphVOList() {
      const { cityId, statType, startTime, statDate } = this.ajaxParam
      if (!statType || !statDate) return
      const { code, data } = await getCentreGraphVOList({
        cityId,
        statType,
        startTime,
        statDate,
        parentTargetId: '101'
      })
      if (code == 200 && data) {
        this.lineData = data
      }
    },
    async getLatestStatDate() {
      try {
        const { code, msg } = await getLatestStatDate()
        if (code == 200 && msg) {
          this.dates = ['月', msg]
          this.defaultDate = { '月': msg + '-01' }
          this.$nextTick(() => {
            this.getData()
          })
        } else {
          this.dates = ['月', Common.formatDate(new Date(), 'yyyy-MM')]
          this.$nextTick(() => {
            this.getData()
          })
        }
      } catch (e) {
        this.dates = ['月', Common.formatDate(new Date(), 'yyyy-MM')]
        this.$nextTick(() => {
          this.getData()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./mobile-traffic.scss";
</style>
