<template>
  <div class="mobile-index">
    <Banner title="手机网络专题" desc="手机网络专题展示" />
    <div class="content">
      <div class="service-header">
        <NumberTitle num="01" text="客户感知" />
        <div class="header-search">
          <div class="block">
            <span class="demonstration">选择时间：</span>

            <!-- <el-select

              v-model="defaultType"
              style="width: 65px"
              placeholder="请选择"
            >
              <el-option label="月" value="3" />
            </el-select> -->

            <el-date-picker
              v-model="dateValue1"
              :picker-options="pickerOptions_startTime"
              style="margin-left: 0px; width: 220px"
              type="monthrange"
              range-separator="-"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              :default-time="['08:00:00', '08:00:00']"
              @change="monthRange($event)"
            />
          </div>
        </div>
      </div>
      <el-tabs
        v-model="tabTopActive"
        class="tob-tabs"
        @tab-click="switchTab(tabTopActive)"
      >
        <el-tab-pane
          v-for="item in tabTop"
          :key="item.targetId"
          :label="item.targetName"
          :name="item.targetId"
        />
      </el-tabs>
      <el-card class="service-content">
        <el-row style="height: 556px" class="charts">
          <el-col :span="12" class="box-border">
            <mapChart
              ref="mapChart"
              style="width: 99%; height: 500px; float: left"
            />
            <div class="cus-border" />
          </el-col>

          <el-col :span="12" class="box-border">
            <div v-if="tabTopActive === '601'">
              <radarChart
                ref="radarChart601"
                style="width: 100%; height: 280px; margin-top: 24px"
              />
              <barChart
                ref="barChart601"
                style="width: 100%; height: 280px"
                label="满意度"
              />
            </div>

            <el-tabs
              v-show="tabTopActive === '602'"
              v-model="tab602Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab602Active)"
            >
              <template v-for="(item, index) in tab602">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab602Components[index].component[idx]"
                    v-for="(i2, idx) in tab602Components[index].component"
                    v-if="tab602Active === item.targetId"
                    :key="idx"
                    :ref="tab602Components[index].ref[idx]"
                    :style="tab602Components[index].style[idx]"
                    :label="tab602Components[index].label[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-col>
        </el-row>
      </el-card>

      <div class="block" style="text-align: right; margin: 24px 0 0 0">
        <time-select
          ref="time_select_1"
          :t_id="'time_select_1'"
          class="modes-select"
          :compain-mode="true"
          :city-level="3"
          @checkDate="checkDate"
        />
      </div>

      <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab603Title }}</span>
              <el-button
                v-show="showExportBtn"
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab603Active)"
              >导出</el-button>
            </div>

            <el-tabs
              v-model="tab603Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab603Active)"
            >
              <template v-for="(item, index) in tab603">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab603Components[index].component[idx]"
                    v-for="(i2, idx) in tab603Components[index].component"
                    v-if="tab603Active === item.targetId"
                    :key="idx"
                    :ref="tab603Components[index].ref[idx]"
                    :label="tab603Components[index].label[idx]"
                    :style="tab603Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12" class="box-border" style="padding-left: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab606Title }}</span>
              <el-button

                v-show="tab606Active !== '60603' && showExportBtn"
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab606Active)"
              >导出</el-button>
            </div>
            <el-tabs
              v-model="tab606Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab606Active)"
            >
              <template v-for="(item, index) in tab606">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab606Components[index].component[idx]"
                    v-for="(i2, idx) in tab606Components[index].component"
                    v-if="tab606Active === item.targetId"
                    :ref="tab606Components[index].ref[idx]"
                    :label="tab606Components[index].label[idx]"
                    :style="tab606Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
      <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab605Title }}</span>
              <el-button
                v-show="showExportBtn"
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab605Active)"
              >导出</el-button>
            </div>
            <el-tabs
              v-model="tab605Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab605Active)"
            >
              <template v-for="(item, index) in tab605">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab605Components[index].component[idx]"
                    v-for="(i2, idx) in tab605Components[index].component"
                    v-if="tab605Active === item.targetId"
                    :ref="tab605Components[index].ref[idx]"
                    :label="tab605Components[index].label[idx]"
                    :style="tab605Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12" class="box-border" style="padding-left: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab604Title }}</span>
              <el-button
                v-show="showExportBtn"
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab604Active)"
              >导出</el-button>
            </div>
            <el-tabs
              v-model="tab604Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab604Active)"
            >
              <template v-for="(item, index) in tab604">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab604Components[index].component[idx]"
                    v-for="(i2, idx) in tab604Components[index].component"
                    v-if="tab604Active === item.targetId"
                    :ref="tab604Components[index].ref[idx]"
                    :label="tab604Components[index].label[idx]"
                    :style="tab604Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
      <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab607Title }}</span>
              <el-button
                v-show="showExportBtn"
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab607Active)"
              >导出</el-button>
            </div>
            <el-tabs
              v-model="tab607Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab607Active)"
            >
              <template v-for="(item, index) in tab607">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab607Components[index].component[idx]"
                    v-for="(i2, idx) in tab607Components[index].component"
                    v-if="tab607Active === item.targetId"
                    :ref="tab607Components[index].ref[idx]"
                    :label="tab607Components[index].label[idx]"
                    :style="tab607Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>

      <div style="position: relative">
        <NumberTitle num="02" text="业务投诉" />
        <div
          class="block"
          style="margin: 24px 0px 0px; position: absolute; right: 0; top: 8px"
        >
          <time-select-single
            ref="time_select_2"
            :t_id="'time_select_2'"
            style="justify-content: flex-end"
            class="modes-select"
            :compain-mode="true"
            :city-level="2"
            @checkDate="checkDateSingle"
          />
        </div>
      </div>
      <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab609Title }}</span>
            </div>
            <pie-chart
              ref="chart609"
              width="100%"
              style="height: 370px"
              :datas="[]"
              text=""
            />
          </el-card>
        </el-col>
        <el-col :span="12" class="box-border" style="padding-left: 17px">
          <el-card class="box-card">

            <div slot="header" class="clearfix header-title">
              <span>{{ tab608Title }}</span>
            </div>
            <bar-chart-top
              ref="chart608"
              width="100%"
              style="height: 370px"
              :datas="[]"
              text=""
            />
          </el-card>
        </el-col>
      </el-row>
      <!--       <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab608Title }}</span>
            </div>
            <bar-chart-top
              width="100%"
              ref="chart608"
              style="height: 370px"
              :datas="[]"
              text=""
            />
          </el-card>
        </el-col>
      </el-row> -->
      <div style="position: relative">
        <NumberTitle num="03" text="网络质量" />
        <div
          class="block"
          style="margin: 24px 0px 0px; position: absolute; right: 0; top: 8px"
        >
          <time-select
            ref="time_select_3"
            :t_id="'time_select_3'"
            style="justify-content: flex-end"
            class="modes-select"
            :compain-mode="true"
            :city-level="2"
            @checkDate="checkDate"
          />
        </div>
      </div>

      <el-row style="margin-top: 24px">
        <el-col :span="12" class="box-border" style="padding-right: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab611Title }}</span>
              <!--               <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab611Active)"
                >导出</el-button
              > -->
            </div>
            <el-tabs
              v-model="tab611Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab611Active)"
            >
              <template v-for="(item, index) in tab611">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab611Components[index].component[idx]"
                    v-for="(i2, idx) in tab611Components[index].component"
                    v-if="tab611Active === item.targetId"
                    :ref="tab611Components[index].ref[idx]"
                    :label="tab611Components[index].label[idx]"
                    :style="tab611Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="12" class="box-border" style="padding-left: 17px">
          <el-card class="box-card">
            <div slot="header" class="clearfix header-title">
              <span>{{ tab612Title }}</span>
              <!--               <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="handleExport(tab612Active)"
                >导出</el-button
              > -->
            </div>
            <el-tabs
              v-model="tab612Active"
              style="margin: 8px 24px 0 24px"
              @tab-click="switchTab(tab612Active)"
            >
              <template v-for="(item, index) in tab612">
                <el-tab-pane
                  :key="item.targetId"
                  :label="item.targetName"
                  :name="item.targetId"
                >
                  <component
                    :is="tab612Components[index].component[idx]"
                    v-for="(i2, idx) in tab612Components[index].component"
                    v-if="tab612Active === item.targetId"
                    :ref="tab612Components[index].ref[idx]"
                    :label="tab612Components[index].label[idx]"
                    :style="tab612Components[index].style[idx]"
                  />
                </el-tab-pane>
              </template>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { eventBus } from '@/main.js'
import Banner from './../../components/common/Banner.vue'
import NumberTitle from '../../components/common/numberTitle'
import TimeSelect from './components/timeSelect.vue'
import TimeSelectSingle from './components/timeSelectSingle.vue'
import mapChart from './components/mapChart.vue'
import barChart from './components/barChart.vue'
import barChartBlue from './components/barChartBlue.vue'
import radarChart from './components/radarChart.vue'
import PieChart from './components/pieChart.vue'
import barChartTop from './components/barChartTop.vue'
import barChartBlackPoint from './components/barChartBlackPoint.vue'
import {
  getAllTarget,
  exportTargetScoreInfo,
  getDefaultMonth
} from '@/api/insight/mobileApi.js'

export default {
  components: {
    Banner,
    NumberTitle,
    radarChart,
    mapChart,
    barChart,
    TimeSelect,
    barChartBlue,
    TimeSelectSingle,
    PieChart,
    barChartTop,
    barChartBlackPoint
  },
  inject: ['showExportBtn', 'mapPermission'],
  data() {
    return {
      map: { ...this.mapPermission },
      defaultType: '3',
      cityId: '',
      dateValue1: [
        this.parseTime(new Date(new Date().getTime()), '{y}-01'),
        this.parseTime(new Date(new Date().getTime()), '{y}-{m}')
        // "2022-06",
      ],
      pickerOptions_startTime: {
        disabledDate(time) {
          const bb = new Date()
          bb.setFullYear(bb.getFullYear() - 1)
          return time.getTime() > Date.now()
        }
      },
      // 全量target ,仅为导出用
      allTarget: [],

      tabTop: [],
      tabTopActive: '',

      tab602: [],
      tab602Active: '',
      tab602Components: [
        {
          targetId: 602,
          component: ['barChart'],
          style: ['width: 100%; height: 550px'],
          ref: ['barChart602'],
          label: ['满意度']
        },
        {
          targetId: 60201,
          component: ['radarChart', 'barChart'],
          style: ['width: 100%; height: 260px', 'width: 100%; height: 260px'],
          ref: ['radarChart60201', 'barChart60201'],
          label: ['满意度', '满意度']
        },
        {
          targetId: 60202,
          component: ['radarChart', 'barChart'],
          style: ['width: 100%; height: 260px', 'width: 100%; height: 260px'],
          ref: ['radarChart60202', 'barChart60202'],
          label: ['满意度', '满意度']
        }
      ],

      searchOpt01: {},
      // 移动网络质量
      tab603: [],
      tab603Title: '',
      tab603Active: '',
      tab603Components: [
        {
          targetId: 60301,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60301'],
          label: ['万投比']
        },
        {
          targetId: 60302,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60302'],
          label: ['万投比']
        },
        {
          targetId: 60303,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60303'],
          label: ['万投比']
        }
      ],

      // 黑点
      tab606: [],
      tab606Title: '',
      tab606Active: '',
      tab606Components: [
        {
          targetId: 60601,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60601'],
          label: ['关联率']
        },
        {
          targetId: 60602,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60602'],
          label: ['出库率']
        },
        {
          targetId: 60603,
          component: ['barChartBlackPoint'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60603'],
          label: ['关联率']
        }
      ],

      // 投诉工单管理
      tab605: [],
      tab605Title: '',
      tab605Active: '',
      tab605Components: [
        {
          targetId: 60501,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60501'],
          label: ['及时率']
        },
        {
          targetId: 60502,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60502'],
          label: ['处理时长']
        },
        {
          targetId: 60503,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60503'],
          label: ['解决率']
        }
      ],

      // 投诉工单管理
      tab604: [],
      tab604Title: '',
      tab604Active: '',
      tab604Components: [
        {
          targetId: 60401,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60401'],
          label: ['满意度']
        },
        {
          targetId: 60402,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60402'],
          label: ['投诉率']
        },
        {
          targetId: 60403,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60403'],
          label: ['投诉率']
        }
      ],

      // 现场测试
      tab607: [],
      tab607Title: '',
      tab607Active: '',
      tab607Components: [
        {
          targetId: 60701,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60701'],
          label: ['及时率']
        },
        {
          targetId: 60702,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart60702'],
          label: ['处理时长']
        }
      ],

      searchOpt02: {},

      tab608Title: '',
      tab609Title: '',
      tab610Title: '投诉原因排名',

      searchOpt03: {},
      // 4G网络
      tab611: [],
      tab611Title: '',
      tab611Active: '',
      tab611Components: [
        {
          targetId: 61101,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61101'],
          label: ['优良率']
        },
        {
          targetId: 61102,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61102'],
          label: ['优良率']
        },
        {
          targetId: 61103,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61103'],
          label: ['优良率']
        }
      ],

      // 5G网络
      tab612: [],
      tab612Title: '',
      tab612Active: '',
      tab612Components: [
        {
          targetId: 61201,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61201'],
          label: ['驻留比']
        },
        {
          targetId: 61202,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61202'],
          label: ['覆盖率']
        },
        {
          targetId: 61203,
          component: ['barChartBlue'],
          style: ['width: 100%; height: 300px'],
          ref: ['chart61203'],
          label: ['质差比']
        }
      ]
    }
  },
  created() {
    // 查询最近的一个有数据的月份
    getDefaultMonth().then(res => {
      console.log('查询最近的一个有数据的月份:', res)
      if (res.code == 200 && res.data) {
        console.log(this.dateValue1)
        const startYear = this.dateValue1[0].slice(0, 4)
        const maxDataMonthYear = res.data.maxDataMonth.slice(0, 4)
        if (startYear > maxDataMonthYear) {
          this.dateValue1.splice(0, 1, `${maxDataMonthYear}-01`)
        }

        // this.dateValue1[1] = res.data.maxDataMonth
        this.dateValue1.splice(1, 1, res.data.maxDataMonth)
        console.log(this.dateValue1)
      }
    })
    const { cityId, parentCityId, mapLevel } = this.map

    const _self = this
    eventBus.$off('msgChangeRadar601')
    eventBus.$on('msgChangeRadar601', (msg) => {
      _self.cityId = msg.cityId
      // _self.$refs.radarChart601.refreshData(msg);
    })

    // 下钻时要更新雷达图
    eventBus.$off('msgChangeBar')
    eventBus.$on('msgChangeBar', (msg) => {
      if (msg.saveCity) {
        _self.cityId = msg.cityId
      }
      msg.targetId = _self.tab602Active
      if (_self.tabTopActive === '601') {
        msg.targetId = '601'
        _self.$refs['barChart' + msg.targetId].getBarData(msg)
        if (msg.refreshRadar) {
          if (mapLevel == 3) {
            msg.cityId = parentCityId
          }
          _self.$refs['radarChart' + msg.targetId].refreshData(msg)
        }
      } else if (_self.tabTopActive === '602') {
        msg.targetId = _self.tab602Active

        if (msg.targetId !== '602') {
          if (msg.refreshRadar) {
            if (mapLevel == 3) {
              msg.cityId = parentCityId
            }
            _self.$refs['radarChart' + msg.targetId][0].refreshData(msg)
          }
        }
        _self.$refs['barChart' + msg.targetId][0].getBarData(msg)
      }
    })
  },
  mounted() {
    const _self = this

    getAllTarget({}).then((res) => {
      this.allTarget = res.data

      const data = res.data
      _self.tabTop = [data[0], data[1]]
      _self.tabTopActive = data[0].targetId
      _self.tab602 = data[0].children
      // 移动网络质量
      _self.tab603 = data[2].children
      _self.tab603Title = data[2].targetName
      _self.tab603Active = data[2].children[0].targetId
      // 黑点
      _self.tab606 = data[3].children
      _self.tab606Title = data[3].targetName
      _self.tab606Active = data[3].children[0].targetId
      // 投诉工单管理
      _self.tab605 = data[4].children
      _self.tab605Title = data[4].targetName
      _self.tab605Active = data[4].children[0].targetId
      // 投诉工单
      _self.tab604 = data[5].children
      _self.tab604Title = data[5].targetName
      _self.tab604Active = data[5].children[0].targetId
      // 现场测试
      _self.tab607 = data[6].children
      _self.tab607Title = data[6].targetName
      _self.tab607Active = data[6].children[0].targetId

      _self.tab608Title = data[7].targetName
      _self.tab609Title = data[8].targetName
      // _self.tab610Title = data[9].targetName;

      // 4G
      _self.tab611 = data[9].children
      _self.tab611Title = data[9].targetName
      _self.tab611Active = data[9].children[0].targetId
      // 5G
      _self.tab612 = data[10].children
      _self.tab612Title = data[10].targetName
      _self.tab612Active = data[10].children[0].targetId

      // 加载地图
      eventBus.$emit('msgChangeMap', {
        targetId: '601',
        startTime: _self.dateValue1[0],
        endTime: _self.dateValue1[1]
      })

      // 触发时间选择
      setTimeout(() => {
        this.$refs.time_select_1.checkDate()
        this.$refs.time_select_2.checkDate()
        this.$refs.time_select_3.checkDate()
      }, 1000)
    })
  },
  methods: {
    lookForAll(data = [], arr = []) {
      for (const item of data) {
        arr.push(item)
        if (item.children && item.children.length) { this.lookForAll(item.children, arr) }
      }
      return arr
    },
    handleExport(targetId) {
      let params = {
        parentId: targetId.substring(0, 3),
        targetId: targetId
      }

      if (targetId.includes('611') || targetId.includes('612')) {
        params = { ...params, ...this.searchOpt03 }
      } else {
        params = { ...params, ...this.searchOpt01 }
      }
      params.cityId = params.city
      params.statType = params.mode
      switch (params.mode) {
        case '1':
          params.startTime = params.day[0]
          params.endTime = params.day[1]
          break
        case '2':
          params.startTime = ''
          params.endTime = params.week
          break
        case '3':
          params.startTime = params.month[0]
          params.endTime = params.month[1]
          break
      }

      this.$confirm('是否确认导出数据项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return exportTargetScoreInfo(params)
        })
        .then((response) => {
          // window.open(response);
          const currenTarget = this.lookForAll(this.allTarget).filter(
            (item) => item.targetId === targetId
          )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              currenTarget.targetName + '.xls'
            )
            return false
          }

          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', currenTarget.targetName + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    },

    handleMsg(targetId, opt) {
      if (!targetId) return false
      const _self = this
      const msg = {
        targetId: targetId,
        statType: opt.mode,
        cityId: opt.city
      }

      // 日 周 月
      switch (opt.mode) {
        case '1':
          msg.startTime = opt.day[0]
          msg.endTime = opt.day[1]
          break
        case '2':
          msg.startTime = ''
          msg.endTime = opt.week
          break
        case '3':
          msg.startTime = opt.month[0]
          msg.endTime = opt.month[1]
          break
      }
      setTimeout(() => {
        _self.$refs['chart' + targetId][0].getBarData(msg)
      }, 200)
    },

    checkDate(opt) {
      if (opt.t_id === 'time_select_1') {
        this.searchOpt01 = opt
        this.handleMsg(this.tab603Active, opt)
        this.handleMsg(this.tab606Active, opt)
        this.handleMsg(this.tab605Active, opt)
        this.handleMsg(this.tab604Active, opt)
        this.handleMsg(this.tab607Active, opt)
      } else if (opt.t_id === 'time_select_3') {
        this.searchOpt03 = opt
        this.handleMsg(this.tab611Active, opt)
        this.handleMsg(this.tab612Active, opt)
      }

      // 刷新图表
      // this.$message("这是一条消息提示");
    },

    checkDateSingle(opt) {
      const msg = {
        statType: opt.mode,
        statDate: opt.month,
        cityId: opt.city
      }

      // 日 周 月
      switch (opt.mode) {
        case '1':
          msg.statDate = opt.day
          break
        case '2':
          msg.statDate = opt.week
          break
        case '3':
          msg.statDate = opt.month
          break
      }
      this.$refs['chart609'].refreshData({ ...msg, ...{ targetId: '609' }})
      // this.$refs["chart610"].refreshData({ ...msg, ...{ targetId: "609" } });
      this.$refs['chart608'].refreshData({ ...msg, ...{ targetId: '608' }})
    },

    // 获取全量指标
    switchTab(targetId) {
      const _self = this
      switch (targetId) {
        case '601':
          this.tab602 = this.tabTop[0].children
          // this.tab602Active = "";

          // 发送消息给地图组件
          eventBus.$emit('msgChangeMap', {
            targetId: '601',
            startTime: this.dateValue1[0],
            endTime: this.dateValue1[1],
            // 强制出发地图msgChangeBar事件，解决tab切换，地图处于3级地市，雷达图不刷新的问题
            __emitMsgChangeBar: true
          })

          break
        case '602':
          this.tab602 = this.tabTop[1].children
          this.tab602Active = this.tabTop[1].children[0].targetId
          // 刷新地图
          eventBus.$emit('msgChangeMap', {
            targetId: '602',
            startTime: this.dateValue1[0],
            endTime: this.dateValue1[1],
            // 强制出发地图msgChangeBar事件，解决tab切换，地图处于3级地市，雷达图不刷新的问题
            __emitMsgChangeBar: true
          })
          break
        case '60201':
        case '60202':
          var msg = {
            cityId: _self.cityId,
            targetId: targetId,
            startTime: _self.dateValue1[0],
            endTime: _self.dateValue1[1],
            statType: '3'
          }

          // 刷新地图
          eventBus.$emit('msgChangeMap', {
            targetId: targetId,
            startTime: this.dateValue1[0],
            endTime: this.dateValue1[1],
            // 强制出发地图msgChangeBar事件，解决tab切换，地图处于3级地市，雷达图不刷新的问题
            __emitMsgChangeBar: true
          })

          // setTimeout(() => {
          //   _self.$refs["barChart" + targetId][0].getBarData(msg);
          //   _self.$refs["radarChart" + targetId][0].refreshData(msg);
          // }, 200)
          break
        default:
          if (targetId.includes('611') || targetId.includes('612')) {
            _self.handleMsg(targetId, _self.searchOpt03)
          } else {
            _self.handleMsg(targetId, _self.searchOpt01)
          }
          break
      }
    },
    monthRange(val) {
      // 发送数据给地图
      eventBus.$emit('msgChangeMap', {
        targetId: this.tabTopActive,
        startTime: this.dateValue1[0],
        endTime: this.dateValue1[1],
        // 强制出发地图msgChangeBar事件，解决tab切换，地图处于3级地市，雷达图不刷新的问题
        __emitMsgChangeBar: true
      })
    },

    handleChangeCity() {}
  }
}
</script>
<style lang='less'>
.mobile-index {
  .content {
    padding: 30px 50px 40px 50px;
    box-sizing: border-box;
    width: 100%;
    background: #f0f0f0;

    .el-card__header button {
      float: right;
      padding: 8px 16px !important;
      border: 1px solid #ccc;
      color: #262626;
    }
    .el-card__header button:hover {
      color: #ff9900;
      border-color: #ff9900;
    }
    .el-button--primary {
      background-color: #ff9900;
      border-color: #ff9900;
      color: #fff;
      font-family: "PingFangSC-Regular", "PingFang SC", sans-serif;
      font-size: 18px;
    }

    /*复用  */
    .header-title {
      font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC",
        sans-serif;
      font-weight: bold;
      font-size: 16px;
      line-height: 32px;
    }

    .cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }

    .box-border {
      height: 100%;
      /*       border: 1px solid #dddddd; */
    }

    .el-card__body {
      padding: 1px;
    }

    .el-card__header {
      border-bottom: none;
      padding-left: 16px;
    }

    .el-tabs.el-tabs--top {
      // margin: 8px 14px 0px !important;
    }

    .el-tabs__item:hover {
      color: #303133;
      font-weight: bold;
      cursor: pointer;
    }
    .el-tabs__item.is-active {
      color: #303133;
      font-weight: bold;
    }

    .el-tabs__active-bar {
      height: 3px;
      background-color: #ffbd02;
    }

    .service-header {
      position: relative;

      .header-search {
        position: absolute;
        right: 0;
        top: 60px;
      }
    }
    .service-content {
      margin-top: 16px;
      .el-card__header {
        padding: 14px 15px 0 8px;
        min-height: 40px;
      }

      .charts {
        .charts-tab {
          font-family: "PingFangSC-Semibold", "PingFang SC Semibold",
            "PingFang SC", sans-serif;
          font-weight: bold;
          font-size: 16px;
          color: #555555;
          text-align: center;
          line-height: 22px;
          overflow: hidden;

          .charts-tab-li {
            float: left;
            list-style-type: none;
            padding: 8px 0;
            margin: 0 8px;
            cursor: pointer;
          }

          .charts-tab-li.active {
            border-bottom: 3px solid #ffbd02;
          }
        }
      }
    }
  }
}
</style>
<style>
.el-select-dropdown__wrap {
  margin-bottom: 0 !important;
}
::v-deep .el-cascader-node > .el-radio {
  width: 0px;
  display: none !important;
}
</style>
