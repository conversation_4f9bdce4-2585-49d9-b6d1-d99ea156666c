<template>
  <div class="communityComplaints">
    <NumberTitle num="01" text="报障小区分析" />
    <div class="con">
      <div style="position:absolute;right:10px;top:-60px">
        <DatePicker :key="2" :default-date="defaultDate" @change="checkDate" />
      </div>
      <!-- 地图 -->
      <div v-loading="topconloading" class="flex mapbox">
        <div
          style="position:absolute;height:450px;width:1px;background:#ddd;top:5%;left:48%;opacity:0.5"
        />
        <div v-loading="maploading" style="height:500px;position:relative;">
          <!-- <Map ref="map" :regions="regions" @mapClick="mapClick" /> -->
          <Map
            ref="map"
            :rank-colors="['rgb(253,135,5)','#D1ECD4']"
            :map-data="regions"
            :active-name="topName"
            @map-click="mapClick"
          />
          <div class="pt" style="position:absolute;left:0px;top:25px;">{{ topName }}地图</div>
        </div>
        <!-- 右侧柱状图 -->
        <div v-loading="topconloading" style="padding-top:15px;padding-right:15px">
          <el-tabs key="1" v-model="topName" style="flex: 1" @tab-click="handleClickTab">
            <el-tab-pane
              v-for="item in tabList"
              :key="item.target_name"
              :label="item.target_name"
              :name="item.target_name"
            >
              <div
                style="padding-bottom: 10px;padding-left: 20px;display: flex;align-items: center;justify-content: space-around;height: 80px;"
              >
                <div>
                  <div class="la">报障量</div>
                  <div class="emVal">
                    {{ complainAndTrend.score||'-' }}
                    <span>件</span>
                    <img
                      :src=" complainAndTrend.deliveryratetype!=0? upImg:downImg "
                      width="10px"
                      style="margin-right: 0px;transform:rotate(180deg)"
                    >
                  </div>
                </div>
                <div>
                  <div class="la">万投比</div>
                  <div class="emVal">
                    {{ complainAndTrend.emos||'-' }}‱
                    <img
                      :src=" complainAndTrend.eomstype!=0?upImg:downImg "
                      width="10px"
                      style="margin-right: 0px;transform:rotate(180deg)"
                    >
                  </div>
                </div>
                <div>
                  <div class="la">出库率</div>
                  <div class="emVal">
                    {{ complainAndTrend.deliveryrate||'-' }}%
                    <img
                      :src=" complainAndTrend.deliveryratetype!=0?upImg:downImg "
                      width="10px"
                      style="margin-right: 0px;transform:rotate(180deg)"
                    >
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div style="height:380px;">
            <LineBar
              :all-data="chart1Data"
              :legend-data="['报障量','万投比','出库率']"
              :is-double-line="true"
              :keymap="{
                xData: 'statdate',
                seriesData: ['score','momrate','deliveryrate' ]
              }"
              @barclick="barclick"
            />
          </div>
        </div>
      </div>
      <!-- 地市区县分析 -->
      <div class="complaint-charts">
        <div class="complaint-chart-item">
          <div class="pt">{{ topName.slice(0,-5) }}万投比地市分析</div>
          <div v-loading="topconloading" class="anaylsis-chart">
            <!-- 暂无数据 -->
            <Blank2 v-show="dataCity.data.length === 0" class="complaint-blank" />
            <analysis-chart :data-map="dataCity" @isFull="dataCityFill" />
          </div>
        </div>
        <div class="complaint-chart-item">
          <div class="pt">{{ topName.slice(0,-5) }}万投比区县分析</div>
          <div v-loading="topconloading" class="anaylsis-chart">
            <!-- 暂无数据 -->
            <Blank2 v-show="dataDistrict.data.length === 0" class="complaint-blank" />
            <analysis-chart :data-map="dataDistrict" @isFull="dataDistrictFill" />
          </div>
        </div>
      </div>
    </div>
    <!-- 省内700报障小区报障量清单 -->
    <div v-loading="topconloading" class="con" style="min-height: 500px;padding-bottom:20px">
      <div
        style="width: 100%;height: 60px;align-items: center;display: flex;justify-content: space-between;padding:0 10px"
      >
        <div
          style=" font-weight: bold;font-size: 16px;line-height: 32px;"
        >{{ topName.slice(0,-5) }}报障量清单</div>
        <div style="display:flex; align-items: center;">
          <!-- 位置： -->
          <!-- <el-cascader
            ref="cascaderArr"
            v-model="idList"
            class="el-cascader"
            placeholder="请选择地市"
            :options="optionsCascader"
            :size="conditionsize"
            :props="props"
            @change="cityChange($event)"
          /> -->
          <AreaPicker :size="conditionsize" @change="areaChange" />

          <span style="font-size:14px">网格：</span>
          <el-select
            v-model="subId"
            :size="conditionsize"
            clearable
            style="margin-right:10px ;width:150px"
            placeholder="请选择"
            @change="gridIdChange"
          >
            <el-option
              v-for="item in subnameLisat"
              :key="item.subid"
              :label="item.subname"
              :value="item.subid"
            />
          </el-select>  <span style="font-size:14px">预警级别：</span>
          <el-select
            v-model="WarnLevel"
            :size="conditionsize"
            style="margin-right:10px ;;width:150px"
            placeholder="请选择"
            @change="levelChange"
          >
            <el-option
              v-for="item in WarnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
          <DatePickerJK ref="pickers" key="3" :default-date="defaultDate" @change="checkDate2" />
        </div>
      </div>
      <!-- 表格 -->
      <div v-loading="trendloading" class="table" style="padding:0 10px">
        <el-table
          :data="tableData"
          border
          max-height="83%"
          min-height="150px"
          :header-cell-style="{ 'background-color': 'rgb(255,222,172)' }"
          style=" border: 1px solid #dfe6ec;"
        >
          <el-table-column prop="indicateName" align="center" label="序号">
            <template slot-scope="scope">
              <span>{{ scope.$index + (currentPage-1) * pageSize + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="cityname" align="center" label="地市" />
          <el-table-column prop="countyname" align="center" label="区县" />
          <el-table-column prop="gridname" align="center" width="180" label="网格" />
          <el-table-column prop="estate_name" align="center" label="小区名称" />
          <el-table-column
            v-if="statType2==3"
            prop="trend[0].score"
            align="center"
            :label="`上月报障量`"
          />
          <el-table-column
            v-if="statType2==3"
            prop="score"
            align="center"
            :label="`本月报障量`"
          />
          <el-table-column
            v-if="statType2==3 && topName=='集团高频报障小区整体报障量'"
            prop="monthpz"
            align="center"
            :label="`拍照月`"
          />
          <el-table-column
            v-if="statType2==3 && topName=='集团高频报障小区整体报障量'"
            prop="monthpznum"
            align="center"
            :label="`拍照月投诉量`"
          />
          <el-table-column v-if="statType2==4" prop="score" align="center" label="上季度报障量" />
          <el-table-column
            v-if="statType2==4"
            prop="score"
            align="center"
            :label="`本季度报障量`"
          />
          <el-table-column v-if="statType2==1" prop="score" align="center" label="日报障量" />
          <el-table-column prop="amplitude" align="center" label="增降幅" />
          <el-table-column prop="level" align="center" label="预警级别（红黄绿）" />
        </el-table>
        <el-pagination
          layout="total,prev, pager, next"
          :page-size="8"
          background
          style="margin-top: 5px;display: flex;justify-content: flex-end;"
          :current-page.sync="currentPage"
          :total="total"
          @current-change="currentChange"
        />
      </div>
    </div>
    <!-- //遮罩层 -->
    <div :class="{ show: isFull, hide: !isFull }">
      <div id="fullScreen" style="  width: 90vw;  height: 90vh; background: #ffffff;" />
    </div>
  </div>
</template>
<script>
import { AreaByUser } from '@/api/mobilePhoneTariff'
import LineBar from './linebarChart.vue'
import {
  getRepeatMap,
  getObstacle,
  getObstacleChart,
  getRepeatDistrict,
  getGridData,
  getRepeatCity,
  getNewTime
} from '@/api/homebroadband/index.js'
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
// import Map from './mapComplaints.vue'
import Map from '@/views/bj_proatal_web/nx-components/map/Map.vue'
import AreaPicker from '@/views/bj_proatal_web/nx-components/area-picker/AreaPicker.vue'

import AnalysisChart from './AnalysisChartC.vue'
import tool from '@/views/bj_proatal_web/utils/utils'
import DatePicker from '../../contactor/components/DatePicker.vue'
import DatePickerJK from '../../contactor/components/DatePickerJK.vue'

export default {
  components: {
    NumberTitle,
    DatePicker,
    DatePickerJK,
    AreaPicker,
    tool,
    AnalysisChart,
    LineBar,
    Map
  },
  inject: ['showExportBtn', 'mapPermission'],
  data() {
    // 有问题需要进一步测试
    const year = new Date().getFullYear()
    let month = Number(new Date().getMonth()) + 1
    if (month < 10) {
      month = '-0' + month
    } else {
      month = '-' + month
    }
    return {
      chart: null, // 全屏柱状图
      upImg: require('@/assets/images/up-icon.png'),
      downImg: require('@/assets/images/down-icon.png'),
      pageSize: '8',
      // 折线图样式定义
      lineStyle: {
        yAxisIndex: 1,
        lineStyle: {
          color: '#f9906f'
        },
        itemStyle: {
          color: '#f9906f'
        }
      },
      dataZoomStyle: {
        handleSize: 0, // 滑动条的 左右2个滑动条的大小
        height: 8, // 组件高度
        left: 0, // 左边的距离
        right: 0, // 右边的距离
        bottom: 10, // 右边的距离
        handleColor: '#eee', // h滑动图标的颜色
        handleStyle: {
          borderColor: '#eee',
          borderWidth: '1',
          shadowBlur: 2,
          background: '#eee',
          shadowColor: '#eee'
        },
        backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
        showDataShadow: false, // 是否显示数据阴影 默认auto
        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        handleIcon:
          'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
        // filterMode: "filter",
        moveHandleStyle: { color: '#eee' }
      },
      defaultDate: {},

      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      WarnOptions: [
        {
          value: '0',
          label: '全部'
        },
        {
          value: '1',
          label: '红'
        },
        {
          value: '2',
          label: '黄'
        },
        {
          value: '3',
          label: '绿'
        }
      ], // 预警等级数据
      WarnLevel: '全部',
      conditionsize: 'small',
      complainAndTrend: {},
      idList: [this.mapPermission.cityId],
      dateType: {
        日: '1',
        月: '3',
        季度: '4'
      },
      currentPage: 1,
      isFull: false, // 控制是否展示全屏柱状图
      optionsCascader: [],
      tableData: [], // 表格数据
      tabList: [
        // {
        //   target_name: '省内700报障小区整体报障量',
        //   target_id: 'h007',
        //   tableId: 'h010'
        // },
        {
          target_name: '集团高频报障小区整体报障量',
          target_id: 'h011',
          tableId: 'h014'
        }
      ],
      subnameLisat: [], // 网格下拉数据
      subId: '', // 网格id
      tableId: 'h014', // 报障清单的指标id
      chart1Data: [],
      trendloading: false,
      dataCity: { xAxis: [], data: [] }, // 地市分析
      dataDistrict: { xAxis: [], data: [] }, // 区县分析
      topName: '集团高频报障小区整体报障量',
      targetId: 'h011',
      isLevel3: this.mapPermission.mapLevel == 3, // 是否是区县
      isLevel3Pid: this.mapPermission.mapLevel == 3 ? this.mapPermission.parentCityId : null,
      cityId: this.mapPermission.cityId,
      cityLevel: this.mapPermission.mapLevel,
      gridId: this.mapPermission.cityId,
      total: 0, // table的总条数
      maploading: false,
      topconloading: false,
      date: '',
      regions: [], // 地图数据
      mapDate: [],
      endTime: '',
      startDate: year + month,
      mapDate2: [],
      startDate2: year + month,
      startTime: '',
      statType: '3',
      statType2: '3', // table的时间类型及时间
      optime2: year + month,
      optime: year + month,
      // 当选择区县时记录 当清空网格时
      countyCache: null
    }
  },
  computed: {
    // mapDateParams() {
    //   const [type, value] = this.mapDate;
    //   this.statType = this.dateType[type];
    //   this.date = type === "日" ? "" : value;
    //   return {
    //     statType: this.dateType[type], // 1日 3月 4季度
    //     ...(type === "日"
    //       ? { opTime: "", startTime: value[0], endTime: value[1] }
    //       : { opTime: value, startTime: "2022-06-05", endTime: "2022-06-08" })
    //   };
    // }
  },
  created() {
    this.init() // 初始化获取城市区域
    // 查询最近的数据时间
    getNewTime({ targetId: this.targetId }).then(res => {
      if (res.code == 200 && res.data.data && Array.isArray(res.data.data)) {
        const statdate = res.data.data[0].statdate
        this.startDate = statdate
        this.defaultDate = {
          月: new Date(statdate.replace(/-/g, '/') + '/01')
        }
        this.startDate2 = statdate
        this.queryData()
        // this.$refs.date.dates[`${data}__date`] = res.data.data[0].statdate;
      }
    })
  },
  mounted() {
    if (this.mapPermission.mapLevel == 3) {
      const param = {
        cityId: this.mapPermission.name
      }
      getGridData(param).then(res => {
        if (res.code == 200 && res.data) {
          this.subnameLisat = res.data.data
        }
      })
    }
  },
  methods: {
    barclick(c) {
      // 点击柱状图table时间变化
      this.optime2 = c.xValue
      this.startDate2 = c.xValue
      const ll = [
        { label: '1', value: 'daterange' }, // date
        { label: '3', value: 'month' }, // monthrange
        { label: '4', value: 'quarter' }
      ]
      let data = ''
      ll.forEach(({ value, label }) => {
        if (this.statType == label) {
          data = value
        }
      })
      this.$refs.pickers.dates[`${data}__date`] = JSON.parse(
        JSON.stringify(this.optime2)
      )
      const p1 = this.getObstacleData() // 查询报障小区报障量清单
      this.topconloading = true
      Promise.all([p1]).then(values => {
        const res1 = values[0]
        if (res1.code == 200 && res1.data && res1.data.data) {
          const { first, second } = res1.data.data
          this.topconloading = false

          this.tableData = first

          this.total = Number(second[0].total) || 0
        }
      })
    },
    handleClickTab(val) {
      this.topName = val.name
      this.targetId = (
        this.tabList.find(el => el.target_name == val.name) || {}
      ).target_id
      this.tableId = (
        this.tabList.find(el => el.target_name == val.name) || {}
      ).tableId
      this.chart1Data = []
      this.complainAndTrend = {}
      this.dataCity = { xAxis: [], data: [] } // 地市分析
      this.dataDistrict = { xAxis: [], data: [] }
      this.queryData()
    },
    mapClick(city) {
      console.log('city==>', city)
      // this.cityId = city.id
      // const ids = ['640000', '640100', '640200', '640300', '640400', '640500']
      // if (ids.indexOf(city.id) == -1) {
      //   // 区县
      //   this.isLevel3Pid = city.pid
      // } else {
      //   this.isLevel3Pid = ''
      // }

      this.cityId = city.cityId
      const ids = ['640000', '640100', '640200', '640300', '640400', '640500']
      if (ids.indexOf(city.cityId) == -1) { // 区县
        this.isLevel3 = true
        this.isLevel3Pid = city.parentCityId
      } else {
        this.isLevel3 = false
        this.isLevel3Pid = ''
      }

      // 去获取相关的数据 并通知更新地图 主要是拼接地图的 regions 数据
      // 获取数据
      this.chart1Data = []
      this.complainAndTrend = {}
      this.dataCity = { xAxis: [], data: [] } // 地市分析
      this.dataDistrict = { xAxis: [], data: [] }
      this.queryData()
    },
    queryData() {
      const p1 = this.getMapRank2() // 查询地图及排名
      const p2 = this.getChart() // 获取右上柱状图
      const p3 = this.getCityCharts() // 获取地市分析
      const p4 = this.getDistrictCharts() // 获取区县分析
      const p5 = this.getObstacleData() // 查询报障小区报障量清单
      this.currentPage = 1
      this.topconloading = true
      Promise.all([p1, p2, p3, p4, p5])
        .then(values => {
          const res1 = values[0]
          const res2 = values[1]
          const res3 = values[2]
          const res4 = values[3]
          const res5 = values[4]
          if (
            res1.code == 200 &&
            res1.data.data &&
            Array.isArray(res1.data.data)
          ) {
            var temp = []
            res1.data.data.forEach((i, idx) => {
              const x = {
                cityId: i.cityid,
                targetName: i.rankname,
                name: i.cityname,
                score: tool.dealFloatNum(i.score),
                value: tool.dealFloatNum(i.score),
                targetname: this.topName
              }
              if (idx < 3) {
                x.itemStyle = { normal: { areaColor: 'rgb(253,135,5)' }}
                // 区县的情况
              }
              temp.push(x)
            })
            console.log('regions==>', temp)
            this.regions = temp
            // console.log(this.regions, 5555555);
          }
          if (res2.code == 200 && res2.data && res2.data.data) {
            const { chart, data } = res2.data.data
            const chart1Data = []
            if (chart.length > 0) {
              chart.forEach((i, dex) => {
                const obj = {
                  statdate: i.statdate,
                  deliveryrate: i.deliveryrate ? i.deliveryrate : '',
                  score: i.score, // 家庭业务投诉量
                  momrate: i.emos ? i.emos : '' // 万投比
                }
                chart1Data.push(obj)
              })
            }
            this.chart1Data = chart1Data
            if (data.length > 0) {
              this.complainAndTrend = data[0]
            } else {
              this.complainAndTrend = []
            }
          }
          if (res3.code == 200 && res3.data && res3.data.data) {
            const { first, second, third } = res3.data.data.data
            if (this.statType == 3 || this.statType == 1) {
              var ll = [].concat(first, second, third)
            } else if (this.statType == 4) {
              var ll = [].concat(first, second)
            }
            // const ll = [].concat(first, second, third);
            this.dataCity = this.processData(ll)
            console.log(this.dataCity, 888888888888888)
          }
          if (res4.code == 200 && res4.data && res4.data.data) {
            const { first, second, third } = res4.data.data.data
            if (this.statType == 3 || this.statType == 1) {
              var ls = [].concat(first, second, third)
            } else if (this.statType == 4) {
              var ls = [].concat(first, second)
            }
            this.dataDistrict = this.processData(ls)
            console.log(this.dataDistrict, 88888122188888888)
          }
          if (res5.code == 200 && res5.data && res5.data.data) {
            const { first, second } = res5.data.data

            this.tableData = first
            this.total = Number(second[0].total) || 0
          }
        })
        .finally(() => {
          this.topconloading = false
          this.trendloading = false
        })
        .catch(() => {
          this.topconloading = false
          this.trendloading = false
        })
    },
    getMapRank2() {
      const { cityId, targetId, cityLevel, isLevel3, isLevel3Pid } = this
      const param = {
        cityId: cityId,
        targetId: targetId,
        level: isLevel3 ? cityLevel - 1 : cityLevel,
        // ...this.mapDateParams
        opTime: this.startDate,
        statType: this.statType,
        startTime: this.startTime || '2022-06-05',
        endTime: this.endTime || '2022-06-08'
      }

      return getRepeatMap(param)
    },
    // 处理数据
    processData(dtAry) {
      const self = this
      const dateKey = {}
      const xAxis = []
      // 处理环比正负数
      const data = dtAry
      data.forEach(dt => {
        if (!dateKey[dt.statdate]) dateKey[dt.statdate] = {}
        dateKey[dt.statdate][dt.cityname] = dt
        if (!xAxis.includes(dt.cityname)) xAxis.push(dt.cityname)
      })
      // 图表类型
      const series = []
      Object.entries(dateKey).forEach(([key, values]) => {
        let name = ''
        if (self.statType == 3) {
          const [month] = key.split('-').reverse()
          name = Number(month) + '月'
          // 万投比
          series.push({
            name: name,
            type: 'bar',
            data: xAxis.map(cityname => {
              if (values[cityname]) {
                const { score } = values[cityname]
                return score ? Number(score).toFixed(2) : ''
              }
              return ''
            })
          })

          // 环比
          if (key === this.startDate) {
            series.push({
              name: `${Number(month)}月环比`,
              type: 'line',
              data: xAxis.map(cityname => {
                if (values[cityname]) {
                  const { momrate, momratetype } = values[cityname]

                  return !momrate
                    ? ''
                    : momratetype == 0
                      ? '-' + Number(momrate * 100).toFixed(2)
                      : Number(momrate * 100).toFixed(2)
                }
                return ''
              })
            })
          }
        } else if (self.statType == 4) {
          const [quarter] = key.slice(-1)
          name = '第' + Number(quarter) + '季度'
          // 万投比
          series.push({
            name: name,
            type: 'bar',
            data: xAxis.map(cityname => {
              if (values[cityname]) {
                const { score } = values[cityname]
                return score ? Number(score).toFixed(2) : ''
              }
              return ''
            })
          })
          // 环比
          if (key === this.startDate) {
            series.push({
              name: `${name}环比`,
              type: 'line',
              data: xAxis.map(cityname => {
                if (values[cityname]) {
                  const { momrate, momratetype } = values[cityname]

                  return !momrate
                    ? ''
                    : momratetype == 0
                      ? '-' + Number(momrate * 100).toFixed(2)
                      : Number(momrate * 100).toFixed(2)
                }
                return ''
              })
            })
          }
        } else {
          //   const [day] = key;
          //  name = day;
          //  console.log(key,"namenamenamenamenamenamenamenamenamenamenamenamenamename")
          // 万投比
          series.push({
            name: key,
            type: 'bar',
            data: xAxis.map(cityname => {
              if (values[cityname]) {
                const { score } = values[cityname]
                return score ? Number(score).toFixed(2) : ''
              }
              return ''
            })
          })
        }
      })

      // 横坐标
      // console.log(JSON.stringify({ xAxis, data: series }));
      return { xAxis, data: series }
    },
    levelChange() {
      this.currentPage = 1
      const p1 = this.getObstacleData() // 查询报障小区报障量清单
      this.trendloading = true
      Promise.all([p1])
        .then(values => {
          const res1 = values[0]
          if (res1.code == 200 && res1.data && res1.data.data) {
            const { first, second } = res1.data.data

            this.tableData = first
            this.total = Number(second[0].total) || 0
          }
        })
        .finally(() => {
          this.trendloading = false
        })
        .catch(() => {
          this.trendloading = false
        })
    },
    // 获取table数据
    getObstacleData() {
      const {
        pageSize,
        gridId,
        tableId,
        WarnLevel,
        currentPage,
        statType2,
        startDate2
      } = this
      const param = {
        pageSize: pageSize,
        currentPage: Number(pageSize * (currentPage - 1)).toString(),
        cityId: gridId,
        targetId: tableId,
        level: WarnLevel,
        opTime: startDate2,
        statType: statType2
      }
      console.log('param:', param)
      return getObstacle(param)
    },
    // 获取地市分析
    getCityCharts() {
      const { cityId, targetId } = this
      const param = {
        cityId: cityId,
        targetId: targetId,
        // ...this.mapDateParams
        opTime: this.startDate,
        statType: this.statType,
        startTime: this.startTime || '2022-06-05',
        endTime: this.endTime || '2022-06-08'
      }
      return getRepeatCity(param)
    },
    // 获取区县分析
    getDistrictCharts() {
      const { cityId, targetId } = this
      const param = {
        cityId: cityId,
        targetId: targetId,
        // ...this.mapDateParams
        opTime: this.startDate,
        statType: this.statType,
        startTime: this.startTime || '2022-06-05',
        endTime: this.endTime || '2022-06-08'
      }
      return getRepeatDistrict(param)
    },
    // 日期控件
    checkDate([type, val]) {
      if (type == '月') {
        this.statType = '3'
        this.statType2 = '3'
        this.startDate = val
        this.startTime = '2022-06-05'
        this.endTime = '2022-06-08'
      } else if (type == '日') {
        this.statType = '1'
        this.statType2 = '1'
        this.startTime = val[0]
        this.endTime = val[1]
      } else if (type == '季度') {
        this.statType = '4'
        this.statType2 = '4'
        this.startTime = '2022-06-05'
        this.endTime = '2022-06-08'
        const [year, quarter] = val.split('-')
        this.startDate = `${year}-Q${Number(quarter)}`
      }
      this.chart1Data = []
      this.complainAndTrend = {}
      this.dataCity = { xAxis: [], data: [] } // 地市分析
      this.dataDistrict = { xAxis: [], data: [] }
      this.optime2 = JSON.parse(JSON.stringify(this.startDate))
      this.startDate2 = JSON.parse(JSON.stringify(this.startDate))
      const ll = [
        { label: '1', value: 'daterange' }, // date
        { label: '3', value: 'month' }, // monthrange
        { label: '4', value: 'quarter' }
      ]
      let data = ''
      ll.forEach(({ value, label }) => {
        if (this.statType == label) {
          data = value
        }
      })

      // this.$refs.pickers.dates[`${data}__date`] = val
      // this.$refs.pickers.dateType = { value: data, label: type }
      this.queryData()
    },
    async init() {
      // 初始化获取所有区域
      const { data, code } = await AreaByUser({
        restSerialNo: '6MujCDCs'
      })
      if (code == 200) {
        this.optionsCascader = data[0]
      }
    },
    // 表格中切换地区
    cityChange(val) {
      console.log('val==>', val)
      const checkedNode = this.$refs['cascaderArr'].getCheckedNodes()
      this.currentPage = 1
      if (val.length == 3) {
        // 选择区县，出现网格
        const cityname = checkedNode[0].data.name
        const param = {
          cityId: cityname
        }
        getGridData(param).then(res => {
          if (res.code == 200 && res.data) {
            this.subnameLisat = res.data.data
          }
        })
      } else {
        this.subnameLisat = []
      }
      this.subId = ''
      this.gridId = this.getAllGridPathObj(val)
      this.levelChange()
    },
    areaChange(val, checkNodeData) {
      console.log('val==>', val)
      console.log('checkNodeData===>', checkNodeData)
      const checkedNode = checkNodeData[0]

      this.currentPage = 1
      if (checkedNode.level == 3) {
        this.countyCache = checkedNode
        // 选择区县，出现网格
        const cityname = checkedNode.name
        const param = {
          cityId: cityname
        }
        getGridData(param).then(res => {
          if (res.code == 200 && res.data) {
            this.subnameLisat = res.data.data
          }
        })
      } else {
        this.countyCache = null
        this.subnameLisat = []
      }
      this.subId = ''
      this.gridId = this.getAllGridPathObj(val)
      this.levelChange()
    },
    // 选择网格
    gridIdChange() {
      this.tableData = []
      this.total = 0
      const {
        pageSize,
        subId,
        tableId,
        WarnLevel,
        currentPage,
        statType2,
        startDate2
      } = this
      const param = {
        pageSize: pageSize,
        currentPage: (currentPage - 1).toString(),
        cityId: subId || this.countyCache.id,
        targetId: tableId,
        level: WarnLevel,
        opTime: startDate2,
        statType: statType2
      }
      getObstacle(param).then(res => {
        if (res.code == 200 && res.data && res.data.data) {
          const { first, second } = res.data.data
          this.tableData = first
          this.total = Number(second[0].total) || 0
        }
      })
    },
    // 处理cityid
    getAllGridPathObj(keyarr) {
      const len = keyarr.length
      let gridId = ''
      if (len) {
        if (len == 1) {
          gridId = keyarr[0]
        } else if (len == 2) {
          gridId = keyarr[1]
        } else if (len == 3) {
          gridId = keyarr[2]
        }
      }
      return gridId
    },
    // 下方表格日期控件
    checkDate2(params) {
      console.log('params:', params)
      const [type, val] = params
      if (type == '月') {
        this.statType2 = '3'
        this.startDate2 = val
        this.startTime2 = '2022-06'
        this.endTime2 = '2022-06'
      } else if (type == '日') {
        this.statType2 = '1'
        console.log('val:', val)
        this.startTime2 = val[0]
        this.startDate2 = val
        this.endTime2 = val[1]
      } else if (type == '季度') {
        this.statType2 = '4'
        const [year, quarter] = val.split('-')
        this.startTime2 = '2022-06-05'
        this.endTime2 = '2022-06-08'
        this.startDate2 = `${year}-Q${Number(quarter)}`
      }
      // this.statType2 = this.dateType[type];
      this.optime2 = val
      this.levelChange()
    },
    currentChange(val) {
      // 表格切换页
      this.currentPage = val
      const p1 = this.getObstacleData() // 查询报障小区报障量清单
      this.trendloading = true
      Promise.all([p1])
        .then(values => {
          const res1 = values[0]
          if (res1.code == 200 && res1.data && res1.data.data) {
            const { first, second } = res1.data.data
            this.tableData = first
            this.total = Number(second[0].total) || 0
          }
        })
        .finally(() => {
          this.trendloading = false
        })
        .catch(() => {
          this.trendloading = false
        })
    },
    // 查询报障小区分析
    getChart() {
      const { cityId, targetId } = this
      const param = {
        cityId: cityId,
        targetId: targetId,
        // ...this.mapDateParams
        opTime: this.startDate,
        statType: this.statType,
        startTime: this.startTime || '2022-06-05',
        endTime: this.endTime || '2022-06-08'
      }
      return getObstacleChart(param)
    },
    // 全屏展示地市分析
    dataCityFill(datalist, show) {
      const self = this
      this.$nextTick(() => {
        this.chart = this.$echarts.init(document.getElementById('fullScreen'))
        this.isFull = show
        const { xAxis, data } = datalist
        const option = {
          color: ['#7097f6', '#66d7cc', '#ffbe4d', '#f9906f'],
          toolbox: {
            show: true,
            feature: {
              myTool1: {
                show: true,
                title: '缩小',
                icon:
                  'path://M641.750109 384.100028l205.227128-204.519-0.704035 115.89966c-0.282433 9.611915 7.489578 18.09103 17.101493 17.808598l12.297071 0c9.611915-0.283456 17.667382-5.936199 17.808598-15.689331l0.565888-172.57752c0-0.14224 0.282433-9.187243 0.282433-9.187243 0.14224-4.804423-0.99056-9.187243-4.100388-12.297071-3.109828-3.109828-7.347339-5.086855-12.297071-4.946662l-8.763594 0.14224c-0.141216 0-0.278339 0-0.420579 0.14224L697.581696 98.166787c-9.611915 0.283456-17.667382 8.200776-17.808598 17.950837l0 12.297071c1.416256 11.44875 10.458189 18.092054 20.070104 17.808598l112.789832 0.283456-204.66124 203.814965c-9.329483 9.329483-9.329483 24.449855 0 33.778314 9.329483 9.470699 24.452925 9.470699 33.782408 0L641.750109 384.100028zM383.095141 576.889893 177.726797 780.705881l0.707105-115.338888c0.283456-9.607822-7.492648-18.086937-17.104563-17.808598l-13.001105 0c-9.611915 0.283456-17.667382 5.937223-17.808598 15.690354l-0.565888 172.718737c0 0.14224-0.282433 9.187243-0.282433 9.187243-0.14224 4.808516 0.99056 9.187243 4.096295 12.297071 3.109828 3.109828 7.351432 5.086855 12.297071 4.946662l8.762571-0.14224c0.14224 0 0.283456 0 0.425695-0.14224l171.873486 0.708128c9.607822-0.283456 17.667382-8.196683 17.808598-17.950837L344.93503 832.575226c-1.415232-11.44875-10.461259-18.092054-20.074198-17.808598L212.069977 814.483172 416.59 610.671277c9.329483-9.329483 9.329483-24.453948 0-33.782408C407.40685 567.41817 392.424624 567.41817 383.095141 576.889893L383.095141 576.889893zM894.047276 835.967486l-0.424672-172.718737c-0.283456-9.612938-8.200776-15.406898-17.809621-15.690354l-12.296047 0c-9.612938-0.278339-17.243733 8.200776-17.105586 17.808598l0.708128 115.903753L641.750109 576.889893c-9.329483-9.329483-24.452925-9.329483-33.782408 0-9.325389 9.328459-9.325389 24.452925 0 33.782408L812.490795 814.483172l-112.789832 0.283456c-9.611915-0.283456-18.515702 6.502088-20.073174 17.808598l0 12.297071c0.282433 9.611915 8.200776 17.667382 17.808598 17.950837l171.166381-0.708128c0.141216 0 0.282433 0.14224 0.424672 0.14224l8.763594 0.14224c4.803399 0.141216 9.187243-1.694595 12.296047-4.946662 3.109828-3.109828 4.238534-7.488555 4.097318-12.297071 0 0-0.14224-9.046027-0.14224-9.187243L894.047276 835.968509zM212.216309 146.506748l112.789832-0.283456c9.607822 0.283456 18.512632-6.502088 20.070104-17.808598L345.076246 116.116601c-0.283456-9.611915-8.196683-17.667382-17.808598-17.950837l-172.011632 0.708128c-0.14224 0-0.283456-0.14224-0.425695-0.14224l-8.761548-0.14224c-4.808516-0.141216-9.187243 1.694595-12.297071 4.946662-3.109828 3.109828-4.242627 7.488555-4.096295 12.297071 0 0 0.282433 9.046027 0.282433 9.187243l0.420579 172.718737c0.14224 9.608845 8.200776 15.406898 17.808598 15.686261l13.005198 0c9.611915 0.282433 17.242709-8.196683 17.10047-17.808598l-0.564865-115.334795 205.231221 203.958228c9.324366 9.329483 24.448832 9.329483 33.777291 0 9.329483-9.329483 9.329483-24.452925 0-33.782408L212.216309 146.506748 212.216309 146.506748zM212.216309 146.506748',
                onclick: function() {
                  self.isFull = false
                }
              }
            }
          },
          tooltip: {
            trigger: 'axis',
            top: '6%'
          },
          grid: {
            left: '5%',
            right: '5%',
            top: '10%',
            containLabel: true
          },
          legend: {},
          xAxis: [
            {
              type: 'category',
              data: xAxis,

              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                rotate: xAxis.length > 8 ? 25 : 0,
                textStyle: {
                  color: '#393939'
                }
              }
              // axisPointer: {
              //   type: 'shadow'
              // }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '万投比',
              axisLabel: {
                formatter: function(value) {
                  return value ? value + '‱' : '-'
                }
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed'
                }
              }
            },
            {
              type: 'value',
              name: '环比',
              axisLabel: {
                formatter: '{value}%'
              },
              splitLine: {
                show: false
              }
            }
          ],
          // 根据横坐标判断是否显示
          dataZoom:
            xAxis.length > 10
              ? [
                {
                  show: false,
                  type: 'slider',
                  end: 30,
                  ...this.dataZoomStyle
                }
              ]
              : [],
          series: data.map(item => {
            if (item.type === 'line') {
              return {
                ...item,
                ...this.lineStyle,
                tooltip: {
                  valueFormatter: function(value) {
                    return value ? value + '%' : '-'
                  }
                }
              }
            }
            return {
              ...item,
              barWidth: 15,
              //  barGap: 10,
              tooltip: {
                valueFormatter: function(value) {
                  return value ? value + '‱' : '-'
                }
              }
            }
          })
        }
        this.chart && this.chart.setOption(option, true)
      })
    },
    // 区县全屏
    dataDistrictFill(datalist, show) {
      const self = this
      this.$nextTick(() => {
        this.chart = this.$echarts.init(document.getElementById('fullScreen'))
        this.isFull = show
        const { xAxis, data } = datalist
        const option = {
          color: ['#7097f6', '#66d7cc', '#ffbe4d', '#f9906f'],
          toolbox: {
            show: true,
            feature: {
              myTool1: {
                show: true,
                title: '缩小',
                icon:
                  'path://M641.750109 384.100028l205.227128-204.519-0.704035 115.89966c-0.282433 9.611915 7.489578 18.09103 17.101493 17.808598l12.297071 0c9.611915-0.283456 17.667382-5.936199 17.808598-15.689331l0.565888-172.57752c0-0.14224 0.282433-9.187243 0.282433-9.187243 0.14224-4.804423-0.99056-9.187243-4.100388-12.297071-3.109828-3.109828-7.347339-5.086855-12.297071-4.946662l-8.763594 0.14224c-0.141216 0-0.278339 0-0.420579 0.14224L697.581696 98.166787c-9.611915 0.283456-17.667382 8.200776-17.808598 17.950837l0 12.297071c1.416256 11.44875 10.458189 18.092054 20.070104 17.808598l112.789832 0.283456-204.66124 203.814965c-9.329483 9.329483-9.329483 24.449855 0 33.778314 9.329483 9.470699 24.452925 9.470699 33.782408 0L641.750109 384.100028zM383.095141 576.889893 177.726797 780.705881l0.707105-115.338888c0.283456-9.607822-7.492648-18.086937-17.104563-17.808598l-13.001105 0c-9.611915 0.283456-17.667382 5.937223-17.808598 15.690354l-0.565888 172.718737c0 0.14224-0.282433 9.187243-0.282433 9.187243-0.14224 4.808516 0.99056 9.187243 4.096295 12.297071 3.109828 3.109828 7.351432 5.086855 12.297071 4.946662l8.762571-0.14224c0.14224 0 0.283456 0 0.425695-0.14224l171.873486 0.708128c9.607822-0.283456 17.667382-8.196683 17.808598-17.950837L344.93503 832.575226c-1.415232-11.44875-10.461259-18.092054-20.074198-17.808598L212.069977 814.483172 416.59 610.671277c9.329483-9.329483 9.329483-24.453948 0-33.782408C407.40685 567.41817 392.424624 567.41817 383.095141 576.889893L383.095141 576.889893zM894.047276 835.967486l-0.424672-172.718737c-0.283456-9.612938-8.200776-15.406898-17.809621-15.690354l-12.296047 0c-9.612938-0.278339-17.243733 8.200776-17.105586 17.808598l0.708128 115.903753L641.750109 576.889893c-9.329483-9.329483-24.452925-9.329483-33.782408 0-9.325389 9.328459-9.325389 24.452925 0 33.782408L812.490795 814.483172l-112.789832 0.283456c-9.611915-0.283456-18.515702 6.502088-20.073174 17.808598l0 12.297071c0.282433 9.611915 8.200776 17.667382 17.808598 17.950837l171.166381-0.708128c0.141216 0 0.282433 0.14224 0.424672 0.14224l8.763594 0.14224c4.803399 0.141216 9.187243-1.694595 12.296047-4.946662 3.109828-3.109828 4.238534-7.488555 4.097318-12.297071 0 0-0.14224-9.046027-0.14224-9.187243L894.047276 835.968509zM212.216309 146.506748l112.789832-0.283456c9.607822 0.283456 18.512632-6.502088 20.070104-17.808598L345.076246 116.116601c-0.283456-9.611915-8.196683-17.667382-17.808598-17.950837l-172.011632 0.708128c-0.14224 0-0.283456-0.14224-0.425695-0.14224l-8.761548-0.14224c-4.808516-0.141216-9.187243 1.694595-12.297071 4.946662-3.109828 3.109828-4.242627 7.488555-4.096295 12.297071 0 0 0.282433 9.046027 0.282433 9.187243l0.420579 172.718737c0.14224 9.608845 8.200776 15.406898 17.808598 15.686261l13.005198 0c9.611915 0.282433 17.242709-8.196683 17.10047-17.808598l-0.564865-115.334795 205.231221 203.958228c9.324366 9.329483 24.448832 9.329483 33.777291 0 9.329483-9.329483 9.329483-24.452925 0-33.782408L212.216309 146.506748 212.216309 146.506748zM212.216309 146.506748',
                onclick: function() {
                  self.isFull = false
                }
              }
            }
          },
          tooltip: {
            trigger: 'axis',
            top: '6%'
          },
          grid: {
            left: '5%',
            right: '5%',
            top: '10%',
            containLabel: true
          },
          legend: {
            selectedMode: false // 是否允许点击
          },
          xAxis: [
            {
              type: 'category',
              data: xAxis,

              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                rotate: xAxis.length > 8 ? 25 : 0,
                textStyle: {
                  color: '#393939'
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '万投比',
              axisLabel: {
                formatter: function(value) {
                  return value ? value + '‱' : '-'
                }
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed'
                }
              }
            },
            {
              type: 'value',
              name: '环比',
              axisLabel: {
                formatter: '{value}%'
              },
              splitLine: {
                show: false
              }
            }
          ],
          // 根据横坐标判断是否显示
          dataZoom:
            xAxis.length > 10
              ? [
                {
                  show: false,
                  type: 'slider',
                  end: 100,
                  ...this.dataZoomStyle
                }
              ]
              : [],
          series: data.map(item => {
            if (item.type === 'line') {
              return {
                ...item,
                ...this.lineStyle,
                tooltip: {
                  valueFormatter: function(value) {
                    return value ? value + '%' : '-'
                  }
                }
              }
            }
            return {
              ...item,
              barWidth: 8,
              //  barGap: 10,
              tooltip: {
                valueFormatter: function(value) {
                  return value ? value + '‱' : '-'
                }
              }
            }
          })
        }
        this.chart && this.chart.setOption(option, true)
      })
    }
  }
}
</script>
<style  lang="scss" scoped>
.mapwraper {
  position: relative;
  height: 500px;
}

.communityComplaints {
  background-color: rgba(242, 242, 242, 1);
  padding: 30px 40px 30px 40px;
  min-height: 100vh;
}
.con {
  position: relative;
  background: #fff;
  margin-top: 10px;
}
// .con1 {
//   position: relative;
//   background: #fff;
//   padding: 30px 40px 0px 40px;
//   margin-top: 10px;
//   height: 500px;
// }
.flex {
  display: flex;
  &.mapbox {
    > div {
      width: 50%;
      flex: 1;
    }
  }
}
.complaint-charts {
  background: #ffffff;
  display: flex;
  margin-top: 10px;
  // flex-wrap: wrap;
  .complaint-chart-item {
    flex: 1;
    // width:100%;
    &:not(:first-child) {
      .anaylsis-chart {
        &:before {
          content: " ";
          position: absolute;
          border-left: 1px dashed rgb(240, 237, 237);
          height: 95%;
        }
      }
    }
  }
  .complaint-chart-title {
    color: #262626;
    font-weight: bold;
    font-size: 14px;
    padding: 20px;
  }
  .anaylsis-chart {
    width: 100%;
    height: 350px;
    position: relative;
  }
  .complaint-blank {
    position: absolute;
    left: 0;
    top: 0;
    background: #ffffff;
    z-index: 1;
  }
}
.el-cascader {
  //位置选择中的图标样式
  margin: 0 15px;
  :deep(.el-icon-arrow-down:before ) {
    content: "\E6E1";
  }
  :deep(.el-icon-arrow-down ) {
    transform: rotate(180deg);
  }
  :deep(.is-reverse.el-icon-arrow-down ) {
    transform: rotate(0deg);
  }
}
.emVal {
  font-size: 20px;
  font-weight: 400;
  text-align: left;
  padding-right: 10px;
  color: #262626;
  span {
    font-size: 14px;
    color: #6b6b6b;
  }
}
.la {
  font-size: 14px;
  color: #6b6b6b;
  margin-bottom: 5px;
}
.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pt {
  font-weight: bold;
  font-size: 16px;
  line-height: 32px;
  padding: 10px 10px 0px 10px;
}
.hide {
  display: none;
}
</style>
