<template>
  <div v-loading="loading" class="imp">
    <NumberTitle num="01" text="无线网故障区县投诉异动监控" />
    <div class="con">
      <NewDatePicker class="datebox" :dateTypes="['date','month','daycumulative']" :tableName="'complaint_structure_wireless_network_top10'" @change="dateChange"></NewDatePicker>
      <!-- <AreaSelect class="areabox" @sendCityId="areaChange" /> -->
      <AreaPicker class="areabox" size="small" @change="areaChange" />

      <div style="position:absolute;right:10px;top:10px;z-index:10">
        <el-button v-if="tableDatas.length && showExportBtn" size="small" type="primary" style="margin-left:15px" @click="down">下载</el-button>
      </div>
      <div>
        <el-tabs v-model="activeTabId" @tab-click="queryTableData">
          <el-tab-pane v-for="item in labelArr" :key="item.id" :label="item.name" :name="item.id" />
        </el-tabs>
        <div class="tablebox" style="padding:10px">
          <div class="conbox" style="background:#fff;">
            <div>
              <el-table
                v-loading="tableLoading"
                :data="tableDatas"
                style="width: 100%"
                max-height="560"
                @row-click="clickTableCell"
              >
                <el-table-column
                  label="排名"
                  type="index"
                  align="center"
                  width="50"
                />
                <el-table-column
                  v-for="item in columnList"
                  :key="item.name"
                  :prop="item.key"
                  :label="item.name"
                  :width="item.width?item.width:'unset'"
                  align="center"
                  :show-overflow-tooltip="true"
                  :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
                >
                  <template slot-scope="scope">
                    <!-- <span>{{ scope.row[item.key] }}</span> -->
                    <span>{{ scope.row[item.key]===0||scope.row[item.key]==='0' ?'0':scope.row[item.key]?scope.row[item.key]:'-' }}</span>

                  </template>
                </el-table-column>

              </el-table>

            </div>

          </div>
          <!-- 分页功能 -->
          <!-- <div style="padding:5px 0;background:#fff;text-align:right" >
            <el-pagination
              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="page.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="page.total"
              @size-change="sizeChange"
              @current-change="pageCurrentChange"
            />
          </div> -->
        </div>
        <!-- echarts图 -->
        <div v-show="dateType=='3'" v-loading="chartLoading" style="height:380px;position:relative;padding:15px;">
          <div class="pt">
            <span v-show="activeRow.countyname" style="margin-left:15px">
              {{ activeRow.countyname }}故障区县-投诉量趋势图
            </span>
          </div>
          <LineBar
            :all-data="chartData"
            :legend-data="['投诉量','投诉率',]"
            :is-double-line="false"
            :keymap="{
              xData: 'statdate',
              seriesData: ['complaintnum','complaintrate' ]
            }"
          />
        </div>
        <el-divider />
        <!-- top5 -->
        <div v-loading="top5TableLoading" class="top5box">
          <div>
            <div class="pt pl">
              {{ activeRow.countyname }}故障区县-TOP5故障现象
            </div>
            <div

              class="topbox"
            >
              <Top5
                v-if="Top5DataL.length"
                :all-data="Top5DataL"
                :style="{
                  opacity: Top5DataL.length ? 1 : 0,
                  width: '100%',
                  height: '150px',
                }"
                :keymap="{
                  yName: '故障现象',
                  yData: 'filed',
                  seriesData: ['score'],
                }"
              />
              <div
                :style="{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  display: Top5DataL.length ? 'none' : 'block',
                }"
              >
                <Blank2 />
              </div>
            </div>
          </div>
          <div>
            <div class="pt pl">
              {{ activeRow.countyname }}故障区县-TOP5责任部门
            </div>
            <div
              class="topbox"
            >
              <Top5
                v-if="Top5DataC.length"
                :all-data="Top5DataC"
                :style="{
                  opacity: Top5DataC.length ? 1 : 0,
                  width: '100%',
                  height: '150px',
                }"
                :keymap="{
                  yName: '责任部门',
                  yData: 'filed',
                  seriesData: ['score'],
                }"
              />
              <div
                :style="{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  display: Top5DataC.length ? 'none' : 'block',
                }"
              >
                <Blank2 />
              </div>
            </div>
          </div>
          <div>
            <div class="pt pl">
              {{ activeRow.countyname }}故障区县-TOP5责任原因
            </div>
            <div
              class="topbox"
            >
              <Top5
                v-if="Top5DataR.length"
                :all-data="Top5DataR"
                :style="{
                  opacity: Top5DataR.length ? 1 : 0,
                  width: '100%',
                  height: '150px',
                }"
                :keymap="{
                  yName: '责任原因',
                  yData: 'filed',
                  seriesData: ['score'],
                }"
              />
              <div
                :style="{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  display: Top5DataR.length ? 'none' : 'block',
                }"
              >
                <Blank2 />
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaSelect from '../../HomeBroadband/components/areaSelect.vue'
import LineBar from '@/views/bj_proatal_web/components/nx-components/linebarChart.vue'
import Tool from '@/views/bj_proatal_web/utils/startTimeCal.js'
import Top5 from './top5.vue'
import columnListAll from './columList'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker'

import { getTableDataModule1, getProblemDetailTop5, exportClassMonitor, FAX0p6GF, uFWqP4z2, NqtLLcvL, xiBI6C4M, oBCGdH0G, ZtCC2C7G, tingdainExport, onlineAndOutLineExport, wifiExport, homebroadExport } from '@/api/complainPrewarining'

export default {
  components: {
    NumberTitle,
    AreaSelect,
    LineBar,
    Top5,
    AreaPicker,
    NewDatePicker
  },
  inject: ['showExportBtn', 'mapPermission'],

  data() {
    const dateObj = new Date()
    const year = dateObj.getFullYear()
    let month = Number(dateObj.getMonth()) + 1
    if (month < 10) {
      month = '0' + month
    } else {
      month = '' + month
    }

    return {
      dialogTableVisible: false,
      dialogtableLoading: false,
      loading: false,
      activeTabId: 'TS0007',
      businessOpts: [
        { label: '全部', value: '0' },
        { label: 'C-移动业务', value: 'C-移动业务' },
        { label: 'H-家庭业务', value: 'H-家庭业务' },
        { label: 'B-集团业务', value: 'B-集团业务' },
        { label: 'N-增值业务', value: 'N-增值业务' }
      ],
      Top5Data: [],
      top5TableLoading: false,
      businessClassify: '0',
      labelArr: [
        { name: '投诉量排名TOP10的网络类故障区县', id: 'TS0007' }
      ],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      dialogColumnList: [
        {
          key: 'channelname',
          name: '渠道名称'
        },
        {
          key: 'complaintnum',
          name: '投诉量'
        },
        {
          key: 'cityname',
          name: '地市'
        },
        {
          key: 'countyname',
          name: '区县'
        },
        {
          key: 'griddingname',
          name: '网格'
        },
        {
          key: 'orderjobnum',
          name: '订购工号'
        }

      ],
      columnList: columnListAll.wifiColumnList,
      tableDatas: [],
      dialogData: [],
      tableLoading: false,
      defaultDate: `${year}${month}`,
      dateType: '3',
      cityId: this.mapPermission.cityId,
      cityLevel: `${this.mapPermission.mapLevel}`,
      chartLoading: false,
      chartData: [],
      activeRow: { },
      Top5DataR: [],
      Top5DataC: [],
      Top5DataL: []

    }
  },
  mounted() {
    const self = this
    setTimeout(function() {
      var dateObj = new Date()
      const year = dateObj.getFullYear()
      let month = Number(dateObj.getMonth()) + 1
      if (month < 10) {
        month = '0' + month
      } else {
        month = '' + month
      }

      self.defaultDate = `${year}-${month}`
      self.dateType = '3'
      self.queryTableData()
    }, 0)
  },
  methods: {

    dateChange(params) {
      const [ dateType, v ] = params
      this.dateType = dateType == '月' ? '3' : dateType == '日' ? '1' : dateType == '日累计' ? '5' : ''
      this.defaultDate = v
      this.queryTableData()
    },
    areaChange(value) {
      const v = value[value.length - 1]
      this.cityId = v
      var ids = ['640100', '640200', '640300', '640400', '640500']
      if (v == '640000') {
        this.cityLevel = '1'
      } else if (ids.indexOf(v) != -1) {
        this.cityLevel = '2'
      } else {
        this.cityLevel = '3'
      }
      // 查询表格数据
      this.queryTableData()
    },
    handleSelectionChange(v) {
      console.log('业务选择', v)
    },

    // 查询表格的数据
    queryTableData() {
      const { defaultDate, dateType, activeTabId, cityLevel, cityId } = this
      const params = {
        targetId: activeTabId,
        level: cityLevel,
        cityId: cityId,
        statDate: defaultDate,
        statType: dateType,
        plotName: ''
      }
      this.tableLoading = true
      xiBI6C4M(params).then(res => {
        console.log(res)
        if (res.code == 200 && res.data) {
          this.tableDatas = res.data.data || []
          console.log(res.data.data)

          if (dateType == 3 && this.tableDatas.length) {
            // 查询echarts图的数据

            this.clickTableCell(this.tableDatas[0])
          } else {
            this.dialogData = []
            this.chartData = []
          }
          if (this.tableDatas.length) {
            // 获取top5
            this.activeRow = this.tableDatas[0]
            this.getTop5Channel(this.tableDatas[0])
          }
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // xxx故障区县-TOP5故障现象 xxx故障区县-TOP5责任部门 xxx故障区县-TOP5责任原因
    getTop5Channel(row) {
      const p1 = this.getProblemDetail('TS0008', row)
      const p2 = this.getProblemDetail('TS0009', row)
      const p3 = this.getProblemDetail('TS0010', row)
      this.top5TableLoading = true
      Promise.all([p1, p2, p3]).then(values => {
        console.log('values=>', values)
        const res1 = values[0]
        const res2 = values[1]
        const res3 = values[2]

        if (res1.code == 200 && res1.data) {
          res1.data.data.forEach(i => {
            i.score = (i.complaintnum === 0 || i.complaintnum === '0') ? 0 : i.complaintnum ? i.complaintnum : '-'
            i.filed = i.breakdownappearance
          })
          this.Top5DataL = res1.data.data || []
        } else {
          this.Top5DataL = []
        }
        if (res2.code == 200 && res2.data) {
          res2.data.data.forEach(i => {
            i.score = (i.complaintnum === 0 || i.complaintnum === '0') ? 0 : i.complaintnum ? i.complaintnum : '-'
            i.filed = i.breakdowndepartment
          })
          this.Top5DataC = res2.data.data || []
        } else {
          this.Top5DataC = []
        }
        if (res3.code == 200 && res3.data) {
          res3.data.data.forEach(i => {
            i.score = (i.complaintnum === 0 || i.complaintnum === '0') ? 0 : i.complaintnum ? i.complaintnum : '-'
            i.filed = i.breakdownreason
          })
          this.Top5DataR = res3.data.data || []
        } else {
          this.Top5DataR = []
        }
      }).finally(() => {
        this.top5TableLoading = false
      })

      // breakdownappearance 故障现象
      // breakdowndepartment 故障部门
      // breakdownreason 故障原因
    },

    // 获取故障详情接口
    getProblemDetail(targetId, row) {
      const { defaultDate, dateType, cityLevel, cityId } = this
      var ids = ['640100', '640200', '640300', '640400', '640500']
      var p = {
        targetId, // 故障原因
        'level': '',
        'cityId': row.countyid,
        'statType': dateType,
        'statDate': defaultDate,
        'plotName': row.plotName
      }
      if (row.countyid == '640000') {
        p.level = '1'
      } else if (ids.indexOf(row.countyid) != -1) {
        p.level = '2'
      } else if (row.countyid) {
        p.level = '3'
      }

      return ZtCC2C7G(p)
    },
    // 查询 xxx故障区县-投诉量趋势图 这个参数有疑问 跟row 的关系在哪里
    clickTableCell(row) {
      const { activeTabId, cityLevel, cityId, dateType, defaultDate } = this
      this.activeRow = row
      this.getTop5Channel(row)
      if (dateType != 3) return

      const params = {}
      const startDate = Tool.get12MonthAgo(defaultDate)
      var ids = ['640100', '640200', '640300', '640400', '640500']
      const p = {
        'targetId': activeTabId,
        'level': '',
        'cityId': row.countyid,
        'statType': dateType,
        'startDate': startDate,
        'endDate': defaultDate,
        'plotName': row.plotname
      }
      if (row.countyid == '640000') {
        p.level = '1'
      } else if (ids.indexOf(row.countyid) != -1) {
        p.level = '2'
      } else if (row.countyid) {
        p.level = '3'
      }
      this.chartLoading = true
      // 查询echarts的值
      oBCGdH0G(p).then(res => {
        if (res.code == 200 && res.data) {
          this.chartData = res.data.data.sort(this.compare) || []
        }
      }).finally(() => {
        this.chartLoading = false
      }).catch(() => {
        this.chartLoading = false
      })
    },
    compare(a, b) {
      var value1 = a['statdate']
      var value2 = b['statdate']
      return value1 == value2 ? 0 : (value1 > value2) ? 1 : -1
    },
    // 下载
    down() {
      //  this.statType = mode
      // this.date = monthValue
      const { defaultDate, dateType, activeTabId, cityLevel, cityId } = this
      const params = {
        targetId: activeTabId,
        level: cityLevel,
        cityId: cityId,
        statDate: defaultDate,
        statType: dateType,
        plotName: ''
      }
      this.$confirm('是否确认导出无线网故障区县投诉监控数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return wifiExport(params)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              '无线网故障区县投诉监控数据' + '.xls'
            )
            return false
          }
          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', '无线网故障区县投诉监控' + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    }

  }
}
</script>
<style lang="scss" scoped>
.top5box{
  display: flex;

  >div{
    width:33.3%;
    border-right:1px dashed #DCDFE6;
  }
}
.datebox{
    position: absolute;
    right:0px;
    top:-50px;
}
.areabox{
     position: absolute;

     right:350px;
    top:-50px;
}
.imp{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 40px 40px;
     min-height: 100vh;

     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
     /deep/.el-radio__input{
       display:none !important;
     }
}

.con{
    position: relative;
    margin-top:10px;
    background:#fff;

}
.topbox{
  width: 100%;padding-left:15px; height:300px;position: relative
}
 /deep/.el-table--medium .el-table__cell{
   padding:8px 0;
 }
/deep/.el-table th.el-table__cell.is-leaf,
/deep/.el-table td.el-table__cell{
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }
/deep/.el-tabs__nav-wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0px;
    background-color: #dfe4ed;
    z-index: 1;
}
/deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
/deep/.el-dialog{
       display: flex;
       flex-direction: column;
       margin:0 !important;
       position:absolute;
       top:40vh;
       left:50%;
       transform:translate(-50%,-50%);
   }
.pt{
    font-weight: 700;
    font-size: 16px;
    line-height: 32px;
    &.pl{
      padding-left: 15px;
    }
}

</style>

