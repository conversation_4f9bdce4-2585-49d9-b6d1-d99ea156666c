<template>
  <div v-loading="loading" class="imp">
    <NumberTitle num="01" text="投诉结构分类异动监控" />
    <div class="con">
      <NewDatePicker class="datebox" :dateTypes="['date','month','daycumulative']" :tableName="'complaint_structure_classify_monitor_top10_problem'" @change="dateChange"></NewDatePicker>
      <!-- <AreaSelect class="areabox" @sendCityId="areaChange" /> -->
      <AreaPicker class="areabox" size="small" @change="areaChange" />

      <div style="position:absolute;right:10px;top:10px;z-index:10">
        <span style="font-size:14px">业务分类：</span>
        <el-select v-model="businessClassify" placeholder="请选择" size="small" @change="handleSelectionChange">
          <el-option
            v-for="item in businessOpts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button v-if="tableDatas.length && showExportBtn" size="small" type="primary" style="margin-left:15px" @click="down">下载</el-button>
      </div>
      <div>
        <el-tabs v-model="activeTabId">
          <el-tab-pane v-for="item in labelArr" :key="item.id" :label="item.name" :name="item.id" />
        </el-tabs>
        <div class="tablebox" style="padding:10px">
          <div class="conbox" style="background:#fff;">
            <div>
              <el-table
                v-loading="tableLoading"
                :data="tableDatas"
                style="width: 100%"
                max-height="560"
                @row-click="clickTableCell"
              >
                <el-table-column
                  label="排名"
                  type="index"
                  align="center"
                  width="50"
                />
                <el-table-column
                  v-for="item in columnList"
                  :key="item.name"
                  :prop="item.key"
                  :label="item.name"
                  :width="item.width?item.width:'unset'"
                  align="center"
                  :show-overflow-tooltip="true"
                  :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
                >
                  <template slot-scope="scope">
                    <!-- <span v-if="item.key=='proportion'||item.key=='rosepercent'">
                      {{ scope.row[item.key]===0||scope.row[item.key]==='0' ?'0%':scope.row[item.key]?scope.row[item.key]+ '%':'-'
                      }}
                    </span> -->
                    <!-- <span v-else>{{ scope.row[item.key] }}</span> -->
                    <span>{{ scope.row[item.key]===0||scope.row[item.key]==='0' ?'0':scope.row[item.key]?scope.row[item.key]:'-' }}</span>

                  </template>
                </el-table-column>

              </el-table>

            </div>

          </div>
          <!-- 分页功能 -->
          <!-- <div style="padding:5px 0;background:#fff;text-align:right" >
            <el-pagination

              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="page.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="page.total"
              @size-change="sizeChange"
              @current-change="pageCurrentChange"
            />
          </div> -->
        </div>
      </div>
    </div>
    <el-dialog top="30vh" :title="activeRow.problemname" :visible.sync="dialogTableVisible">
      <el-table v-loading="dialogtableLoading" :data="dialogData">
        <el-table-column
          label="排名"
          type="index"
          align="center"
          width="50"
        />
        <el-table-column
          v-for="item in dialogColumnList"
          :key="item.name"
          :prop="item.key"
          :label="item.name"
          :width="item.width?item.width:'unset'"
          align="center"
          :show-overflow-tooltip="true"
          :min-width="item.key=='csmcustomerlableVisibleTxt'?200:100"
        >
          <template slot-scope="scope">
            <!-- <span v-if="item.key=='proportion'||item.key=='rosepercent'">
              {{ scope.row[item.key]===0||scope.row[item.key]==='0' ?'0%':scope.row[item.key]?scope.row[item.key]+ '%':'-'
              }}
            </span> -->

            <span>{{ scope.row[item.key]===0||scope.row[item.key]==='0' ?'0':scope.row[item.key]?scope.row[item.key]:'-' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaSelect from '../../HomeBroadband/components/areaSelect.vue'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import { getTableDataModule1, getProblemDetailTop5, exportClassMonitor, FAX0p6GF, uFWqP4z2, NqtLLcvL, xiBI6C4M, oBCGdH0G, ZtCC2C7G, classifyExport, onlineAndOutLineExport, wifiExport, homebroadExport } from '@/api/complainPrewarining'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker'

export default {
  components: {
    NumberTitle,
    AreaSelect,
    AreaPicker,
    NewDatePicker
  },
  inject: ['showExportBtn', 'mapPermission'],
  data() {
    const dateObj = new Date()
    const year = dateObj.getFullYear()
    let month = dateObj.getMonth() + 1
    if (month < 10) {
      month = '0' + month
    } else {
      month = '' + month
    }
    return {

      dialogTableVisible: false,
      dialogtableLoading: false,
      loading: false,
      activeTabId: 'x',
      businessOpts: [
        { label: '全部', value: '0' },
        { label: 'C-移动业务', value: 'C-移动业务' },
        { label: 'H-家庭业务', value: 'H-家庭业务' },
        { label: 'B-集团业务', value: 'B-集团业务' },
        { label: 'N-增值业务', value: 'N-增值业务' }
      ],
      businessClassify: '0',

      labelArr: [
        { name: '投诉量环比增长TOP10的问题', id: 'x' }
      ],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      columnList: [
        {
          key: 'problemname',
          name: '问题名称',
          width: '350px'
        },
        {
          key: 'businessclassify',
          name: '业务分类'
        },
        {
          key: 'complaintnum',
          name: '投诉量',
          width: '120px'
        },

        {
          key: 'proportion',
          name: '占比'
        },

        {
          key: 'rosenum',
          name: '环比增长量'
        },
        {
          key: 'rosepercent',
          name: '环比增幅百分比'
        }
      ],
      tableDatas: [],
      dialogData: [],
      tableLoading: false,
      defaultDate: `${year}${month}`,
      dateType: '3',
      cityId: this.mapPermission.cityId,
      cityLevel: `${this.mapPermission.mapLevel}`,
      activeRow: {}
    }
  },
  computed: {
    dialogColumnList() {
      const dialogColumnList = [
        {
          key: 'cityname',
          name: '地市'
        },
        {
          key: 'complaintnum',
          name: '投诉量'
        },
        {
          key: 'proportion',
          name: '占比'
        },
        {
          key: 'rosenum',
          name: '环比增长量'
        },
        {
          key: 'rosepercent',
          name: '环比增幅百分比'
        }
      ]
      switch (this.cityLevel) {
        case '1' : dialogColumnList.splice(0, 1, {
          key: 'cityname',
          name: '地市'
        }); break
        case '2': dialogColumnList.splice(0, 1, {
          key: 'countyname',
          name: '区县'
        }); break
        case '3': break
      }
      return dialogColumnList
    }
  },
  mounted() {
    const self = this
    setTimeout(function() {
      var dateObj = new Date()
      const year = dateObj.getFullYear()
      let month = Number(dateObj.getMonth()) + 1
      if (month < 10) {
        month = '0' + month
      } else {
        month = '' + month
      }

      self.defaultDate = `${year}-${month}`
      self.dateType = '3'
      self.queryTableData()
    }, 100)
  },
  methods: {
    clickTableCell(row) {
      if (this.cityLevel == 3) {
        return
      }
      this.activeRow = row
      this.dialogTableVisible = true
      this.dialogtableLoading = true
      const { defaultDate, dateType, activeTabId, cityLevel, cityId, businessClassify } = this
      const params = {
        id: row.id,
        level: cityLevel,
        cityId: cityId,
        statDate: defaultDate,
        statType: dateType,
        businessClassify,
        problemName: row.problemname.replace(/>/g, '\t') // 可是化平台> 符号不能解析 所以把> 转化为 \t
      }
      getProblemDetailTop5(params).then(res => {
        if (res.code == 200 && res.data) {
          this.dialogData = res.data.data || []
        }
      }).finally(() => {
        this.dialogtableLoading = false
      })
    },
    dateChange(params) {
      const [ dateType, v ] = params
      this.dateType = dateType == '月' ? '3' : dateType == '日' ? '1' : dateType == '日累计' ? '5' : ''
      this.defaultDate = v
      this.queryTableData()
    },
    areaChange(value) {
      const v = value[value.length - 1]
      this.cityId = v
      var ids = ['640100', '640200', '640300', '640400', '640500']
      if (v == '640000') {
        this.cityLevel = '1'
        // this.dialogColumnList.splice(0, 1, {
        //   key: 'cityname',
        //   name: '地市'
        // })
      } else if (ids.indexOf(v) != -1) {
        // this.dialogColumnList.splice(0, 1, {
        //   key: 'countyname',
        //   name: '区县'
        // })
        // console.log('this.dialogColumnList:', this.dialogColumnList)
        this.cityLevel = '2'
      } else {
        this.cityLevel = '3'
      }
      // 查询表格数据
      this.queryTableData()
    },
    handleSelectionChange(v) {
      this.queryTableData()
    },
    // 查询表格的数据
    queryTableData() {
      const { defaultDate, dateType, activeTabId, cityLevel, cityId, businessClassify } = this
      const params = {
        level: cityLevel,
        cityId: cityId,
        statDate: defaultDate,
        statType: dateType,
        businessClassify
      }
      this.tableLoading = true
      getTableDataModule1(params).then(res => {
        if (res.code == 200 && res.data) {
          this.tableDatas = res.data.data || []
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 下载
    down() {
      //  this.statType = mode
      // this.date = monthValue
      const { defaultDate, dateType, cityLevel, cityId, businessClassify } = this
      const params = {
        level: cityLevel,
        cityId: cityId,
        statDate: defaultDate,
        statType: dateType,
        businessClassify
      }
      this.$confirm('是否确认导出投诉结构分类异动监控数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return exportClassMonitor(params)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              '投诉结构分类异动监控' + '.xls'
            )
            return false
          }

          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', '投诉结构分类异动监控' + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    }

  }
}
</script>
<style lang="scss" scoped>
.datebox{
    position: absolute;
    right:0px;
    top:-50px;
}
.areabox{
     position: absolute;

     right:350px;
    top:-50px;
}
.imp{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 40px 40px;
     min-height: 100vh;

     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
     /deep/.el-radio__input{
       display:none !important;
     }
}

.con{
    position: relative;
    margin-top:10px;
    background:#fff;

}
 /deep/.el-table--medium .el-table__cell{
   padding:8px 0;
 }
/deep/.el-table th.el-table__cell.is-leaf,
/deep/.el-table td.el-table__cell{
   border-bottom: 1px solid rgba(225,225,225,0.3);
 }
/deep/.el-tabs__nav-wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 0px;
    background-color: #dfe4ed;
    z-index: 1;
}
/deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }

.conbox{
  background:#fff;
  border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;

}
/deep/.el-dialog{
       display: flex;
       flex-direction: column;
       margin:0 !important;
       position:absolute;
       top:40vh;
       left:50%;
       transform:translate(-50%,-50%);
   }
/deep/.el-dialog__body{
  padding:0px 20px 20px 20px;
}

</style>

