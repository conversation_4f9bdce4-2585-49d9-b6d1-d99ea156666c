<template>
  <div class="reportforms blackpot" style="height: 100%;">
    <div class="layout">
      <el-tabs v-model="type" type="border-card">
        <el-tab-pane name="1" label="两类差错及反悔办理设置">
          <div class="grid" style="background:#fff;padding:10px 10px">
            <div class="flexbox" style="padding-top:20px;padding-left:10px">
              <div class="formbox" style="position:relative;">
                <el-form ref="queryForm" :inline="true" :model="formInline" @submit.prevent.native :size="conditionsize" class="demo-form-inline" label-position="right">
                  <el-form-item label="网格名" class="timeRange" prop="gridName">
                    <el-input :clearable="true" style="width:200px" v-model="formInline.gridName"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="query">查询</el-button>
                    <el-button type="default" @click="resetForm('queryForm')">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <div class="conbox" style="background:#fff;">
              <div>
                <el-table v-loading="tableLoading" :data="tableDatas" style="width: 100%" :height="getTableHeight()" border>
                  <el-table-column v-for="item in columnList" :key="item.key" :prop="item.key" :label="item.name" align="center" min-width="140">
                    <template slot-scope="{row}">
                      <div>
                        <span>{{ row[item.key] }}</span>
                        <i class="el-icon-edit" v-if="item.key !== 'gridName'" style="margin-left: 10px;cursor: pointer;" @click="editValue(row, item.key)"></i>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <!-- 分页功能 -->
            <div style="padding:10px 0;background:#fff;">
              <el-pagination v-if="tableDatas" :current-page="page.pageCurrent" :page-sizes="[10, 20, 50, 100]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="total" @size-change="sizeChange" @current-change="pageCurrentChange" />
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>

    </div>
    <el-dialog :visible.sync="editVisible" width="30%" append-to-body :show-close="false">
      <div>
        <el-form ref="form" :model="form" label-width="80px" label-suffix="：" label-position="right" :rules="editRules">
          <el-form-item label="部门" prop="checkDept">
            <el-cascader v-model="form.checkDept" :show-all-levels="false" :options="deptOptions" :props="{children: 'childs', checkStrictly: true}" @change="changeDept" style="width: 240px;">
            </el-cascader>
          </el-form-item>
          <el-form-item label="人员" prop="checkUserId">
            <el-select v-model="form.checkUserId" style="width: 240px;" @change="addcheckedUserList">
              <el-option :label="it.label" :value="it.value" v-for="it in userOptions" :key="it.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="cancelHandle" :size="conditionsize">取 消</el-button>
        <el-button type="primary" @click="confirmHandle" :size="conditionsize">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getPage,
  getUsersByDeptId,
  updateInfo
} from '@/api/workflowSetting/index';
import { getDepartmentChildren } from '@/api/warn-rule/index';
export default {
  name: 'workflowSetting',
  components: {},
  data() {
    return {
      type: '1',
      formInline: {
        gridName: ''
      },
      form: {
        checkDept: '',
        checkUserId: ''
      },
      checkedRow: {id: '', key: ''},
      conditionsize: 'small',
      page: {
        pageCurrent: 1,
        pageSize: 20
      },
      total: 0,

      tableDatas: [],

      columnList: [
        { name: '网格名', key: 'gridName' },
        // { name: '网格长账号', key: 'gridManager' },
        { name: '网格长', key: 'gridManagerName' },
        // { name: '区县经理账号', key: 'gridAuditor' },
        { name: '区县经理', key: 'gridAuditorName' },
        // { name: '分管服务副总账号', key: 'secondGridAuditor' },
        { name: '分管服务副总', key: 'secondGridAuditorName' },
        // { name: '办阅人账号', key: 'gridReaders' },
        { name: '办阅人', key: 'gridReadersName' }
      ],
      tableLoading: false,
      editVisible: false,
      deptOptions: [],
      userOptions: [],
      editRules: {
        checkDept: [{ required: true, message: '请选择部门', trigger: 'change' }],
        checkUserId: [{ required: true, message: '请选择人员', trigger: 'change'}]
      }
    };
  },
  watch: {
    editVisible(nv) {
      if(!nv) {
        this.checkedRow = {id: '', key: ''};
        this.userOptions = [];
      }
    }
  },
  mounted() {
    this.query();
    this.getDeptOption();
  },
  methods: {
    query() {
      this.page.pageCurrent = 1;
      this.queryList();
    },
    async queryList() {
      const { page, formInline } = this;
      const params = { ...formInline, ...page };
      this.tableLoading = true;
      const { code, data } = await getPage(params).finally(() => {
        this.tableLoading = false;
      });
      if (code == 200) {
        const { records, total } = data;
        this.tableDatas = records || [];
        this.total = total;
      }
    },

    resetForm(formName) {
      this.page.pageCurrent = 1;
      this.$refs[formName].resetFields();
      this.queryList();
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.page.pageCurrent = 1;
      this.page.pageSize = val;
      this.queryList();
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.page.pageCurrent = val;
      this.queryList();
      // 下方添加查询逻辑
    },
    getTableHeight() {
      return document.body.offsetHeight - 268 + 'px';
    },
    editValue(row, rowKey) {
      const [key] = rowKey.split('Name');
      this.checkedRow.key = key;
      this.checkedRow.id = row.id;
      this.editVisible = true;
    },
    async getDeptOption() {
      const { code, data } = await getDepartmentChildren();
      if (code === 200) {
        this.deptOptions = data;
      }
    },
    changeDept(value) {
      console.log('选择的部门', value, this.form.checkDept);
      this.getUser();
    },
    addcheckedUserList(value) {
      console.log('选择的用户', value);
    },
    async getUser() {
      const deptId = this.form.checkDept.at(-1);
      const params = { deptId };
      const { code, data } = await getUsersByDeptId(params);
      if (code === 200) {
        this.userOptions = data;
      }
    },
    cancelHandle() {
      this.$refs.form.resetFields();
      this.editVisible = false;
    },
    async confirmHandle() {
      const status = await this.$refs.form.validate().catch(() => false);
      if(!status) return;
      const {key, id} = this.checkedRow;
      const {checkUserId} = this.form;
      const params = {[key]: checkUserId , id};
      const {code, msg} = await updateInfo(params).catch((err) => ({msg: err}));
      if(code !== 200) {
        this.$message.error(msg || '修改失败');
        return;
      }
      this.$message.success(msg || '修改成功');
      this.cancelHandle();
      this.queryList();
    }
  }
};
</script>




<style lang='scss' scoped>
/deep/.el-tabs--border-card > .el-tabs__content {
  padding: 0 !important;
}
.formItemWd {
  width: 150px;
}

/deep/.el-select .el-input.is-focus .el-input__inner {
  border-color: #ff9900;
}
/deep/.el-textarea__inner:focus {
  border-color: #ff9900;
}
.reportforms {
  background-color: rgba(242, 242, 242, 1);

  /deep/.el-input-group__append,
  /deep/.el-input-group__prepend {
    background: #f19733;
    color: #262626;
    &:hover {
      color: #fff;
    }
  }
  .layout {
    // padding: 0 50px;

    /deep/.el-tabs__nav-wrap::after {
      height: 1px;
      background: #e6e6e6;
    }
    /deep/.el-tabs__active-bar {
      background: #ff9900;
    }
    /deep/.el-tabs__item.is-active {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #262626;
    }
    /deep/.el-tabs__item {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #595959;
    }
    /deep/.el-form-item.el-form-item--medium {
      margin-right: 20px;
    }
    /deep/.el-form-item__content .el-input-group {
      vertical-align: middle;
    }
    /deep/.el-pagination {
      text-align: right;
    }
    .speformitem /deep/.el-form-item__label {
      width: 96px;
    }
    .timeRange {
    }
  }
  .flexbox {
    display: flex;
    position: relative;

    .formbox {
      flex: 1;
    }
    .btnbox {
      position: absolute;
      right: 0;
      bottom: 22px;
    }
  }

  .el-cascader {
    /deep/.el-icon-arrow-down:before {
      content: '\E6E1';
    }
    /deep/.el-icon-arrow-down {
      transform: rotate(180deg);
    }
    /deep/.is-reverse.el-icon-arrow-down {
      transform: rotate(0deg);
    }
  }
}

/deep/.el-table--medium .el-table__cell {
  padding: 8px 0;
}
/deep/.el-table th.el-table__cell.is-leaf,
/deep/.el-table td.el-table__cell {
  border-bottom: 1px solid rgba(225, 225, 225, 0.3);
}

.conbox {
  background: #fff;
  //border:1px solid rgba(225,225,225,0.8);
  border-bottom: none;
}
.optionbtnbox {
  display: flex;
  > div {
    flex: 1;
    width: 33.3%;
    span {
      cursor: pointer;
      &.coloryellow {
        color: #ff9900;
      }
    }
  }
}
.textAlign {
  /deep/ .el-input__inner {
    text-align: left;
  }
}
.set-label {
  display: inline-block;
  max-width: 110px;
  overflow: hidden;
  text-emphasis: ellipsis;
}
</style>
