<template>
  <div class="service-item" v-loading="loading" v-if="option!=undefined && option.length > 0">
    <div class="service-header">
      <div style="width: 100%;height: 92px;">
        <div class="service-header-title">感知要素分项情况</div>
        <div class="service-header-tab">
          <span v-for="item in option" :key="item.selectedKpiId" @click="changeTab(item)" :class="{'tabs-item': true ,'tab-checked ': item.id == selectedKpiId}">
            {{ item.name }}
          </span>
        </div>
      </div>
    </div>
    <div class="service-box">
      <div class="base_date_wrap">
          <!-- 时间查询 -->
          <div class="service-date">
            <date-picker @change="handleDateChange" :default-date="defaultDate" :date-types="dateTypes" @switch-date-type="onSwitchDateType" />
            <!-- <el-button type="primary" @click="query" size="small">查询</el-button> -->
          </div>
       </div>

       <div class="echarts_row_wrap">
          <el-row :gutter="20">
            <el-col :span="7"><div class="grid-content-left">
              <!-- 地图 -->
              <PerceptualMap
                :date="date"
                :selectedKpiId="selectedKpiId"
                :target="target"
                @map-change="onMapChange"
                class="service-kpi-map"
              >
              </PerceptualMap>
            </div></el-col>
            <el-col :span="17">
              <div class="grid-content-right">
                <!-- 图表 -->
                <el-row v-for="(item,index) in comConfig" :key="'row' + index" class="margin-row">
                  <el-col v-for="it in item" :key="it.targetId" :span="it.col" style="height: 351px" class="border-col">
                      <ServiceOverviewChart
                        :key="`ServiceOverviewChart${it.targetId}${nums}`"
                        :title="title"
                        :selectedKpiId="selectedKpiId"
                        :currentView="it"
                        :date="date"
                        :mapCity="mapCity"
                      ></ServiceOverviewChart>
                    </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>

          <!-- <el-row :gutter="20" v-for="item in homeOption" :key="item" >
            <el-col  v-for="it in item" style="height: 351px" :span="it.col" class="border-col">
              <ServiceOverviewChart
                :key="`ServiceOverviewChart${it.targetId}${nums}`"
                :title="''"
                :selectedKpiId="selectedKpiId"
                :currentView="it"
                :date="date"
                :mapCity="mapCity"
              ></ServiceOverviewChart>
            </el-col>
          </el-row> -->

       </div>
    </div>
  </div>
</template>

<script>
import homeOption from '../Tab4Child2/homeOption';
import DatePicker from '../../../date-picker/DatePicker.vue';
import ServiceOverviewChart from '../../tab4Component/ServiceOverviewChart.vue';
import PerceptualMap from '../PerceptualMap.vue';
import {getNewTime} from '@/api/afterRemark/index.js';
export default {
  name: 'Perceptual',
  props:['option','dateTypes'],
  components: {
    DatePicker,
    ServiceOverviewChart,
    PerceptualMap
  },
  inject: ['mapPermission'],
  data() {
    return {
      nums: '1',
      homeOption: homeOption,
      target:'',
      date: [],
      loading: false,
      defaultDate: {},
      selectedKpiId: '',
      mapCity: { ...this.mapPermission, mapAction: 'query' },
      // 时间参数对应映射
      baseConfig: {
        '日': '1',
        '周': '2',
        '月': '3',
        '季度':'4'
      },
      comConfig: [
        [
          {
            chartConfigId: 'KPI04-ART01',
            isTable: true,
            col: 24,
            name: '满意度趋势',
            restSerialNo: 'trend',
            table: [
              { label: '满意度', score: null, rate: null },
              { label: '环比', score: null, rate: null, unit: '%'}
            ]
          },
          {
            chartConfigId: 'KPI04-ART03',
            col: 24,
            isTable: true,
            isTab: true,
            isSort: true,
            switchArr: [
              {
                tabId: '1',
                name:'地市排名'
              },
              {
                tabId: '2',
                name:'区县排名'
              }
            ],
            name: '满意度区域',
            restSerialNo: 'area',
            table: [
              { label: '满意度', score: null, rate: null },
              { label: '环比', score: null, rate: null, unit: '%'}
            ]
          }
        ]
      ]
    };
  },
  created() {

  },
  mounted() {
    // 获取最新有数据时间
    this.getTime();
  },

  watch: {
     option: {
        handler(newVal) {
          console.log('新数据', newVal);
          if(newVal == undefined || newVal.length == 0) {
            return;
          }
          // 初始化选中
          this.selectedKpiId = newVal[0].id;
        },
        deep: true,
        immediate: true
     }
  },


  computed: {
    title() {
      let option = this.option;
      if( option && option.find(item=> item.id == this.selectedKpiId)) {
        return option.find(item=> item.id == this.selectedKpiId).name
      }
    },
    // 选中的时间类型(默认取第一个类型)
    dateTypeSelect() {
      // 默认时间有的情况
      if(this.date.length > 0) {
         return this.date && this.date[0] && this.baseConfig[this.date[0]]
      }else{
         return this.dateTypes[0].label && this.baseConfig[this.dateTypes[0].label]
      }

    },
    // 选中的时间类型名
    dateNameSelect() {
      if(this.date.length > 0) {
         return this.date && this.date[0]
      }else{
         return this.dateTypes[0].label
      }
    },
  },
  methods: {
    // 查询
    queryDate() {

    },
    query() {
      this.nums++;
    },
     // 日期时间改变回调
     handleDateChange(date) {
      const [type, value] = date;
      // 处理季度格式为2022-Q1
      this.date =
        type === '季度' ? [type, value.replace('-0', '-Q')] : date;
    },
     // 日期类型改变回调
     onSwitchDateType(date) {
      this.handleDateChange(date);
    },
    // 获取最新时间
    async getTime() {
      let params = {
          "cityId": this.mapPermission.cityId || '640000',
          "statType": this.dateTypeSelect,
          "targetId": this.selectedKpiId
      }
      try {
        const {data} =  await getNewTime(params);
        // 赋值给默认时间
        this.defaultDate = { [this.dateNameSelect]: data.replace('-Q', '-0') };
        // 赋值给当前时间, 季度特殊处理
        this.date = this.dateNameSelect == '季度' ? [this.dateNameSelect, data.replace('-0', '-Q')] : [this.dateNameSelect, data];

        return Promise.resolve();
        // 处理不同的时间类型, 然后入参
      } catch (error) {
        console.log('抛出',error);
      }

    },
    // 服务分项各类切换
    async changeTab(item) {
      const { id } = item;
      this.selectedKpiId = id;
    },
    // 地图改变
    onMapChange(map) {
      this.mapCity = { ...map };

    },

  }
};
</script>

<style lang="scss" scoped>
.service-item {
  .service-header {
    height: 140px;
    padding-top: 32px;
    .service-header-title {
      color: #262626;
      font-size: 30px;
      line-height: 38px;
      text-align: center;
    }
    .service-header-tab {
      margin-top: 32px;
      cursor: pointer;
      height: 40px;
      padding: 0 25%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      > span {
        line-height: 40px;
        font-size: 14px;
        color: #262626;
      }
      .tab-items {
        cursor: pointer;
        display: inline-block;
        width: 84px;
        text-align: center;
      }
      .tab-checked {
        border-bottom: 1px solid #000;
      }
    }

    .service-date {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .service-box {
    width: 100%;
    .service-date {
      display: flex;
      justify-content: flex-end;
      margin: 25px 0 23px;
      .el-button {
        margin-left: 16px;
      }

      .el-button--small{
        height: 32px !important;
        padding: 6px 24px;
        font-size: 14px;
        font-family: Source Han Sans SC;
        font-weight: 400;
      }
   }

   .echarts_row_wrap {
     background-color: #fff;
     padding: 24px;
     min-height: 750px;
     .grid-content-left {
        height: 702px;
        border-right: 1px solid #DCDCDC;
        .service-kpi-map {
          height: 100%;
        }
     }
     .grid-content-right {
        height: 702px;
     }
   }


  }
}
</style>
