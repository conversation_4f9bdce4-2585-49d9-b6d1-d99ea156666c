/**
 * 集团考核服务KPI指标
 *
 * targetId: 指标id， 以KPI开头的targetId是自定义的数据
 * name: 指标名称
 * dateTypes: 日期类型
 * hasMap: 是否展示地图，不配置就默认有地图
 * mapMaxLevel: 地图的最大层级，不设置就默认为3层
 * mapOption: 地图自定义选项配置
 * restSerialNo: 接口请求配置
 * children:[ 二级指标数组
 *   {
 *    restSerialNo: 接口请求配置
 *    tabs: { 二级tab切换
 *      name: tab名称
 *      tabId: 自定义id
 *      table: tab对应的表格配置
 *    }
 *    children: 三级指标数据
 *   }
 * ]
 */
const img1 = import('bj_src/assets/img/zhmyd.svg');
const img2 = import('bj_src/assets/img/gxbsg.svg');
const img3 = import('bj_src/assets/img/tsmyd.svg');
const img4 = import('bj_src/assets/img/yhjp.svg');
const img5 = import('bj_src/assets/img/zlts.svg');
export const KPI = [
  {
    targetId: 'KPI01',
    name: '综合满意度',
    img: img1,
    dateTypes: [{ label: '季度', value: 'quarter' }],
    hasMap: false, // 是否有地图
    mapMaxLevel: 2,
    children: [
      {
        targetId: '10605',
        name: '工信部综合满意度（整体）',
        restSerialNo: 'LMdH7x1J',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '移动综合满意度累计表现值', score: null, rate: null, unit: '%' },
              { label: '移动综合满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '移动综合满意度累计表现值', score: null, rate: null, unit: '%' }
            ]
          }
        ]
      },
      {
        targetId: 'KPI0102',
        name: '工信部综合满意度（分业务）',
        restSerialNo: 'vpWMQynD',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '移动####满意度累计表现值', score: null, rate: null, unit: '%' },
              { label: '移动####满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '移动####满意度累计表现值', score: null, rate: null, unit: '%' }
            ]
          }
        ],
        children: [
          {
            targetId: '1060601',
            name: '固定电话'
          },
          {
            targetId: '1060602',
            name: '固定上网'
          },
          {
            targetId: '1060603',
            name: '移动业务'
          }
        ]
      }
    ]
  },
  {
    targetId: 'KPI02',
    name: '工信部申告',
    img: img2,
    dateTypes: [
      { label: '日', value: 'date' }, // date
      { label: '月', value: 'month' } // monthrange
    ],
    restSerialNo: '7LxwnCvM',
    mapMaxLevel: 2,
    // 地图选项配置
    mapOption: {
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const { name, data } = params
          const str = [name]
          for (let i = 0; i < 2; i++) {
            const targetName = i == 0 ? 'targetname' : `targetname${i}`
            const value = i == 0 ? 'value' : `value${i}`
            if (data[targetName]) {
              const [seriesName] = data[targetName].split('-')
              str.push(`${seriesName} ${data[value] || '-'}`)
            }
          }
          return str.join('</br>')
        }
      }
    },
    children: [
      {
        targetId: '302',
        name: '工信部百万申告率',
        ajaxIds: ['309', '302'],
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '移动工信部百万申告量月累计', score: null },
              { label: '移动工信部百万申告率月累计', score: null },
              { label: '移动工信部百万申告率月累计领先值', score: null, isCal: true }
            ]
          },
          {
            name: '归责单位',
            tabId: '2',
            table: [
              { label: '移动工信部百万申告量月累计', score: null },
              { label: '移动工信部百万申告率月累计', score: null }
            ]
          }
        ]
      },
      {
        targetId: '305',
        name: '工信部携号转网申告率',
        ajaxIds: ['306', '305'],
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '移动工信部携号转网申告量月累计', score: null },
              { label: '移动工信部携号转网申告率月累计', score: null },
              { label: '移动工信部携号转网申告率月累计领先值', score: null, isCal: true }
            ]
          },
          {
            name: '归责单位',
            tabId: '2',
            table: [
              { label: '移动工信部携号转网申告量月累计', score: null },
              { label: '移动工信部携号转网申告率月累计', score: null }
            ]
          }
        ]
      },
      {
        targetId: '307',
        name: '工信部营销宣传申告率',
        ajaxIds: ['308', '307'],
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '移动工信部营销宣传申告量月累计', score: null },
              { label: '移动工信部营销宣传申告率月累计', score: null },
              { label: '移动工信部营销宣传申告率月累计领先值', score: null, isCal: true }
            ]
          },
          {
            name: '归责单位',
            tabId: '2',
            table: [
              { label: '移动工信部营销宣传申告量月累计', score: null },
              { label: '移动工信部营销宣传申告率月累计', score: null }
            ]
          }
        ]
      },
      {
        targetId: '303',
        name: 'TOP10申告问题改善',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '总累计申告量', score: null, rateLabel: '同比', rate: null, unit: '%' },
              { label: '总累计申告率', score: null, rateLabel: '同比', rate: null, unit: '%' }
            ]
          },
          {
            name: '归责单位',
            tabId: '2',
            table: [
              { label: '总累计申告量', score: null, rateLabel: '同比', rate: null, unit: '%' },
              { label: '总累计申告率', score: null, rateLabel: '同比', rate: null, unit: '%' }
            ]
          }
        ]
      }
    ]
  },
  {
    targetId: 'KPI03',
    name: '自测满意度',
    img: img3,
    dateTypes: [
      { label: '月', value: 'month' }, // monthrange
      { label: '季度', value: 'quarter' }
    ],
    restSerialNo: 'eEs2GWZt',
    mapMaxLevel: 3,
    children: [
      {
        targetId: 'KPI0301',
        name: '手机满意度',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '手机满意度', score: null, rate: null, unit: '%' },
              { label: '手机领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060201'] },
              // { label: '下发量', score:null, unit:'%'  },
              // { label: '参评量', score:null,  unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '手机满意度', score: null, rate: null, unit: '%' },
              // { label: '下发量', score:null, unit:'%'  },
              // { label: '参评量', score:null,  unit: '%' }
            ]
          }
        ],
        children: [

          {
            targetId: '105060201',
            name: 'CATI（省内）'
          },
          {
            targetId: '10802',
            name: 'CATI（集团）',
            disable: false
          },
          {
            targetId: '101',
            name: '用后即评'
          }
        ]
      },
      {
        targetId: 'KPI0302',
        name: '家宽满意度',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            component: '',
            table: [
              { label: '家宽满意度', score: null, rate: null, unit: '%' },
              { label: '家宽领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060101'] }, // cati省内的时候才有）
              // { label: '下发量', score:null, unit:'%'  },
              // { label: '参评量', score:null,  unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            component: '',
            table: [
              { label: '家宽满意度', score: null, rate: null, unit: '%' },
              // { label: '下发量', score:null, unit:'%'  },
              // { label: '参评量', score:null,  unit: '%' }
            ]
          }
        ],
        children: [

          {
            targetId: '105060101',
            name: 'CATI（省内）'
          },
          {
            targetId: '10801',
            name: 'CATI（集团）',
            disable: false
          },
          {
            targetId: '102',
            name: '用后即评'
          }
        ]
      },
      {
        targetId: '103',
        name: '政企满意度',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '政企满意度', score: null, rate: null, unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '政企满意度', score: null, rate: null, unit: '%' }
            ]
          }
        ]
      }
    ]
  },
  {
    targetId: 'KPI05',
    name: '投诉满意度',
    img: img4,
    dateTypes: [{ label: '周', value: 'week' }],
    hasMap: false, // 是否有地图
    mapMaxLevel: 2,
    children: []
  },
  {
    targetId: 'KPI04',
    name: '质量提升',
    img: img5,
    dateTypes: [
      { label: '月', value: 'month' } // monthrange
    ],
    restSerialNo: '34H7WNFl',
    mapMaxLevel: 2,
    children: [
      {
        targetId: '20202',
        name: '资费办理规范性',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '周', value: 'monthweek' } // monthweek
        ],
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '满意度', score: null, rate: null, unit: '%' },
              { label: '月均满意度', score: null, rate: null, unit: '%', excludeDateType: ['周'] } // 日期为周时，不显示
            ]
          },
          {
            name: '业务受理单位',
            tabId: '2',
            table: [
              { label: '满意度', score: null, rate: null, unit: '%' },
              { label: '月均满意度', score: null, rate: null, unit: '%', excludeDateType: ['周'] }
            ]
          }
        ]
      },
      {
        targetId: '20201',
        name: '流量套餐适配性',
        table: [
          { label: '三高客户数', score: null, rate: null, unit: '%' },
          { label: '三高占比', score: null, rate: null, unit: '%' },
          { label: '改善值', score: null, rate: null, unit: '%' }
        ]
      },
      {
        targetId: '20102',
        name: '5GSA时长驻留比',
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '驻留比', score: null, rate: null, unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '驻留比', score: null, rate: null, unit: '%' }
            ]
          }
        ]
      },
      // {
      //   targetId: '20105',
      //   name: '高投诉小区解决率',
      //   mapMaxLevel: 3,
      //   tabs: [
      //     {
      //       name: '趋势图',
      //       tabId: '1',
      //       table: [
      //         { label: '解决率', score: null, rate: null, unit: '%' }
      //       ]
      //     },
      //     {
      //       name: '地市',
      //       tabId: '2',
      //       table: [
      //         { label: '解决率', score: null, rate: null, unit: '%' }
      //       ]
      //     }
      //   ]
      // },
      {
        targetId: '20106',
        name: '家宽单用户中断时长',
        mapMaxLevel: 3,
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '时长(分钟)', score: null, rate: null, unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '时长(分钟)', score: null, rate: null, unit: '%' }
            ]
          }
        ]
      },
      {
        targetId: '20302',
        name: '影响客户工单占比',
        mapMaxLevel: 3,
        tabs: [
          {
            name: '趋势图',
            tabId: '1',
            table: [
              { label: '占比', score: null, rate: null, unit: '%' }
            ]
          },
          {
            name: '地市',
            tabId: '2',
            table: [
              { label: '占比', score: null, rate: null, unit: '%' }
            ]
          }
        ]
      }
    ]
  }
]



export const KPI1 = [
  { targetId: 'KPI01', name: '综合满意度', component: 'Tab1', img: img1, },
  { targetId: 'KPI02', name: '工信部申告', component: 'Tab2', img: img2 },
  /*{ targetId: 'KPI03', name: '投诉满意度', component: 'Tab3', img: img3 },*/
  { targetId: 'KPI04', name: '用后即评', component: 'Tab4', img: img4 },
  // { targetId: 'KPI05', name: '质量提升', component: 'Tab5', img: img5 },
]


export const childTabs = [
  /* {
     targetId: 'KPI0101',
     name: '工信部满意度',
     component: 'Tab1Child1',
     children: [
       [
         {
           targetId: 'KPI010101',
           chartConfigId: 'chart_1',
           id: '10605',
           dimType: '1',
           col: 12,
           name: '工信部综合满意度趋势',
           restSerialNo: 'LMdH7x1J',
           table: [
             { label: '移动综合满意度累计表现值', score: null, rate: null, unit: '%' },
             { label: '移动综合满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
           ]
         },
         {
           targetId: 'KPI010102',
           chartConfigId: 'chart_2',
           id: '10605',
           dimType: '2',
           col: 12,
           name: '工信部综合满意度区域',
           restSerialNo: 'LMdH7x1J',
           table: [
             { label: '移动综合满意度累计表现值', score: null, rate: null, unit: '%' }
           ]
         }
       ],
       [
         {
           targetId: 'KPI010103',
           id: '1060601',
           name: '工信部固定语音综合满意度',
           chartConfigId: 'chart_3',
           col: 8,
           restSerialNo: 'vpWMQynD',
           tabs: [
             {
               name: '趋势图',
               tabId: '1',
               table: [
                 { label: '移动固定电话满意度累计表现值', score: null, rate: null, unit: '%' },
                 { label: '移动固定电话满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
               ]
             },
             {
               name: '地市',
               tabId: '2',
               table: [
                 { label: '移动固定电话满意度累计表现值', score: null, rate: null, unit: '%' }
               ]
             }
           ],
         },
         {
           targetId: 'KPI010104',
           id: '1060602',
           chartConfigId: 'chart_3',
           name: '工信部固定上网综合满意度',
           col: 8,
           restSerialNo: 'vpWMQynD',
           tabs: [
             {
               name: '趋势图',
               tabId: '1',
               table: [
                 { label: '移动固定上网满意度累计表现值', score: null, rate: null, unit: '%' },
                 { label: '移动固定上网满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
               ]
             },
             {
               name: '地市',
               tabId: '2',
               table: [
                 { label: '移动固定上网满意度累计表现值', score: null, rate: null, unit: '%' }
               ]
             }
           ],
         },
         {
           targetId: 'KPI010105',
           id: '1060603',
           chartConfigId: 'chart_3',
           name: '工信部移动业务综合满意度',
           col: 8,
           restSerialNo: 'vpWMQynD',
           tabs: [
             {
               name: '趋势图',
               tabId: '1',
               table: [
                 { label: '移动移动业务满意度累计表现值', score: null, rate: null, unit: '%' },
                 { label: '移动移动业务满意度累计领先值', score: null, rate: null, unit: '%', isCal: true }
               ]
             },
             {
               name: '地市',
               tabId: '2',
               table: [
                 { label: '移动移动业务满意度累计表现值', score: null, rate: null, unit: '%' }
               ]
             }
           ],
         }
       ]
     ]
   },*/
  {
    targetId: 'KPI0103',
    name: '集团测评满意度',
    component: 'Tab1Child3',
    children: [
      [
        {
          targetId: 'KPI010103',
          id: '10802',
          name: '移动业务满意度',
          chartConfigId: 'KPI03_KPI0301',
          col: 12,
          restSerialNo: 'eEs2GWZt',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '手机满意度', score: null, rate: null, unit: '%' },
                { label: '手机领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060201'] },
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '手机满意度', score: null, rate: null, unit: '%' },
              ]
            }
          ],
        },
        {
          targetId: 'KPI010104',
          id: '10801',
          chartConfigId: 'KPI03_KPI0302',
          name: '固定宽带满意度',
          col: 12,
          restSerialNo: 'eEs2GWZt',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '家宽满意度', score: null, rate: null, unit: '%' },
                { label: '家宽领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060201'] },
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '家宽满意度', score: null, rate: null, unit: '%' },
              ]
            }
          ],
        },
      ]
    ]
  },
  { targetId: 'KPI0104', name: '省内测评满意度', component: 'Tab1Child4',
    children: [
      [
        {
          targetId: 'KPI010103',
          id: '1050602',
          name: '移动业务满意度',
          chartConfigId: 'KPI03_KPI0301',
          col: 12,
          restSerialNo: 'eEs2GWZt',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '手机满意度', score: null, rate: null, unit: '%' },
                { label: '手机领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060201'] },
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '手机满意度', score: null, rate: null, unit: '%' },
              ]
            }
          ],
        },
        {
          targetId: 'KPI010104',
          id: '1050601',
          chartConfigId: 'KPI03_KPI0302',
          name: '固定宽带满意度',
          col: 12,
          restSerialNo: 'eEs2GWZt',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '家宽满意度', score: null, rate: null, unit: '%' },
                { label: '家宽领先值', score: null, rate: null, unit: '%', isCal: true, includeTargetId: ['105060201'] },
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '家宽满意度', score: null, rate: null, unit: '%' },
              ]
            }
          ],
        },
      ],
      [
        {
          targetId: 'KPI010105',
          id: '1050603',
          name: '新入网分析',
          chartConfigId: 'KPI03_KPI0303',
          col: 12,
          restSerialNo: 'audit001',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '平均新入网数', score: 43182, rate: '--', unit: '%' }
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '地市平均新入网数', score: 8032, rate: '--', unit: '%' },
              ]
            }
          ],
        },
        {
          targetId: 'KPI010106',
          id: '1050604',
          name: '携入携出分析',
          chartConfigId: 'KPI03_KPI0304',
          col: 12,
          restSerialNo: 'audit002',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '平均携入携出数', score: 763, rate: '--', unit: '%' }
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '地市平均携入携出', score: 149, rate: '--', unit: '%' },
              ]
            }
          ],
        },
      ],
      [
        {
          targetId: 'KPI010107',
          id: '1050605',
          name: '5G套餐升级客户数',
          chartConfigId: 'KPI03_KPI0305',
          col: 12,
          restSerialNo: 'audit003',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '平均升级数', score: 784, rate: '--', unit: '%' }
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '地市平均升级数', score: 162, rate: '--', unit: '%' },
              ]
            }
          ],
        },
        {
          targetId: 'KPI010108',
          id: '1050606',
          name: '短信满意度',
          chartConfigId: 'KPI03_KPI0306',
          col: 12,
          restSerialNo: 'audit004',
          tabs: [
            {
              name: '趋势图',
              tabId: '1',
              table: [
                { label: '满意度', score: 93.66, rate: '--', unit: '%' }
              ]
            },
            {
              name: '地市',
              tabId: '2',
              table: [
                { label: '满意度', score: 93.66, rate: '--', unit: '%' },
              ]
            }
          ],
        },
      ]
    ]}
]

export const childTabs2 = {
  targetId: 'KPI02',
  name: '工信部申告',
  dateTypes: [
    { label: '日', value: 'date' }, // date
    { label: '月', value: 'month' } // monthrange
  ],
  restSerialNo: '7LxwnCvM',
  mapMaxLevel: 2,
  // 地图选项配置
  mapOption: {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const { name, data } = params
        const str = [name]
        for (let i = 0; i < 2; i++) {
          const targetName = i == 0 ? 'targetname' : `targetname${i}`
          const value = i == 0 ? 'value' : `value${i}`
          if (data[targetName]) {
            const [seriesName] = data[targetName].split('-')
            str.push(`${seriesName} ${data[value] || '-'}`)
          }
        }
        return str.join('</br>')
      }
    }
  },
  children: [
    {
      targetId: '302',
      name: '工信部百万申告率',
      ajaxIds: ['309', '302'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          { label: '移动工信部百万申告量月累计', score: null },
          { label: '移动工信部百万申告率月累计', score: null },
          { label: '移动工信部百万申告率月累计领先值', score: null, isCal: true }
        ]
      },
    },
    {
      targetId: '302',
      name: '工信部百万申告率',
      ajaxIds: ['309', '302'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          { label: '移动工信部百万申告量月累计', score: null },
          { label: '移动工信部百万申告率月累计', score: null }
        ]
      }
    },
    {
      targetId: '305',
      name: '七类顽疾',
      ajaxIds: ['306', '305'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          { label: '移动工信部携号转网申告量月累计', score: null },
          { label: '移动工信部携号转网申告率月累计', score: null },
          { label: '移动工信部携号转网申告率月累计领先值', score: null, isCal: true }
        ]
      },
    },
    {
      targetId: '305',
      name: '工信部携号转网申告率',
      ajaxIds: ['306', '305'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          { label: '移动工信部携号转网申告量月累计', score: null },
          { label: '移动工信部携号转网申告率月累计', score: null }
        ]
      }
    },
    {
      targetId: '307',
      name: '工信部有责申告量',
      ajaxIds: ['308', '307'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '趋势',
        tabId: '1',
        table: [
          { label: '移动工信部营销宣传申告量月累计', score: null },
          { label: '移动工信部营销宣传申告率月累计', score: null },
          { label: '移动工信部营销宣传申告率月累计领先值', score: null, isCal: true }
        ]
      },
    },
    {
      targetId: '307',
      name: '工信部营销宣传申告率',
      ajaxIds: ['308', '307'],
      barLineData: [],
      echartsKey: '',
      tabs: {
        name: '归责单位',
        tabId: '2',
        table: [
          { label: '移动工信部营销宣传申告量月累计', score: null },
          { label: '移动工信部营销宣传申告率月累计', score: null }
        ]
      },
    },
  ]
}

export const childTabs4 = [
  {
    targetId: 'KPI0401',
    name: '个人业务',
    component: 'Tab4Child1',
    // 个人业务模块通用
    echartsMaps: [
      [
        {
          chartConfigId: 'KPI04-ART01',
          col: 12,
          name: '满意度趋势',
          restSerialNo: 'trend',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
        {
          chartConfigId: 'KPI04-ART03',
          col: 12,
          name: '满意度区域',
          restSerialNo: 'area',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        }
      ],
      [
        {
          chartConfigId: 'KPI04-ART02',
          col: 24,
          name: '满意度区县排名',
          isSort: true,
          restSerialNo: 'rank',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
      ]

    ],
    children: [
      {
        targetId: 'KPI040101',
        id: '20202',
        name: '资费办理规范性',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
        tabs: [
          {
            id: '20202',
            name: '总体',
            // child: [
            //   {
            //     targetId: 'KPI040101-01',
            //     id: '202020102',
            //     name: '关键信息告知到位'
            //   },
            //   {
            //     targetId: 'KPI040101-02',
            //     id: '202020103',
            //     name: '客户订购正向确认'
            //   },
            //   {
            //     targetId: 'KPI040101-03',
            //     id: '202020104',
            //     name: '收到办理成功提醒短信'
            //   },
            //   {
            //     targetId: 'KPI040101-04',
            //     id: '202020105',
            //     name: '通知短信规范透明'
            //   }
            // ]
          },
          {
            id: '2020201',
            name: '合约办理',
            child: [
              {
                targetId: 'KPI040101-01',
                id: '202020102',
                name: '关键信息告知到位'
              },
              {
                targetId: 'KPI040101-02',
                id: '202020103',
                name: '客户订购正向确认'
              },
              {
                targetId: 'KPI040101-03',
                id: '202020104',
                name: '收到办理成功提醒短信'
              },
              {
                targetId: 'KPI040101-04',
                id: '202020105',
                name: '通知短信规范透明'
              }
            ]
          },
          {
            id: '2020202',
            name: '套餐变更',
            child: [
              {
                targetId: 'KPI040101-01',
                id: '202020202',
                name: '关键信息告知到位'
              },
              {
                targetId: 'KPI040101-02',
                id: '202020203',
                name: '客户订购正向确认'
              },
              {
                targetId: 'KPI040101-03',
                id: '202020204',
                name: '收到办理成功提醒短信'
              },
              {
                targetId: 'KPI040101-04',
                id: '202020205',
                name: '通知短信规范透明'
              }
            ]
          }
        ]
      },
      {
        targetId: 'KPI040102',
        id: '10101',
        name: '资费套餐',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '1010101',
            name: '宣传推广'
          },
          {
            targetId: 'KPI040102-02',
            id: '1010102',
            name: '业务规则'
          },
          {
            targetId: 'KPI040102-03',
            id: '1010103',
            name: '明白消费'
          }
        ]
      },
      {
        targetId: 'KPI040103',
        id: '1010201',
        name: '手机上网',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ],
        tabs: [
          {
            id: '1010201',
            name: '总体',
            // child: [
            //   {
            //     targetId: 'KPI040103-01',
            //     id: '10102010101',
            //     name: '网络覆盖与信号强度'
            //   },
            //   {
            //     targetId: 'KPI040103-02',
            //     id: '10102010102',
            //     name: '手机上网速度'
            //   },
            //   {
            //     targetId: 'KPI040103-03',
            //     id: '10102010103',
            //     name: '手机上网稳定性'
            //   },
            // ]
          },
          {
            id: '101020101',
            name: '5G',
            child: [
              {
                targetId: 'KPI040103-01',
                id: '10102010101',
                name: '网络覆盖与信号强度'
              },
              {
                targetId: 'KPI040103-02',
                id: '10102010102',
                name: '手机上网速度'
              },
              {
                targetId: 'KPI040103-03',
                id: '10102010103',
                name: '手机上网稳定性'
              },
            ]
          },
          {
            id: '101020102',
            name: '非5G',
            child: [
              {
                targetId: 'KPI040103-01',
                id: '10102010201',
                name: '网络覆盖与信号强度'
              },
              {
                targetId: 'KPI040103-02',
                id: '10102010202',
                name: '手机上网速度'
              },
              {
                targetId: 'KPI040103-03',
                id: '10102010203',
                name: '手机上网稳定性'
              },
            ]
          }
        ]
      },
      {
        targetId: 'KPI040104',
        id: '1010202',
        name: '语音通话',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040104-01',
            id: '101020201',
            name: '信号强度'
          },
          {
            targetId: 'KPI040104-02',
            id: '101020202',
            name: '通话清晰'
          },
          {
            targetId: 'KPI040104-03',
            id: '101020203',
            name: '通话稳定'
          }
        ]
      },
      {
        targetId: 'KPI040105',
        id: '101',
        name: '手机客户满意度',
        issueNum: true,
        dateTypes: [
          { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
      },

    ]
  },
  {
    targetId: 'KPI0402',
    name: '家庭业务',
    id: '102',
    component: 'Tab4Child2',
    echartsMaps: [
      [
        {
          chartConfigId: 'KPI04-ART01',
          col: 12,
          name: '满意度趋势',
          restSerialNo: 'trend',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
        {
          chartConfigId: 'KPI04-ART03',
          col: 12,
          name: '满意度区域',
          restSerialNo: 'area',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        }
      ],
      [
        {
          chartConfigId: 'KPI04-ART02',
          col: 24,
          name: '满意度区县排名',
          isSort: true,
          restSerialNo: 'rank',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
      ]

    ],
    children: [
      {
        targetId: 'KPI040201',
        id: '10202',
        name: '上网质量',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040201-01',
            id: '1020201',
            name: '上网速度'
          },
          {
            targetId: 'KPI040201-02',
            id: '1020202',
            name: '网络连接稳定性'
          },
          {
            targetId: 'KPI040201-03',
            id: '1020203',
            name: '重点场景上网感知',
            child: [
              {
                id: '102020301',
                name: '浏览图文'
              },
              {
                id: '102020302',
                name: '看视频'
              },
              {
                id: '102020303',
                name: '玩游戏'
              },
              {
                id: '102020304',
                name: '看互联网电视'
              },
            ]
          }
        ]
      },
      {
        targetId: 'KPI040202',
        id: '10204',
        name: '互联网电视',
        dateTypes: [
          // { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040202-01',
            id: '1020401',
            name: '整体感知'
          },
          {
            targetId: 'KPI040202-02',
            id: '1020402',
            name: '终端感知质量'
          },
          {
            targetId: 'KPI040202-03',
            id: '1020403',
            name: '内容体验',
          },
          {
            targetId: 'KPI040202-04',
            id: '1020404',
            name: '使用体验',
          },
          {
            targetId: 'KPI040202-05',
            id: '1020405',
            name: '业务办理推动体验',
          },
          {
            targetId: 'KPI040202-06',
            id: '1020406',
            name: '故障维修',
          }
        ]
      },
      {
        targetId: 'KPI040203',
        id: '10203',
        name: '装维服务',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040202-01',
            id: '1020301',
            name: '装机服务'
          },
          {
            targetId: 'KPI040202-02',
            id: '1020302',
            name: '故障维修',
          },
          // {
          //   targetId: 'KPI040202-03',
          //   id: '1020303',
          //   name: '移机服务',
          // }
        ]
      },
      {
        targetId: 'KPI040204',
        id: '10201',
        name: '资费套餐',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '1020101',
            name: '宣传推广'
          },
          {
            targetId: 'KPI040102-02',
            id: '1020102',
            name: '业务规则'
          },
          {
            targetId: 'KPI040102-03',
            id: '1020103',
            name: '明白消费'
          }
        ]
      },
      {
        targetId: 'KPI040205',
        id: '102',
        name: '家宽客户满意度',
        issueNum: true,
        dateTypes: [
          { label: '月', value: 'month' }, // month
          // { label: '周', value: 'monthweek' }, // monthweek
          { label: '季度', value: 'quarter' } // quarter
        ],
      },

    ]
  },
  { targetId: 'KPI0403', name: '触点业务', component: 'Tab4Child3',
    id: '10103',
    echartsMaps: [
      [
        {
          chartConfigId: 'KPI04-ART01',
          col: 12,
          name: '满意度趋势',
          restSerialNo: 'trend',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
        {
          chartConfigId: 'KPI04-ART03',
          col: 12,
          name: '满意度区域',
          restSerialNo: 'area',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        }
      ],
      [
        {
          chartConfigId: 'KPI04-ART02',
          col: 24,
          isSort: true,
          name: '满意度区县排名',
          restSerialNo: 'rank',
          table: [
            { label: '满意度', score: null, rate: null },
            { label: '环比', score: null, rate: null, unit: '%'}
          ]
        },
      ]

    ],
    children: [
      {
        targetId: 'KPI0403-01',
        id: '1010303',
        name: '手厅',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ],
        child: [
          {
            targetId: 'KPI040102-01',
            id: '101030301',
            name: '整体感知'
          },
          {
            targetId: 'KPI040102-02',
            id: '101030305',
            name: '在线客服感知'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030303',
            name: '业务丰富度'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030302',
            name: '界面友好性'
          },
          {
            targetId: 'KPI040102-03',
            id: '101030304',
            name: '使用体验'
          }
        ]
      },
      {
        targetId: 'KPI0403-02',
        id: 't0001',
        name: '营业厅',
        dateTypes: [
          { label: '月', value: 'month' }, // month
          { label: '季度', value: 'quarter' } // quarter
        ]
      },
    ]}
]




/**
 * 计算领先值
 * @param {移动} first
 * @param {电信} second
 * @param {联通} third
 * @returns
 */
function calLeadingValue(first, second, third) {
  if (!second && !third) return null
  let secleading, thileading
  if (second) {
    secleading = (Number(first) - Number(second)).toFixed(2)
  }
  if (third) {
    thileading = (Number(first) - Number(third)).toFixed(2)
  }
  if (secleading == undefined) return thileading
  if (thileading == undefined) return secleading
  return Math.min(secleading, thileading)
}

/**
 * 空值数组
 */
const emptyValueArray = [null, undefined, '', '-']
/**
 * 单位配置
 */
const percentUnitTextObj = { '环比': '%', '同比': '%' }
/**
 * tooltip加单位判断
 * @param {*} value
 * @param {*} unit
 * @returns
 */
function formatterValueUnit(value, unit = '%') {
  return emptyValueArray.includes(value) ? '-' : `${value}${unit}`
}
/**
 * tooltip文字百分比的判断
 * @param {tooltip数据} item
 */
export function formatterTextUnit(item) {
  const { seriesName, value } = item
  return `${seriesName}: ${formatterValueUnit(value, percentUnitTextObj[seriesName] || '')}`
}
/**
 * 服务指标的echarts图表配置
 * 大类指标id_二级指标id_(趋势/地市)Id：{
 *  option: 图表组件配置
 *  keyMap: 图表组件字段配置
 *  echartComponent: 配置特定的echarts组件，默认是ServiceBarLine组件
 *  mapMaxLevelSelect: true 地图最大级别时，是否关联选中城市
 * }
 */
export const KPI_ECHARTS = {
  // 工信部满意度
  'chart_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name, data: { value1, value2, value3 } }] = params
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}工信部综合满意度: ${item.value || '-'}
          环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: '满意度',
      legend: ['移动', '电信', '联通'],
      control: ['line', 'line', 'line'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'chart_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
             环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: '表现值',
      legend: ['工信部综合表现值'],
      control: ['bar'],
      series: ['value1']
    },
    mapMaxLevelSelect: true
  },
  'chart_3_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name, data: { value1, value2, value3, childSplitActiveName } }] = params
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}${childSplitActiveName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: '满意度',
      legend: ['移动', '电信', '联通'],
      control: ['line', 'line', 'line'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'chart_3_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: '表现值',
      legend: ['工信部综合表现值'],
      control: ['bar'],
      series: ['value1']
    },
    mapMaxLevelSelect: true
  },
  // 工信部申诉
  'KPI02_302_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name, data: { value1, value2, value3 } }] = params
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`]
          const tips = ['移动工信部百万申告量', '电信工信部百万申告量', '联通工信部百万申告量', '移动工信部百万申告率', '电信工信部百万申告率', '联通工信部百万申告率']
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_302_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部百万申告量', '工信部百万申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_305_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name, data: { value1, value2, value3 } }] = params
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`]
          const tips = ['移动工信部携号转网申告量', '电信工信部携号转网申告量', '联通工信部携号转网申告量', '移动工信部携号转网申告率', '电信工信部携号转网申告率', '联通工信部携号转网申告率']
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_305_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
              环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部携号转网申告量', '工信部携号转网申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_307_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name, data: { value1, value2, value3 } }] = params
          const str = [`${name} 领先值: ${calLeadingValue(value1, value2, value3) || '--'}`]
          const tips = ['移动工信部营销宣传申告量', '电信工信部营销宣传申告量', '联通工信部营销宣传申告量', '移动工信部营销宣传申告率', '电信工信部营销宣传申告率', '联通工信部营销宣传申告率']
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${tips[index]}: ${item.value || '-'}
          环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 0, 0, 1, 1, 1],
      legend: ['移动', '电信', '联通'],
      control: ['bar', 'bar', 'bar', 'line', 'line', 'line'],
      series: ['value1', 'value2', 'value3', 'value4', 'value5', 'value6']
    }
  },
  'KPI02_307_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['工信部营销宣传申告量', '工信部营销宣传申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'value2']
    },
    mapMaxLevelSelect: true
  },
  'KPI02_303_1': {
    // echartComponent 配置特定的echarts组件，默认是ServiceBarLine组件
    echartComponent: 'ServiceTopSwitchChart'
  },
  'KPI02_303_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item, index) => {
            str.push(
              `${item.marker} ${item.seriesName}: ${item.value || '-'}
            环比: ${formatterValueUnit(item.data[`momrate${index + 1}`])}`
            )
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '申告量',
        '申告率'
      ],
      yAxisIndex: [0, 1],
      legend: ['申告量', '申告率'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  // 自测满意度
  'KPI03_KPI0301_1': { // 手机满意度-趋势
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['手机满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_KPI0301_2': {// 手机满意度-地市
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['手机满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  'KPI03_KPI0302_1': { // 家宽满意度-趋势
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['家宽满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_KPI0302_2': { // 家宽满意度-地市
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['家宽满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  // 应对审计 新入网分析 趋势图
  'KPI03_KPI0303_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: ['新入网数'],
      yAxisIndex: [0],
      legend: ['新入网数'],
      control: ['bar'],
      series: ['value1']
    }
  },
  // 应对审计 新入网分析 地市
  'KPI03_KPI0303_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: ['新入网数'],
      yAxisIndex: [0],
      legend: ['新入网数'],
      control: ['bar'],
      series: ['value1']
    },
  },
  // 应对审计 携入携出分析 趋势图
  'KPI03_KPI0304_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: ['携入携出数'],
      yAxisIndex: [0],
      legend: ['携入携出数'],
      control: ['bar'],
      series: ['value1']
    }
  },
  // 应对审计 月携入携出析 地市
  'KPI03_KPI0304_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: ['携入携出数'],
      yAxisIndex: [0],
      legend: ['携入携出数'],
      control: ['bar'],
      series: ['value1']
    },
  },
  // 应对审计 5G套餐升级客户数 趋势图
  'KPI03_KPI0305_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: ['升级数'],
      yAxisIndex: [0],
      legend: ['升级数'],
      control: ['bar'],
      series: ['value1']
    }
  },
  // 应对审计 5G套餐升级客户数 地市
  'KPI03_KPI0305_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: ['升级数'],
      yAxisIndex: [0],
      legend: ['升级数'],
      control: ['bar'],
      series: ['value1']
    },
  },
  // 应对审计 短信满意度 趋势图
  'KPI03_KPI0306_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: ['满意度'],
      yAxisIndex: [0],
      legend: ['满意度'],
      control: ['bar'],
      series: ['value1']
    }
  },
  // 应对审计 短信满意度 地市
  'KPI03_KPI0306_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: ['满意度'],
      yAxisIndex: [0],
      legend: ['满意度'],
      control: ['bar'],
      series: ['value1']
    },
  },
  'KPI03_103_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['政企满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI03_103_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['政企满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  // 质量提升
  'KPI04_20201': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${item.value || '-'}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '客户数(万户)',
          nameLocation: 'end',
          nameTextStyle: {
            padding: [0, 0, 0, 5]
          }
        },
        {
          name: '比值',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1, 0],
      legend: ['三高客户数', '三高占比', '改善值'],
      control: ['bar', 'line', 'bar'],
      series: ['value1', 'value2', 'value3']
    }
  },
  'KPI04_20202_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20202_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        '满意度',
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20102_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '驻留比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['驻留比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20102_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '驻留比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['驻留比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    },
    mapMaxLevelSelect: true
  },
  'KPI04_20105_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '解决率',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['解决率', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20105_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '解决率',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['解决率', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20106_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker}  ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '时长(分钟)',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['时长', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20106_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker}  ${formatterTextUnit(item)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '时长(分钟)',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['时长', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },

  // 影响客户工单占比
  'KPI04_20302_1': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statdate',
      yAxis: [
        {
          name: '占比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['占比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  'KPI04_20302_2': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            str.push(`${item.marker} ${item.seriesName}: ${formatterValueUnit(item.value)}`)
          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityname',
      yAxis: [
        {
          name: '占比',
          axisLabel: {
            formatter: '{value}%'
          }
        },
        {
          name: '环比',
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['占比', '环比'],
      control: ['bar', 'line'],
      series: ['value1', 'momrate1']
    }
  },
  // 趋势图 / 满意度趋势   --- 用后即评模块
  'KPI04-ART03': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`)
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
            }

          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityName',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',

          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['score', 'momrate']
    }
  },
  'KPI04-ART01': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`)
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
            }

          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'statDate',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',
          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度', '环比'],
      control: ['bar', 'line'],
      series: ['score', 'momrate']
    }
  },
  'KPI04-ART02': {
    option: {
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const [{ name }] = params
          const str = [name]
          params.forEach((item) => {
            if(item.seriesName == '满意度') {
              str.push(`${item.marker} ${item.seriesName}:  ${item.value}`)
              str.push(`<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#4afede;"></span> 环比:  ${item.data.momrate ? (item.data.momrate +'%')  :'-'}`)
            }else{
              str.push(`${item.marker} ${item.seriesName}:  ${formatterValueUnit(item.value)}`)
            }

          })
          return str.join('<br/>')
        }
      }
    },
    keyMap: {
      xAxis: 'cityName',
      yAxis: [
        {
          name: '满意度',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          name: '',
          visible: false ,
          axisLabel: {
            show: false,
            formatter: '{value}%'
          }
        }
      ],
      yAxisIndex: [0, 1],
      legend: ['满意度'],
      control: ['bar'],
      series: ['score','momrate']
    }
  }
}

// 根据年度第几周算出日期
export const getStartAndEndDate = (year, week) => {
  const startDate = new Date(year, 0, 1 + (week - 1) * 7);
  const endDate = new Date(year, 0, 1 + week * 7);
  const dayOfWeek = startDate.getDay();

  // 如果一年的第一天不是周一，需要调整计算
  if (dayOfWeek !== 1) {
    startDate.setDate(startDate.getDate() - dayOfWeek + 1);
  }

  // 如果一年的最后一天不是周日，需要调整计算
  if (endDate.getDay() !== 0) {
    endDate.setDate(endDate.getDate() + (7 - endDate.getDay()));
  }

  return {
    startDate: startDate.toISOString().slice(0, 10),
    endDate: endDate.toISOString().slice(0, 10)
  };
}
// yyyyMMdd格式转换为yyyy年第ww周
export const formatDateWeek = (inputDate, format = 'yyyy年第ww周') => {
  const year = inputDate.getFullYear();
  const month = inputDate.getMonth() + 1; // 月份从0开始，所以要加1
  const day = inputDate.getDate();

  // 计算周数
  const startOfYear = new Date(year, 0, 1);
  const days = Math.floor((inputDate - startOfYear) / 86400000); // 一天的毫秒数
  const weekNumber = Math.floor(days / 7) + 1;

  // 格式化月份和日期，确保是两位数
  const formattedMonth = month < 10 ? `0${month}` : month;
  const formattedDay = day < 10 ? `0${day}` : day;
  const map = new Map([
    ['yyyy年第ww周', `${year}年第${weekNumber}周`],
    ['yyyy-MM-dd', `${year}-${formattedMonth}-${formattedDay}`],
    ['yyyyMMdd', `${year}${formattedMonth}${formattedDay}`],
    ['yyyy-MM', `${year}-${formattedMonth}`],
    ['yyyyMM', `${year}${formattedMonth}`],
  ]);
  return map.get(format) || '';
}
