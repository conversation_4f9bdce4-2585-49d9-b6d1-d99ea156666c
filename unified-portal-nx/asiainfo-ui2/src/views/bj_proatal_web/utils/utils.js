function deepClone(obj) {
  // 先检测是不是数组和Object
  // let isArr = Object.prototype.toString.call(obj) === '[object Array]';
  const isArr = Array.isArray(obj)
  const isJson = Object.prototype.toString.call(obj) === '[object Object]'
  if (isArr) {
    // 克隆数组
    const newObj = []
    for (let i = 0; i < obj.length; i++) {
      newObj[i] = deepClone(obj[i])
    }
    return newObj
  } else if (isJson) {
    // 克隆Object
    const newObj = {}
    for (const i in obj) {
      newObj[i] = deepClone(obj[i])
    }
    return newObj
  }
  // 不是引用类型直接返回
  return obj
}

export default {
  // 序列化请求参数到url
  serialize: (obj) => {
    const str = []
    Object.keys(obj).forEach((key) => {
      let value = obj[key]
      if (typeof (value) === 'object') {
        value = encodeURIComponent(JSON.stringify(obj[key]))
      } else {
        value = encodeURIComponent(value)
      }
      str.push(`${encodeURIComponent(key)}=${value}`)
    })
    return str.join('&')
  },
  IEVersion: () => {
    const { userAgent } = navigator // 取得浏览器的userAgent字符串
    const isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 // 判断是否IE<11浏览器
    const isEdge = userAgent.indexOf('Edge') > -1 && !isIE // 判断是否IE的Edge浏览器
    const isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
    if (isIE) {
      const reIE = new RegExp('MSIE (\\d+\\.\\d+);')
      reIE.test(userAgent)
      const fIEVersion = parseFloat(RegExp.$1)
      if (fIEVersion === 7) {
        return 7
      } if (fIEVersion === 8) {
        return 8
      } if (fIEVersion === 9) {
        return 9
      } if (fIEVersion === 10) {
        return 10
      }
      return 6 // IE版本<=7
    } if (isEdge) {
      return 'edge' // edge
    } if (isIE11) {
      return 11 // IE11
    }
    return -1 // 不是ie浏览器
  },
  formatterDate(val, type) {
    val = new Date(val)
    const year = val.getFullYear()
    const month = (val.getMonth() + 1) < 10 ? `0${val.getMonth() + 1}` : val.getMonth() + 1
    const monthSpe = (val.getMonth() + 1) < 10 ? `${val.getMonth() + 1}` : val.getMonth() + 1
    const date = val.getDate() < 10 ? `0${val.getDate()}` : val.getDate()

    if (type === 'y-m-d') {
      return `${year}-${month}-${date} 23:59:59`
    } else if (type == 'yyyy-MM-dd') {
      return `${year}-${month}-${date}`
    } else if (type == 'yyyyMM') {
      return `${year}${month}`
    } else if (type == 'yyyy年MM月') {
      return `${year}年${month}月`
    } else if (type == 'yyyyMMS') {
      return `${year}${monthSpe}`
    }else if (type == 'yyyy-MM') {
      return `${year}-${month}`
    } else {
      return `${year}${month}${date}`
    }
  },
  // 日期格式化 yyyy年MM月
  formatDate: (date, type, slitType) => {
    let slit = slitType
    slit === undefined ? slit = '' : slit = '-'
    const myyear = date.getFullYear()
    let mymonth = date.getMonth() + 1
    let myweekday = date.getDate()

    if (mymonth < 10) {
      mymonth = `0${mymonth}`
    }
    if (myweekday < 10) {
      myweekday = `0${myweekday}`
    }
    if (type === 'day') {
      return (myyear + slit + mymonth + slit + myweekday)
    }
    return (myyear + slit + mymonth)
  },
  mergeObj: () => {
    const args = []
    const res = {}
    for (var i = 0; i < arguments.length; i++) {
      args.push(arguments[i])
    }
    args.reduce((last, item) => {
      Object.entries(item).forEach(arr => {
        const key = arr[0]
        const value = arr[1]
        if (Object.prototype.hasOwnProperty.call(last, key)) {
          if (value.constructor && value.constructor === Object) {
            last[key] = mergeObj(last[key], value)
          } else {
            last[key] = deepClone(value)
          }
        } else {
          last[key] = deepClone(value)
        }
      })
      return last
    }, res)
    return res
  },
  // 手机网络专题处理保留2位小数的逻辑
  dealFloatNum: function(v) {
    if (v === 0 || v === '0') { // 等于0的情况
      return v
    } else if (!v) { // 没有值
      return '-'
    } else if (isInt(v)) { // 有值的情况 并且是整数
      return v
    } else { // 有值 并且是小数
      const len = String(v).length
      const y = String(v).indexOf('.') + 1// 获取小数点的位置
      const count = String(v).length - y// 获取小数点后的个数

      if (count > 2) { // 2位小数
        if (v < 0.005) {
          return 0.01
        }
        return Number(v).toFixed(2)
      }
      if (count == 2) {
        //  判断是不是.00 .x0
        if (String(v).indexOf('.00') != -1) {
          return Number(v).toFixed(0)
        }
        if (String(v).slice(len - 1) == 0 || String(v).slice(len - 1) == '0') { // 判断最后一位是否是0
          return Number(v).toFixed(1)
        }
        return Number(v).toFixed(2)
      }
      if (count == 1) {
        if (String(v).indexOf(y + 1) == 0) { // .0 情况处理
          return Number(v).toFixed(0)
        }
        return Number(v).toFixed(1)
      }
    }
  },
  // 处理同比环比的政府取值
  handlerMomrateAndYoyrate: function(arr) {
    if (Array.isArray(arr)) {
      arr.forEach(x => {
        if (x.momrate && x.momrate != '-') {
          if (x.momrateType === '1' || x.momratetype === '1' || x.momratetype === 1 ||x.momrateType===1|| x.momrate_type == '1') {
            if (x.momrate === 0 || x.momrate === '0') {
              x.momrate = x.momrate
            } else {
              x.momrate = `-${x.momrate}`
            }
          }
        }
        if (x.yoyrate && x.yoyrate != '-') {
          if (x.yoyrateType === '1' || x.yoyratetype === '1' || x.yoyrate_type == '1') {
            if (x.yoyrate === 0 || x.yoyrate === '0') {
              x.yoyrate = x.yoyrate
            } else {
              x.yoyrate = `-${x.yoyrate}`
            }
          }
        }
        if (x.eoms && x.eoms != '-') {
          if (x.eomstype === '1' || x.eomsType === '1' || x.eoms_type === '1') {
            if (x.eoms === 0 || x.eoms === '0') {
              x.eoms = x.eoms
            } else {
              x.eoms = `${x.eoms}`
            }
          }
        }
        if (x.yearbasis && x.yearbasis != '-') {
          if (x.yearbasisType === '1' || x.yearbasistype || x.yearbasis_type == '1') {
            if (x.yearbasis === 0 || x.yearbasis === '0') {
              x.yearbasis = x.yearbasis
            } else {
              x.yearbasis = `-${x.yearbasis}`
            }
          }
        }
      })
    }
    return arr
  },
  /**
   * 处理百分比的取值
   * @param {*} arr  处理数组
   * @param {*} momrate 需要处理的百分比字段
   * @param {*} momratetype 用于判断升降的字段
   * @returns
   */
  handlerMomrate: function(arr, momrate, momratetype) {
    if (Array.isArray(arr)) {
      arr.forEach(x => {
        if (x[momrate] && x[momrate] != '-') {
          if (x[momratetype] === '1') {
            if (x[momrate] === 0 || x[momrate] === '0') {
              x[momrate] = x[momrate]
            } else {
              x[momrate] = `-${x[momrate]}`
            }
          }
        }
      })
    }
    return arr
  },
  // 全屏相关的js函数
  fullScreen(element) {
    var requestMethod = element.requestFullScreen || element.webkitRequestFullScreen || element.mozRequestFullScreen || element.msRequestFullScreen
    if (requestMethod) {
      requestMethod.call(element)
    } else if (typeof window.ActiveXObject !== 'undefined') { // for Internet Explorer
      var wscript = new ActiveXObject('WScript.Shell')
      if (wscript !== null) {
        wscript.SendKeys('{F11}')
      }
    }
  },
  // 退出全屏
  exitFullScreen() {
    var exitMethod = document.cancelFullScreen || document.webkitCancelFullScreen || document.mozCancelFullScreen || document.exitFullScreen
    if (exitMethod) {
      exitMethod.call(document)
    } else if (typeof window.ActiveXObject !== 'undefined') {
      var wscript = new ActiveXObject('WScript.Shell')
      if (wscript != null) {
        wscript.SendKeys('{F11}')
      }
    }
  },
  // 判断当前是否全屏。
  isFullScreen(element) {
    return (
      element.fullscreen ||
        element.mozFullScreen ||
        element.webkitIsFullScreen ||
        element.webkitFullScreen ||
        element.msFullScreen
    )
  },
  // 文件下载
  downloadBlob: (blob, fileName) => {
    const url = window.URL.createObjectURL(new Blob([blob]))

    if (window.navigator.msSaveBlob) {
      try {
        window.navigator.msSaveBlob(blob, fileName)
      } catch (e) {
        console.log(e)
      }
    } else {
      const link = document.createElement('a')
      link.href = url
      link.setAttribute(
        'download',
        fileName
      )
      link.click()
    }
  }
}

function isInt(n) {
  if (n && typeof n === 'string') {
    n = Number(n)
    return Number(n) === n && n % 1 === 0
  }
  return Number(n) === n && n % 1 === 0
}

