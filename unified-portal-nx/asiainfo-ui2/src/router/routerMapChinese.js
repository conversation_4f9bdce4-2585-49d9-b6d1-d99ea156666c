export default [

  {
    'id': 100101,
    'menuName': '集团考核服务KPI',
    'componentName': 'satisfaction',
    'menuCode': 'jtkhfwkpi',
    'menuUrl': '/satisfaction',
    'sn': 1,
    'parentId': 1001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示集团满意度考核指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 100102,
    'menuName': '投诉概览',
    'componentName': 'complainReview',
    'menuCode': 'tsgl',
    'menuUrl': '/complainReview',
    'sn': 2,
    'parentId': 1001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示投诉、申诉、升级、热点问题等指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 100201,
    'menuName': '端到端服务质量',
    'componentName': 'teleservice',
    'menuCode': 'ddddly',
    'menuUrl': '/teleservice',
    'sn': 1,
    'parentId': 1002,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示集团满意度考核指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 200101,
    'menuName': '客户管理入口',
    'componentName': 'customer',
    'menuCode': 'khglrk',
    'menuUrl': '/customer',
    'sn': 1,
    'parentId': 2001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '点击进入客户管理页面',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 200102,
    'menuName': '客群管理入口',
    'componentName': 'customergroup',
    'menuCode': 'kqglrk',
    'menuUrl': '/customergroup',
    'sn': 2,
    'parentId': 2001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '点击进入客群管理页面',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 200103,
    'menuName': '短信模板入口',
    'componentName': 'msgtep',
    'menuCode': 'dxmbrk',
    'menuUrl': '/msgtep',
    'sn': 3,
    'parentId': 2001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '点击进入短信模板页面',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300201,
    'menuName': '手机网络专题展示',
    'componentName': 'MobileNetwork',
    'menuCode': 'sjwlztzs',
    'menuUrl': '/MobileNetwork',
    'sn': 1,
    'parentId': 3002,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示手机网络专题相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300202,
    'menuName': '移动网络质量投诉分析展示',
    'componentName': 'MobileTraffic',
    'menuCode': 'ydwltsfxzs',
    'menuUrl': '/MobileTraffic',
    'sn': 2,
    'parentId': 3002,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '移动网络质量投诉分析相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300301,
    'menuName': '手机资费及营销专题展示',
    'componentName': 'mobilePhoneTariff',
    'menuCode': 'sjzfjyxzt',
    'menuUrl': '/mobilePhoneTariff',
    'sn': 1,
    'parentId': 3003,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '手机资费及营销专题展示',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300501,
    'menuName': '家庭业务投诉分析',
    'componentName': 'homeBroadband',
    'menuCode': 'jkbqyy',
    'menuUrl': '/homeBroadband',
    'sn': 1,
    'parentId': 3005,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '家宽标签运营展示',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300502,
    'menuName': '家庭业务报障分析',
    'componentName': 'homeBroadbandEr',
    'menuCode': 'jtywbzfx',
    'menuUrl': '/homeBroadbandEr',
    'sn': 2,
    'parentId': 3005,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示家庭业务报障相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300503,
    'menuName': '家庭标签运营分析',
    'componentName': 'homeBroadbandLab',
    'menuCode': 'jtbqyyfx',
    'menuUrl': '/homeBroadbandLab',
    'sn': 3,
    'parentId': 3005,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示家宽标签运营相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300504,
    'menuName': '家宽标准指标趋势',
    'componentName': 'homeBroadbandTrend',
    'menuCode': 'jkbzzbqs',
    'menuUrl': '/homeBroadbandTrend',
    'sn': 4,
    'parentId': 3005,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示家宽标准指标趋势相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300505,
    'menuName': '家宽感知客群分析',
    'componentName': 'homeBroadbandSe',
    'menuCode': 'jkgzkqfx',
    'menuUrl': '/homeBroadbandSe',
    'sn': 5,
    'parentId': 3005,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '家宽感知客群分析相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300401,
    'menuName': '投诉指标监控与预警体系展示',
    'componentName': 'complainPrewarining',
    'menuCode': 'tszbjkyyjtx',
    'menuUrl': '/complainPrewarining',
    'sn': 1,
    'parentId': 3004,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '投诉指标监控与预警体系展示',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300402,
    'menuName': '异动投诉监控专题展示',
    'componentName': 'caMonitor',
    'menuCode': 'ydtsjkztzs',
    'menuUrl': '/caMonitor',
    'sn': 2,
    'parentId': 3004,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '异动投诉监控专题展示',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300101,
    'menuName': '政企满意度展示',
    'componentName': 'GovStatisfaction',
    'menuCode': 'zqmydzs',
    'menuUrl': '/GovStatisfaction',
    'sn': 1,
    'parentId': 3001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '政企满意度指标页面展示',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300102,
    'menuName': '政企服务分析',
    'componentName': 'gov',
    'menuCode': 'zqfwfx',
    'menuUrl': '/gov',
    'sn': 2,
    'parentId': 3001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '政企服务分析入口',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300601,
    'menuName': '触点服务专题展示',
    'componentName': 'contactor',
    'menuCode': 'cdfwztzs',
    'menuUrl': '/contactor',
    'sn': 1,
    'parentId': 3006,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '展示触点服务专题相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 300602,
    'menuName': '服务投诉流程监控',
    'componentName': 'complaintMonitoring',
    'menuCode': 'fwtslcjk',
    'menuUrl': '/complaintMonitoring',
    'sn': 2,
    'parentId': 3006,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '服务投诉流程监控相关指标情况',
    'isExternal': '0',
    'menuList': []
  },
  {
    'id': 500101,
    'menuName': '工单管理',
    'componentName': null,
    'menuCode': 'gdgl',
    'menuUrl': 'http://workflow-nx.asiainfo.work/login?token=',
    'sn': 1,
    'parentId': 5001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '跳转到工单管理页面',
    'isExternal': '1',
    'menuList': []
  },
  {
    'id': 500102,
    'menuName': '工作流看板',
    'componentName': 'WorkflowPanel',
    'menuCode': 'gzlkb',
    'menuUrl': '/WorkflowPanel',
    'sn': 2,
    'parentId': 5001,
    'icon': null,
    'createBy': null,
    'createTime': null,
    'updateBy': null,
    'updateTime': null,
    'delFlag': '0',
    'remark': null,
    'detail': '工作流看板页面',
    'isExternal': '0',
    'menuList': []
  }
]
