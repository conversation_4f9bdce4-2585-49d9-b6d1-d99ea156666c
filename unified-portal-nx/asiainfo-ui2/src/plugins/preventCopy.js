/**
 * 防复制Vue插件
 * 提供全局防复制功能和组件级别的防复制控制
 */

import preventCopy from '@/utils/preventCopy'
import preventCopyDirective from '@/directive/preventCopy'

const PreventCopyPlugin = {
  install(app, options = {}) {
    // 默认配置
    const defaultOptions = {
      // 是否全局启用防复制
      globalEnable: false,
      // 是否在生产环境自动启用
      autoEnableInProduction: true,
      // 自定义提示消息
      warningMessage: '内容受保护，禁止复制！',
      // 是否显示警告消息
      showWarning: true
    }

    const config = { ...defaultOptions, ...options }

    // 注册防复制指令
    app.directive('prevent-copy', preventCopyDirective)

    // 在Vue全局属性上添加防复制方法
    app.config.globalProperties.$preventCopy = {
      // 启用全局防复制
      enable() {
        preventCopy.enable()
        if (config.showWarning) {
          console.warn(config.warningMessage)
        }
      },

      // 禁用全局防复制
      disable() {
        preventCopy.disable()
      },

      // 切换防复制状态
      toggle() {
        if (preventCopy.isPreventCopyEnabled()) {
          preventCopy.disable()
        } else {
          preventCopy.enable()
          if (config.showWarning) {
            console.warn(config.warningMessage)
          }
        }
      },

      // 检查是否启用
      isEnabled() {
        return preventCopy.isPreventCopyEnabled()
      },

      // 获取配置
      getConfig() {
        return config
      }
    }

    // 全局混入，为所有组件添加防复制相关的生命周期钩子
    app.mixin({
      created() {
        // 如果组件设置了preventCopy选项，则启用防复制
        if (this.$options.preventCopy === true) {
          this.$nextTick(() => {
            if (this.$el) {
              this.$el.setAttribute('v-prevent-copy', 'true')
              // Vue 3 中指令的钩子名称发生了变化
              if (preventCopyDirective.mounted) {
                preventCopyDirective.mounted(this.$el, { value: true })
              } else if (preventCopyDirective.inserted) {
                preventCopyDirective.inserted(this.$el, { value: true })
              }
            }
          })
        }
      },

      beforeUnmount() {
        // 组件销毁时清理防复制功能
        if (this.$options.preventCopy === true && this.$el) {
          if (preventCopyDirective.beforeUnmount) {
            preventCopyDirective.beforeUnmount(this.$el)
          } else if (preventCopyDirective.unbind) {
            preventCopyDirective.unbind(this.$el)
          }
        }
      }
    })

    // 根据配置自动启用防复制
    setTimeout(() => {
      if (config.globalEnable ||
          (config.autoEnableInProduction && process.env.NODE_ENV === 'production')) {
        preventCopy.enable()
        if (config.showWarning) {
          console.warn(config.warningMessage)
        }
      }
    }, 0)

    // 添加全局CSS样式
    addGlobalStyles()
  }
}

/**
 * 添加全局CSS样式
 */
function addGlobalStyles() {
  if (document.getElementById('prevent-copy-global-styles')) return

  const style = document.createElement('style')
  style.id = 'prevent-copy-global-styles'
  style.innerHTML = `
    /* 防复制全局样式 */
    .prevent-copy {
      -webkit-user-select: none !important;
      -moz-user-select: none !important;
      -ms-user-select: none !important;
      user-select: none !important;
      -webkit-touch-callout: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    .prevent-copy img {
      -webkit-user-drag: none !important;
      -khtml-user-drag: none !important;
      -moz-user-drag: none !important;
      -o-user-drag: none !important;
      user-drag: none !important;
      pointer-events: none !important;
    }

    .prevent-copy a {
      -webkit-user-drag: none !important;
      -khtml-user-drag: none !important;
      -moz-user-drag: none !important;
      -o-user-drag: none !important;
      user-drag: none !important;
    }

    /* 输入框和可编辑区域例外 */
    .prevent-copy input,
    .prevent-copy textarea,
    .prevent-copy [contenteditable="true"] {
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      user-select: text !important;
      pointer-events: auto !important;
    }

    /* 防复制提示样式 */
    .prevent-copy-warning {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      z-index: 9999;
      font-size: 14px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .prevent-copy-warning.show {
      opacity: 1;
    }
  `
  document.head.appendChild(style)
}

export default PreventCopyPlugin
