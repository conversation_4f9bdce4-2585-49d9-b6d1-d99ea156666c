import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}
export function getNameInCookie() {
  return Cookies.get('loginName')
}

export function setToken(token) {
  console.log('author setToken', Token<PERSON><PERSON>, token)
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(Token<PERSON><PERSON>)
}
