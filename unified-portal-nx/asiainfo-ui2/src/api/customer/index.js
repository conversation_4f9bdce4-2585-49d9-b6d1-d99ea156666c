import request from '@/utils/request-dy'
import request2 from '@/utils/request'

export function csmcustomerlable() {
  return request({
    url: '/portal/csmcustomerlable/page',
    method: 'get'
  })
}

export function csmcustomerbaseinfo(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  // 配合后端接口改造
  return request({
    url: '/portal/csmcustomerbaseinfo/page',
    method: 'post',
    data
  })
}

export function delId(id) {
  return request({
    url: `/portal/csmcustomerbaseinfo/${id}`,
    method: 'delete'
  })
}

export function getByPhoneNo(query) {
  return request({
    url: '/portal/csmcustomerbaseinfo/getByPhoneNo',
    method: 'get',
    params: query
  })
}

export function getAllGrides() {
  return request({
    url: `/portal/csmcustomerbaseinfo/getAllGrides`,
    method: 'get'
  })
}


// 创建客群

export function createCustomer(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmcustomerbaseinfo/createCustomer`,
    method: 'post',
    data
  })
}

export function csmcustomer(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmcustomer/page`,
    method: 'post',
    data
  })
}

export function csmcustomerById(id) {
  return request({
    url: `/portal/csmcustomer/${id}`,
    method: 'get'

  })
}

export function delCsmcustomerById(id) {
  return request({
    url: `/portal/csmcustomer/${id}`,
    method: 'delete'
  })
}

export function removeById(id) {
  return request({
    url: `/zb/group/${id}`,
    method: 'delete'
  })
}


export function getTemplate(id) {
  return request({
    url: `/portal/csmcustomer/getTemplate`,
    method: 'get'

  })
}

export function createCsmcustomer(id) {
  return request({
    url: `/portal/csmcustomer`,
    method: 'post'

  })
}

export function csmsendmessagehistory(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmsendmessagehistory/page`,
    method: 'post',
    data
  })
}

export function getRepairResultById(id) {
  return request({
    url: `portal/csmcustomer/getRepairResultById/${id}`,
    method: 'get'
  })
}

export function getBusinessPeopleRepairRank(id) {
  return request({
    url: `/portal/csmcustomer/getBusinessPeopleRepairRank/${id}`,
    method: 'get'
  })
}

export function getReasonForNoContact(id) {
  return request({
    url: `/portal/csmcustomer/getReasonForNoContact/${id}`,
    method: 'get'
  })
}

export function pushCode(code) {
  return request({
    url: `/portal/csmTextMessaging/push/${code}`,
    method: 'get'
  })
}

export function getReasonForContact(id) {
  return request({
    url: `/portal/csmcustomer/getReasonForContact/${id}`,
    method: 'get'
  })
}

export function getCustomerDetails(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmcustomer/getCustomerDetails`,
    method: 'post',
    data
  })
}

export function csmmessagetemplate(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmmessagetemplate/page`,
    method: 'post',
    data
  })
}

export function csmmessagetemplateAdd(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmmessagetemplate`,
    method: 'post',
    data
  })
}

export function csmmessagetemplateDel(id) {
  return request({
    url: `/portal/csmmessagetemplate/${id}`,
    method: 'delete'
  })
}

export function csmmessagetemplateGet(id) {
  return request({
    url: `/portal/csmmessagetemplate/${id}`,
    method: 'get'
  })
}

export function csmmessagetemplateEdit(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmmessagetemplate`,
    method: 'put',
    data
  })
}

export function labelLists() {
  return request({
    url: `/portal/csmcustomerlable/lists`,
    method: 'get'
  })
}

export function getColumNames() {
  return request({
    url: `/portal/csmcustomerlablecolum/getColumNames`,
    method: 'get'
  })
}

export function getColumScore() {
  return request({
    url: `/portal/csmcustomerlablecolum/getColumScore`,
    method: 'get'
  })
}

export function exportDoc(data = {}) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmcustomer/exportCustomerTableInfo/${data.code}`,
    method: 'post',
    responseType: 'blob'

  })
}

export function querySimpleMessageInfo(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmTextMessaging/querySimpleMessageInfo`,
    method: 'post',
    data
  })
}

export function queryMultiMessageInfo(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/portal/csmTextMessaging/queryMultiMessageInfo`,
    method: 'post',
    data
  })
}

export function exportCareCustomerPhoneList(data = {}) {
  return request({
    url: `/portal/csmTextMessaging/exportCareCustomerPhoneList?code=${data.code}`,
    method: 'post',
    responseType: 'blob'

  })
}

export function queryReinsuranceCusInfos(data) {
  return request({
    url: `/provinceReinsurance/queryReinsuranceCusInfos`,
    method: 'post',
    data
  })
}

export function deleteCsmSingle(data) {
  return request({
    url: `/provinceReinsurance/deleteCsmSingle/${data.id}`,
    method: 'get'
  })
}

export function viewSingleCustomerList(data) {
  return request({
    url: `/provinceReinsurance/viewSingleCustomerList`,
    method: 'post',
    data
  })
}

export function queryCustomerPageList(data) {
  return request({
    url: `/provinceReinsurance/queryCustomerPageList`,
    method: 'post',
    data
  })
}

export function createProvinceCustomer(data) {
  return request2({
    url: `/provinceReinsurance/createProvinceCustomer`,
    method: 'post',
    data
  })
}

export function deleteCsmMulti(data) {
  return request({
    url: `/provinceReinsurance/deleteCsmMulti/${data.id}`,
    method: 'post',
    data
  })
}
// 下载客群
export function downloadCustomerInfo(data) {
  return request({
    url: `/provinceReinsurance/downloadCustomerInfo/${data.code}`,
    method: 'post',
    data
  })
}

export function getCustomersAttribute(data) {
  return request({
    url: `/provinceReinsurance/getCustomersAttribute`,
    method: 'post',
    data
  })
}

export function getFieldEnumImp(fieldName) {
  return request({
    url: `/provinceReinsurance/getFieldEnum/${fieldName}`,
    method: 'post',
    data: {}
  })
}

export function zbPage(data={}) {
  return request({
    url: `/zb/customer/page`,
    method: 'post',
    data: data
  })
}

export function zbgetByPhoneNo(query) {
  return request({
    url: `/zb/customer/getByPhoneNo`,
    method: 'get',
    params: query
  })
}




export function zbcreateCustomer(data) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/zb/customer/createCustomer`,
    method: 'post',
    data
  })
}


export function zbGroupPage(data) {
  return request({
    url: `/zb/group/page`,
    method: 'post',
    data
  })
}

// /zb/group/exportCustomerTableInfo


export function zbexportDoc(data = {}) {
  data.areaId = data.cityId || data.countyId || data.gridId
  return request({
    url: `/zb/group/exportCustomerTableInfo/${data.code}`,
    method: 'post',
    responseType: 'blob'

  })
}


export function getCustomerByCode(query) {
  return request({
    url: `/zb/group/getCustomerByCode/${query.code}`,
    method: 'get',
    params: {}
  })
}

export function listCustomerCountByType(query) {
  return request({
    url: `/zb/group/listCustomerCountByType/${query.code}/${query.type}`,
    method: 'get',
    params: {}
  })
}

export function listCustomerHistogramByCode(query) {
  return request({
    url: `/zb/group/listCustomerHistogramByCode/${query.code}`,
    method: 'get',
    params: {}
  })
}


export function getLabels(query) {
  return request({
    url: `/zb/customer/getLabels`,
    method: 'get',
    params: {}
  })
}


export function pushCustomer(data) {
  return request({
    url: `/zb/group/push`,
    method: 'post',
    data
  })
}

export function getCustomerBaseDetailByPhoneNum(query) {
  return request({
    url: `/zb/customer/getCustomerBaseDetailByPhoneNum/${query.phoneNum}`,
    method: 'get',
    params: {}
  })
}

export function listCustomerDateDetailByPhoneNum(query) {
  return request({
    url: `/zb/customer/listCustomerDateDetailByPhoneNum/${query.phoneNum}`,
    method: 'get',
    params: {}
  })
}

export function getCustomerDateInfoByPhoneNum(query) {
  return request({
    url: `/zb/customer/getCustomerDateInfoByPhoneNum/${query.phoneNum}`,
    method: 'get',
    params: {}
  })
}




export function queryByParamPage(data) {
  return request({
    url: `/QuestionnaireReadController/queryByParamPage`,
    method: 'post',
    data
  })
}

// 评测结果分析-查询问题列表
export function queryQuestionnaireInfo(data) {
  return request({
    url: `/QuestionnaireReadController/queryQuestionnaireInfo`,
    method: 'post',
    data
  });
}
// 评测结果分析-默认报表答案统计
export function queryAnalysisQuestionFault(data) {
  return request({
    url: `/CsmAnalysisCycleController/queryAnalysisQuestionFault`,
    method: 'post',
    data
  });
}


export function getQuestionIdByCode(query) {
  return request({
    url: `/CsmAnalysisCycleController/getQuestionIdByCode?code=${query.code}`,
    method: 'get',
    params: {}
  })
}


// 获取待办列表数据
export function getLatestStatDate(params) {
  return request({
    url: '/queryStatDateCommon/getLatestStatDate',
    method: 'get',
    params
  })
}
// 问卷明细下载
export function exportDetailExcel(data) {
  return request({
    url: `/zb/group/getCustomerExcel?startTime=${data.startTime}&endTime=${data.endTime}&code=${data.code}`,
    method: 'get',
    responseType: 'blob'

  })
}
// 获取是否调研
export function queryCustomType(data) {
  return request({
    url: `/zb/group/isSurvey/${data}`,
    method: 'get',
  })
}
// 获取调研明细
export function getCustomerByCodeV1(data) {
  return request({
    url: `/zb/group/getCustomerByCodeV1/${data}`,
    method: 'get',
  })
}
// /CsmAnalysisCycleController/getQuestionIdByCode
// get请求
// 参数 code

// 轨迹旅程
export function getTrack(data) {
  return request({
    url: `/travel/getUserMsg`,
    method: 'post',
    data:data
  })
}









