import router from './router'
import store from './store'
import {ElMessage as Message} from 'element-plus'
// import NProgress from 'nprogress'
// import 'nprogress/nprogress.css'
import {getToken} from '@/utils/auth'

// NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/bind', '/register']

router.beforeEach((to, from, next) => {
  console.log(1)
  // NProgress.start()
  if (getToken()) {
    console.log(2)
    /* has token*/
    if (to.path === '/login') {
      console.log(3)
      next({ path: '/' })
      // NProgress.done()
    } else {
      console.log(4)
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        console.log(5)
        store.dispatch('GetInfo').then(res => {
          // 拉取user_info
          console.log(6)
          const roles = res.roles
          store.dispatch('GenerateRoutes', { roles }).then(accessRoutes => {
          // 测试 默认静态页面
          // store.dispatch('permission/generateRoutes', { roles }).then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            // Vue Router 4 中使用 addRoute 替代 addRoutes
            accessRoutes.forEach(route => {
              router.addRoute(route)
            })
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        })
        .catch(err => {
          store.dispatch('FedLogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        next()
        // 没有动态改变权限的需求可直接next() 删除下方权限判断 ↓
        // if (hasPermission(store.getters.roles, to.meta.roles)) {
        //   next()
        // } else {
        //   next({ path: '/401', replace: true, query: { noGoBack: true }})
        // }
        // 可删 ↑
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      // NProgress.done()
      next()
    }
  }
})

router.afterEach(() => {
  // NProgress.done()
})
