<template>
  <div id="app" v-prevent-copy>
    <router-view v-slot="{ Component, route }">
      <keep-alive>
        <component :is="Component" v-if="route.meta.keepAlive" />
      </keep-alive>
      <component :is="Component" v-if="!route.meta.keepAlive" />
    </router-view>
  </div>
</template>

<script>

export default {
  name: 'App',
  data() {
    return {

    }
  }
}
</script>

<style >
 #app{
      font-family: 'syht', "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
 }
</style>
