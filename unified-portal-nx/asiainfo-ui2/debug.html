<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Page</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Vue3 升级调试页面</h1>
    
    <div id="status-container">
        <div class="status info">正在检测页面状态...</div>
    </div>
    
    <div id="main-app-container">
        <h2>主应用测试</h2>
        <iframe src="/" width="100%" height="600" style="border: 1px solid #ccc;"></iframe>
    </div>
    
    <script>
        console.log('Debug page loaded');
        
        // 检测主页面状态
        function checkMainPage() {
            const statusContainer = document.getElementById('status-container');
            
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        statusContainer.innerHTML = '<div class="status success">✅ 主页面响应正常 (HTTP ' + response.status + ')</div>';
                        return response.text();
                    } else {
                        throw new Error('HTTP ' + response.status);
                    }
                })
                .then(html => {
                    if (html.includes('<div id="app">')) {
                        statusContainer.innerHTML += '<div class="status success">✅ HTML结构正常，包含Vue应用容器</div>';
                    } else {
                        statusContainer.innerHTML += '<div class="status error">❌ HTML结构异常，缺少Vue应用容器</div>';
                    }
                    
                    if (html.includes('src="/src/main.js"')) {
                        statusContainer.innerHTML += '<div class="status success">✅ main.js引用正常</div>';
                    } else {
                        statusContainer.innerHTML += '<div class="status error">❌ main.js引用异常</div>';
                    }
                })
                .catch(error => {
                    statusContainer.innerHTML = '<div class="status error">❌ 主页面访问失败: ' + error.message + '</div>';
                });
        }
        
        // 页面加载后检测
        setTimeout(checkMainPage, 1000);
        
        // 每5秒检测一次
        setInterval(checkMainPage, 5000);
    </script>
</body>
</html>
