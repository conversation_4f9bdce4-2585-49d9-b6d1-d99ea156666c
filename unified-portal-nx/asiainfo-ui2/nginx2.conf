#user  root;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server_tokens off;

    #8080 宁夏大音门户 此端口承载网能访问
    server {
        listen 1443 ssl;
        server_name **************;
        ssl_certificate      ../nxhttps.crt;
        ssl_certificate_key  ../nxhttps.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        # 禁用RSA密钥交换，只允许支持前向保密的密码套件
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384;
        ssl_prefer_server_ciphers on;
        
        # 启用ECDH临时密钥
        ssl_ecdh_curve secp384r1;
        
        # SSL会话配置
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 禁用SSL会话票据以增强安全性
        ssl_session_tickets off;
        location / {
            root /home/<USER>/shanghuichao/nginx2-1.24.0/html/dist;
            index index.html index.htm;
        }

        error_page  404 /404.html;
        location = /404.html {
            root /home/<USER>/shanghuichao/nginx2-1.24.0/html/;
        }

        location /nxportal {
            client_max_body_size 500m;
            client_body_buffer_size 500m; # 超过这个配置大小的请求，会先放临时文件，这里调大它，不走临时文件（避免普通用户没有目录访问权限问题）
            proxy_buffering off; # 关闭代理缓冲区，nginx会立即把从后端收到的响应内容传送给客户端，每次取的大小为proxy_buffer_size的大小
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+nxportal/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://10.187.238.227:8085;
            proxy_buffer_size 4096k;
            proxy_buffers 16 4096k;
            proxy_busy_buffers_size 4096k;
            proxy_temp_file_write_size 4096k;
        }
        ## 宁夏大音门户-test一个反向代理
        location /portal/login/getFromOaLogin {
            include  uwsgi_params;
            client_max_body_size 20m;
            proxy_pass   http://10.187.238.227:8085/portal/login/getFromOaLogin;
        }

        #工作流看板页面
        location /apiWorkBoard {
            client_max_body_size 500m;
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            rewrite ^.+apiWorkBoard/?(.*)$ /$1 break;
            include uwsgi_params;
            proxy_pass http://10.187.238.227:10101;
        }

        #地图服务转发
        location /Grid {
            proxy_pass http://10.254.123.75:7001;
        }
        location /grid {
            proxy_pass http://10.254.123.75:7001;
        }
    }

}